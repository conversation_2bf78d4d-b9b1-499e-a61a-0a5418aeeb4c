::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
::-webkit-scrollbar-thumb {
  border-radius: 1em;
  background-color: rgba(50,50,50,0.3);
}
::-webkit-scrollbar-track {
  border-radius: 1em;
  background-color: rgba(50,50,50,0.1);
}
.fc-style .ant-radio-wrapper {
  margin: 8px 8px 0 8px;
}
.fc-style .ant-checkbox-wrapper {
  margin: 8px 8px 0 8px;
}
.fc-style .ant-time-picker {
  min-width: 200px;
}
.fc-style .ant-form-item-control-wrapper {
  flex: 1;
}
.fc-style .el-main {
  padding: 0;
}
.fc-style .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}
.fc-style .el-dialog__body {
  padding: 15px 20px;
}
.fc-style .el-dialog__footer {
  border-top: 1px solid #f2f6fc;
  padding: 10px 15px;
}
.fc-style .el-radio-group {
  margin-top: 10px;
  vertical-align: super;
}
.fc-style .el-radio {
  margin-bottom: 10px;
}
.fc-style .el-rate {
  margin-top: 6px;
}

.fc-style {
  height: 100%;
}
.fc-style *,
.fc-style :after,
.fc-style :before {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.fc-style .fc-container {
  background: #fff;
  height: 100%;
}
.fc-style .fc-container .ant-layout,
.fc-style .fc-container .el-container {
  height: 100% !important;
}
.fc-style .fc-container>.ant-layout,
.fc-style .fc-container .el-container {
  background: #fff;
}
.fc-style .fc-container .fc-main {
  position: relative;
}
.fc-style .fc-container .fc-main>.ant-layout,
.fc-style .fc-container .fc-main .el-container {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.fc-style .center-container {
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
}
.fc-style .center-container .btn-bar {
  height: 45px !important;
  line-height: 45px;
  font-size: 18px;
  border-bottom: 2px solid #e4e7ed;
  text-align: right;
  background-color: #fff;
  padding: 0 10px;
}
.fc-style .center-container .btn-bar .svg-icon {
  font-size: 16px;
  margin-right: 5px;
}
.fc-style .center-container .ant-layout-content,
.fc-style .center-container .el-main {
  height: calc(100% - 45px);
  padding: 0;
  position: relative;
  background: #fafafa;
}
.fc-style .components {
  padding: 8px 0;
  width: 100%;
  height: 100%;
}
.fc-style .components .widget-cate {
  padding: 8px 12px;
  font-size: 13px;
}
.fc-style .components ul {
  position: relative;
  overflow: hidden;
  padding: 0 10px 10px;
  margin: 0;
}
.fc-style .components .form-edit-widget-label {
  font-size: 12px;
  display: block;
  width: 48%;
  /* line-height: 26px; */
  line-height: 40px;
  position: relative;
  float: left;
  left: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 1%;
  border: 1px solid #f4f6fc;
}
.fc-style .components .form-edit-widget-label:hover {
  border: 1px dashed #409eff;
}
.fc-style .components .form-edit-widget-label:hover>a {
  color: #409eff;
}
.fc-style .components .form-edit-widget-label>a {
  color: #333;
  /* display: block; */
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: move;
  background: #f4f6fc;
  border: 1px solid #f3f9ff;
}
/* .fc-style .components .form-edit-widget-label>a .svg-icon {
  margin-right: 6px;
  margin-left: 8px;
  font-size: 14px;
  display: inline-block;
  vertical-align: middle;
} */
.fc-style .components .form-edit-widget-label>a span {
  /* display: inline-block;
  vertical-align: middle; */
  margin-left: 10px;
}
.fc-style .widget-form-container {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 300px;
  bottom: 0;
  width: 100%;
}
.fc-style .widget-form-container .widget-form-list {
  background: #fff;
  border: 1px dashed #999;
  min-height: calc(100vh - 65px);
  margin: 10px;
}
.fc-style .widget-form-container .widget-form-list .widget-item-container {
  position: relative;
}
.fc-style .widget-form-container .widget-form-list .widget-item-container .widget-view-action {
  position: absolute;
  right: 0;
  bottom: -2px;
  height: 28px;
  line-height: 28px;
  background: #409eff;
  z-index: 10;
}
.fc-style .widget-form-container .widget-form-list .widget-item-container .widget-view-action .svg-icon {
  font-size: 14px;
  color: #fff;
  margin: 0 5px;
  cursor: pointer;
}
.fc-style .widget-form-container .widget-form-list .widget-item-container .widget-view-drag {
  position: absolute;
  height: 28px;
  left: 0;
  top: -2px;
  line-height: 28px;
  background: #409eff;
  z-index: 10;
}
.fc-style .widget-form-container .widget-form-list .widget-item-container .widget-view-drag .svg-icon {
  font-size: 14px;
  color: #fff;
  margin: 0 5px;
  cursor: move;
}
.fc-style .widget-form-container .widget-form-list .widget-col-list {
  min-height: 100px;
  border: 1px dashed #ccc;
  background: #fff;
}
.fc-style .widget-form-container .widget-form-list .widget-view {
  padding-bottom: 18px;
  position: relative;
  border: 1px dashed rgba(170,170,170,0.7);
  background-color: rgba(236,245,255,0.3);
  margin: 2px;
}
.fc-style .widget-form-container .widget-form-list .widget-view .widget-view-description {
  height: 15px;
  line-height: 15px;
  font-size: 13px;
  margin-top: 6px;
  color: #909399;
}
.fc-style .widget-form-container .widget-form-list .widget-view:after {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: block;
}
.fc-style .widget-form-container .widget-form-list .widget-view:hover {
  background: #ecf5ff;
  outline: 1px solid #409eff;
  outline-offset: 0;
}
.fc-style .widget-form-container .widget-form-list .widget-view:hover.active {
  outline: 2px solid #409eff;
  border: 1px solid #409eff;
  outline-offset: 0;
}
.fc-style .widget-form-container .widget-form-list .widget-view:hover .widget-view-drag {
  display: block;
}
.fc-style .widget-form-container .widget-form-list .widget-view.active {
  outline: 2px solid #409eff;
  border: 1px solid #409eff;
}
.fc-style .widget-form-container .widget-form-list .widget-view.ghost {
  background: #f56c6c;
  border: 2px solid #f56c6c;
  outline-width: 0;
  height: 3px;
  box-sizing: border-box;
  font-size: 0;
  content: '';
  overflow: hidden;
  padding: 0;
}
.fc-style .widget-form-container .widget-form-list .widget-col {
  padding: 5px;
  background-color: rgba(253,246,236,0.3);
}
.fc-style .widget-form-container .widget-form-list .widget-col.active {
  outline: 2px solid #e6a23c;
  border: 1px solid #e6a23c;
}
.fc-style .widget-form-container .widget-form-list .widget-col:hover {
  background: #fdf6ec;
  outline: 1px solid #e6a23c;
  outline-offset: 0px;
}
.fc-style .widget-form-container .widget-form-list .widget-col:hover.active {
  outline: 2px solid #e6a23c;
  border: 1px solid #e6a23c;
  outline-offset: 0;
}
.fc-style .widget-form-container .widget-form-list .widget-col.ghost {
  background: #f56c6c;
  border: 2px solid #f56c6c;
  outline-width: 0;
  height: 3px;
  box-sizing: border-box;
  font-size: 0;
  content: '';
  overflow: hidden;
  padding: 0;
}
.fc-style .widget-form-container .widget-form-list .widget-col .widget-view-action.widget-col-action {
  position: absolute;
  height: 28px;
  right: -2px;
  bottom: -2px;
  line-height: 28px;
  background: #e6a23c;
  z-index: 10;
}
.fc-style .widget-form-container .widget-form-list .widget-col .widget-view-action.widget-col-action .svg-icon {
  font-size: 14px;
  color: #fff;
  margin: 0 5px;
  cursor: move;
}
.fc-style .widget-form-container .widget-form-list .widget-col .widget-view-drag.widget-col-drag {
  position: absolute;
  height: 28px;
  left: -2px;
  top: -2px;
  line-height: 28px;
  background: #e6a23c;
  z-index: 10;
}
.fc-style .widget-form-container .widget-form-list .widget-col .widget-view-drag.widget-col-drag .svg-icon {
  font-size: 14px;
  color: #fff;
  margin: 0 5px;
  cursor: move;
}
.fc-style .widget-form-container .widget-form-list .widget-col::after {
  display: none;
}
.fc-style .widget-form-container .widget-form-list .ghost {
  background: #f56c6c;
  border: 2px solid #f56c6c;
  outline-width: 0;
  height: 3px;
  box-sizing: border-box;
  font-size: 0;
  content: '';
  overflow: hidden;
  padding: 0;
}
.fc-style .widget-form-container .ghost {
  background: #f56c6c;
  border: 2px solid #f56c6c;
  position: relative;
}
.fc-style .widget-form-container .ghost::after {
  background: #f56c6c;
}
.fc-style .widget-form-container li.ghost {
  height: 5px;
  list-style: none;
  font-size: 0;
  overflow: hidden;
}
.fc-style .widget-config-container {
  position: relative;
}
.fc-style .widget-config-container .ant-layout-header,
.fc-style .widget-config-container .el-header {
  border-bottom: 2px solid #e4e7ed;
  padding: 0 5px;
  height: 45px !important;
  line-height: 45px !important;
  background-color: #fff;
}
.fc-style .widget-config-container .config-tab {
  height: 41px;
  line-height: 41px;
  display: inline-block;
  width: 145px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  cursor: pointer;
}
.fc-style .widget-config-container .config-tab.active {
  border-bottom: 2px solid #409eff;
}
.fc-style .widget-config-container .config-content {
  padding: 10px;
  background: #fff;
  overflow: auto;
}
.fc-style .widget-config-container .config-content .ant-form-item-label,
.fc-style .widget-config-container .config-content .el-form-item__label {
  font-weight: 500;
}
.fc-style .widget-config-container .config-content .el-form-item__label {
  padding: 0;
}
.fc-style .widget-config-container .config-content .ant-form-item,
.fc-style .widget-config-container .config-content .el-form-item,
.fc-style .widget-config-container .config-content h4 {
  padding-bottom: 10px;
  border-bottom: 1px solid #e1e1e1;
}
.fc-style .widget-config-container .config-content .label {
  font-size: 14px;
  font-weight: 500;
  margin: 0 5px;
}
.fc-style .widget-config-container .config-content .label:first-child {
  margin-left: 0;
}
.fc-style .widget-config-container .config-content .drag-item {
  font-size: 16px;
  margin: 0 5px;
  cursor: move;
}
.fc-style .widget-config-container .ghost {
  background: #fff;
  border: 1px dashed #409eff;
}
.fc-style .widget-config-container .ghost::after {
  background: #fff;
  display: block;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.fc-style .widget-config-container ul {
  margin: 0;
  padding: 0;
}
.fc-style .widget-config-container li.ghost {
  list-style: none;
  font-size: 0;
  display: block;
  position: relative;
}
.fc-style .form-empty {
  position: absolute;
  text-align: center;
  width: 300px;
  font-size: 20px;
  top: 200px;
  left: 50%;
  margin-left: -150px;
  color: #ccc;
}
.fc-style .widget-empty {
  background-position: 50%;
}



.ant-layout-sider{
  z-index: 99;
}
.center-container
{
  height:calc(100% - 76px)!important;
  overflow: hidden;
}
.fc-style{height:100%;}
.ant-layout{height:100%!important;}