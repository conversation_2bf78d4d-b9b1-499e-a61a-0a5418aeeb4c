import { camelCaseString } from '../event/design';
import { buildAppFormProps } from './designHelper';
import { ComponentOptionModel, FormJson } from '/@/model/generator/codeGenerator';

export const buildAppSchemeStrBySchema = (json: FormJson, convertCamel = true) => {
  const props = buildAppFormProps(json, convertCamel);
  return props;
};

export const buildAppSchemeStr = (list: ComponentOptionModel[], convertCamel: boolean): string => {
  const code = `${list.map((item) => {
    if (item.type === 'divider') {
      return `{
        key: '${item.key}',
        field: ${convertCamel ? camelCaseString(item.bindField) : item.bindField}
        name: '${item.label}',
        component: componentType.divider,
        componentProps: {
          text: '${item.options!.defaultValue}',
          align: '${item.options!.orientation}',
        },
      }`;
    }
    //如果是栅格布局组件 手机端没有  必须要扁平化
    else if (item.type === 'grid') {
      let gridStr = ``;
      item.layout?.map((el) => {
        gridStr += buildAppSchemeStr(el.list, convertCamel);
      });

      return gridStr;
    } //如果是tab组件 转换为手机端配置
    else if (item.type === 'tab') {
      return `{
        key: '${item.key}',
        name: '${item.label}',
        component: componentType.segmented,
        layout: [${item.layout?.map((el, index) => {
          return `{
            name: '${el.name}',
            value: '${index}',
            children: [${buildAppSchemeStr(el.list, convertCamel)}],
          }`;
        })}],
      }`;
    } else if (item.type === 'card') {
      return `{
        key: '${item.key}',
        name: '${item.label}',
        component: componentType.collapse,
        layout: [${item.layout?.map((el, index) => {
          return `{
            name: '${el.name}',
            value: '${index}',
            children: [${buildAppSchemeStr(el.list, convertCamel)}],
          }`;
        })}],
      }`;
    } else if (item.type === 'subForm') {
      return `{
        field: '${item.bindField}', //字段
        key: '${item.key}',
        name: '${item.label}',
        component: componentType.subForm,
        columns: [${buildAppSchemeStr(item.children!, convertCamel)}],
      }`;
    } else {
      return buildAppDefaultSchema(item, convertCamel);
    }
  })}`;
  return code;
};

export function buildAppDefaultSchema(model: ComponentOptionModel, convertCamel: boolean): string {
  const compType = buildAppComponentTypeStr(model.type);
  const schema = `{
    key: '${model.key}',
    field: '${(convertCamel ? camelCaseString(model.bindField)! : model.bindField) || ''}',
    label: '${model.label}',
    component: '${compType}',
    defaultValue: '${model.options?.defaultValue}',
    componentProps: {
      placeholder: '请输入${model.label}',
    },
    name: '${model.label}',
  }`;
  return schema;
}

export function buildAppComponentTypeStr(type: string): string {
  switch (type) {
    case 'input':
      return 'componentType.input';

    case 'password':
      return 'componentType.input';

    case 'textarea':
      return 'componentType.input';

    case 'number':
      return 'componentType.inputNumber';

    case 'radio':
      return 'componentType.radio';

    case 'checkbox':
      return 'componentType.checkbox';

    case 'select':
      return 'componentType.select';

    case 'cascader':
      return 'componentType.select';

    case 'time':
      return 'componentType.dateTime';
    case 'date':
      return 'componentType.dateTime';

    case 'time-range':
      return 'componentType.dateRange';

    case 'date-range':
      return 'componentType.dateRange';

    case 'rate':
      return 'componentType.inputNumber';

    case 'switch':
      return 'componentType.switch';

    case 'slider':
      return 'componentType.inputNumber';

    case 'divider':
      return 'componentType.divider';

    case 'upload':
      return 'componentType.input';

    case 'richtext-editor':
      return 'componentType.input';

    case 'form':
      return 'componentType.subForm';
    case 'one-for-one':
      return 'componentType.subForm';

    default:
      return 'componentType.input';
  }
}
