<template>
  <div class="workflow-chart-container" :key="componentKey">
    <!-- <div class="chart-header">
      <h3>流程图</h3>
      <div class="chart-controls">
        <a-button size="small" @click="zoomIn">放大</a-button>
        <a-button size="small" @click="zoomOut">缩小</a-button>
        <a-button size="small" @click="resetZoom">重置</a-button>
      </div>
    </div> -->
    <div ref="chartContainer" class="chart-container" :key="`chart-${componentKey}`"></div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
  import { Graph, Shape } from '@antv/x6';

  // 定义流程节点数据
  const workflowData = [
    { id: 'start', label: '开始', type: 'start', x: 400, y: 50 },
    { id: 'apply', label: '填写申请单', type: 'process', x: 400, y: 150 },
    { id: 'province-manager', label: '省公司经理审核', type: 'process', x: 400, y: 250 },
    { id: 'operation-dept', label: '运营部审核', type: 'process', x: 400, y: 350 },
    { id: 'operation-manager', label: '运营部经理复核', type: 'process', x: 400, y: 450 },
    { id: 'business-director', label: '事业部总监审批', type: 'process', x: 400, y: 550 },
    { id: 'vice-manager', label: '康盛副总经理审批', type: 'decision', x: 400, y: 650 },
    { id: 'leader-approval', label: '分管领导审批', type: 'process', x: 400, y: 750 },
    { id: 'customer-service', label: '客户服务部备案', type: 'process', x: 400, y: 850 },
    { id: 'feedback', label: '反馈发起人', type: 'end', x: 400, y: 950 },
  ];

  // 定义连接线数据
  const edgeData = [
    { source: 'start', target: 'apply' },
    { source: 'apply', target: 'province-manager' },
    { source: 'province-manager', target: 'operation-dept' },
    { source: 'operation-dept', target: 'operation-manager' },
    { source: 'operation-manager', target: 'business-director' },
    { source: 'business-director', target: 'vice-manager' },
    // 康盛副总经理审批主支到分管领导审批
    { source: 'vice-manager', target: 'leader-approval' },
    // 康盛副总经理审批分支直接到客户服务部备案
    {
      source: 'vice-manager',
      target: 'customer-service',
      label: '在政策范围内',
      type: 'condition',
      vertices: [
        { x: 550, y: 650 },
        { x: 550, y: 850 },
      ], // 添加转折点，向右再向下
    },
    // 分管领导审批到客户服务部备案
    { source: 'leader-approval', target: 'customer-service' },
    // 客户服务部备案到反馈发起人
    { source: 'customer-service', target: 'feedback' },
  ];

  const chartContainer = ref<HTMLDivElement>();
  const componentKey = ref(Date.now());
  let graph: Graph | null = null;

  // 生成唯一的节点名称避免重复注册
  const uniqueId = Date.now() + Math.random().toString(36).substring(2, 11);
  const startNodeName = `start-node-${uniqueId}`;
  const processNodeName = `process-node-${uniqueId}`;
  const endNodeName = `end-node-${uniqueId}`;
  const decisionNodeName = `decision-node-${uniqueId}`;

  // 注册自定义节点
  const registerCustomNodes = () => {
    try {
      // 开始节点
      Shape.Rect.define({
        shape: startNodeName,
        width: 120,
        height: 40,
        attrs: {
          body: {
            rx: 20,
            ry: 20,
            stroke: '#52c41a',
            fill: '#f6ffed',
            strokeWidth: 2,
            filter: {
              name: 'dropShadow',
              args: {
                dx: 2,
                dy: 2,
                blur: 4,
                color: 'rgba(0,0,0,0.1)',
              },
            },
          },
          text: {
            fontSize: 14,
            fill: '#52c41a',
            fontWeight: 'bold',
            textAnchor: 'middle',
            textVerticalAnchor: 'middle',
          },
        },
      });

      // 流程节点
      Shape.Rect.define({
        shape: processNodeName,
        width: 140,
        height: 50,
        attrs: {
          body: {
            rx: 8,
            ry: 8,
            stroke: '#1890ff',
            fill: '#e6f7ff',
            strokeWidth: 2,
            filter: {
              name: 'dropShadow',
              args: {
                dx: 2,
                dy: 2,
                blur: 4,
                color: 'rgba(0,0,0,0.1)',
              },
            },
          },
          text: {
            fontSize: 13,
            fill: '#1890ff',
            fontWeight: 'bold',
            textAnchor: 'middle',
            textVerticalAnchor: 'middle',
          },
        },
      });

      // 结束节点
      Shape.Rect.define({
        shape: endNodeName,
        width: 120,
        height: 40,
        attrs: {
          body: {
            rx: 20,
            ry: 20,
            stroke: '#ff4d4f',
            fill: '#fff2f0',
            strokeWidth: 2,
            filter: {
              name: 'dropShadow',
              args: {
                dx: 2,
                dy: 2,
                blur: 4,
                color: 'rgba(0,0,0,0.1)',
              },
            },
          },
          text: {
            fontSize: 14,
            fill: '#ff4d4f',
            fontWeight: 'bold',
            textAnchor: 'middle',
            textVerticalAnchor: 'middle',
          },
        },
      });

      // 决策节点（菱形）
      Shape.Polygon.define({
        shape: `decision-node-${uniqueId}`,
        width: 140,
        height: 60,
        points: '70,0 140,30 70,60 0,30',
        attrs: {
          body: {
            stroke: '#fa8c16',
            fill: '#fff7e6',
            strokeWidth: 2,
            filter: {
              name: 'dropShadow',
              args: {
                dx: 2,
                dy: 2,
                blur: 4,
                color: 'rgba(0,0,0,0.1)',
              },
            },
          },
          text: {
            fontSize: 13,
            fill: '#fa8c16',
            fontWeight: 'bold',
            textAnchor: 'middle',
            textVerticalAnchor: 'middle',
          },
        },
      });
    } catch (error) {
      console.warn('注册自定义节点时出错:', error);
    }
  };

  // 清理图表
  const destroyChart = () => {
    if (graph) {
      graph.dispose();
      graph = null;
    }
  };

  // 初始化图表
  const initChart = async () => {
    // 确保先清理之前的实例
    destroyChart();

    // 等待 DOM 更新
    await nextTick();

    if (!chartContainer.value) return;

    // 注册自定义节点
    registerCustomNodes();

    // 创建画布
    graph = new Graph({
      container: chartContainer.value,
      width: 900,
      height: 1000,
      background: {
        color: '#fafafa',
      },
      grid: {
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: '#eee',
            thickness: 1,
          },
          {
            color: '#ddd',
            thickness: 1,
            factor: 4,
          },
        ],
      },
      panning: {
        enabled: true,
        eventTypes: ['leftMouseDown', 'mouseWheel'],
      },
      mousewheel: {
        enabled: true,
        modifiers: 'ctrl',
        factor: 1.1,
        maxScale: 1.5,
        minScale: 0.5,
      },
      interacting: {
        nodeMovable: false,
        edgeMovable: false,
        edgeLabelMovable: false,
        arrowheadMovable: false,
        vertexMovable: false,
        vertexAddable: false,
        vertexDeletable: false,
      },
    });

    // 添加节点
    workflowData.forEach((node) => {
      let nodeShape: string;
      let nodeWidth = 70; // 默认宽度偏移

      if (node.type === 'start') {
        nodeShape = startNodeName;
        nodeWidth = 60;
      } else if (node.type === 'end') {
        nodeShape = endNodeName;
        nodeWidth = 60;
      } else if (node.type === 'decision') {
        nodeShape = decisionNodeName;
        nodeWidth = 70;
      } else {
        nodeShape = processNodeName;
        nodeWidth = 70;
      }

      graph!.addNode({
        id: node.id,
        shape: nodeShape,
        x: node.x - nodeWidth,
        y: node.y,
        label: node.label,
      });
    });

    // 添加连接线
    edgeData.forEach((edge) => {
      const edgeConfig: any = {
        source: edge.source,
        target: edge.target,
        attrs: {
          line: {
            stroke: edge.type === 'condition' ? '#ff7875' : '#1890ff',
            strokeWidth: edge.type === 'condition' ? 3 : 2,
            strokeDasharray: edge.type === 'condition' ? '5,5' : undefined,
            targetMarker: {
              name: 'classic',
              size: 8,
              fill: edge.type === 'condition' ? '#ff7875' : '#1890ff',
            },
          },
        },
        labels: edge.label
          ? [
              {
                attrs: {
                  text: {
                    text: edge.label,
                    fontSize: 12,
                    fill: edge.type === 'condition' ? '#ff4d4f' : '#666',
                    fontWeight: 'bold',
                  },
                  rect: {
                    fill: '#fff',
                    stroke: edge.type === 'condition' ? '#ff7875' : '#ddd',
                    strokeWidth: 1,
                    rx: 4,
                    ry: 4,
                  },
                },
              },
            ]
          : [],
      };

      // 如果有转折点，添加vertices
      if (edge.vertices) {
        edgeConfig.vertices = edge.vertices;
      }

      graph!.addEdge(edgeConfig);
    });

    // 添加鼠标悬停高亮效果
    graph.on('node:mouseenter', ({ node }) => {
      const nodeType = workflowData.find((item) => item.id === node.id)?.type;
      let highlightColor: string;
      let highlightFill: string;

      if (nodeType === 'start') {
        highlightColor = '#389e0d';
        highlightFill = '#d9f7be';
      } else if (nodeType === 'end') {
        highlightColor = '#cf1322';
        highlightFill = '#ffccc7';
      } else if (nodeType === 'decision') {
        highlightColor = '#d46b08';
        highlightFill = '#fff1b8';
      } else {
        highlightColor = '#0050b3';
        highlightFill = '#bae7ff';
      }

      node.attr('body/stroke', highlightColor);
      node.attr('body/fill', highlightFill);
      node.attr('body/strokeWidth', 3);
      node.attr('text/fill', highlightColor);
    });

    graph.on('node:mouseleave', ({ node }) => {
      const nodeType = workflowData.find((item) => item.id === node.id)?.type;
      let originalColor: string;
      let originalFill: string;

      if (nodeType === 'start') {
        originalColor = '#52c41a';
        originalFill = '#f6ffed';
      } else if (nodeType === 'end') {
        originalColor = '#ff4d4f';
        originalFill = '#fff2f0';
      } else if (nodeType === 'decision') {
        originalColor = '#fa8c16';
        originalFill = '#fff7e6';
      } else {
        originalColor = '#1890ff';
        originalFill = '#e6f7ff';
      }

      node.attr('body/stroke', originalColor);
      node.attr('body/fill', originalFill);
      node.attr('body/strokeWidth', 2);
      node.attr('text/fill', originalColor);
    });

    // 居中显示
    graph.centerContent();
  };

  // 缩放控制方法
  const zoomIn = () => {
    if (graph) {
      graph.zoom(0.1);
    }
  };

  const zoomOut = () => {
    if (graph) {
      graph.zoom(-0.1);
    }
  };

  const resetZoom = () => {
    if (graph) {
      graph.zoomToFit({ padding: 20 });
    }
  };

  // 重新初始化组件
  const reinitialize = () => {
    componentKey.value = Date.now();
    nextTick(() => {
      initChart();
    });
  };

  // 暴露方法给父组件
  defineExpose({
    reinitialize,
    zoomIn,
    zoomOut,
    resetZoom,
  });

  onMounted(() => {
    initChart();
  });

  onUnmounted(() => {
    destroyChart();
  });

  onBeforeUnmount(() => {
    destroyChart();
  });
</script>

<style lang="less" scoped>
  .workflow-chart-container {
    width: 100%;
    height: 100vh;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    background: #fafafa;
    display: flex;
    flex-direction: column;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fff;
      border-bottom: 1px solid #f0f0f0;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .chart-controls {
        display: flex;
        gap: 8px;
      }
    }

    .chart-container {
      flex: 1;
      width: 100%;
      background: #fafafa;
    }
  }
</style>
