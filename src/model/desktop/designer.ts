import { EChartsOption } from 'echarts';
import {
  AlignType,
  DashboardShowType,
  DesktopComponent,
  ImgType,
  LegendType,
  OrientType,
  ButtonType,
  TableType,
  ExecuteType,
} from '/@/enums/desktop';
import { ApiConfig } from '/@/components/ApiConfig/src/interface';
import { FormTypeEnum } from '/@/enums/formtypeEnum';

export type DesktopInfoId = string;
export type InfoType = DesktopComponent;

export interface DesktopData extends DesktopBasicData {
  jsonContent: string; ////桌面设计json
}
export interface DesktopBasicData {
  systemId?: string;
  id?: string;
  code: string; //编码
  name: string; //名称
  icon: string; //图标
  parentId: string; //上级
  sortCode: number; //排序
  isFirst: number; //首屏
  isMenu: number; //菜单
  remark: string; //描述
  backgroundUrl: string; //封面图片
}
export interface DesktopConfig {
  title: string;
  renderKey: number;
  echarts?: undefined | EChartsOption;
}
export interface processEventItem {
  operateType: ExecuteType;
  operateConfig: any;
}
export interface ButtonItem {
  apiConfig?: any;
  name: string;
  type: ButtonType;
  formName?: string;
  formId?: string;
  apiColumn?: Array<any>;
  processEvent: Array<processEventItem>;
}
export interface TableColumnItem {
  dataIndex: string;
  title: string;
  show: boolean;
  width?: number;
  align?: string;
}
export interface TableConfig {
  title: string;
  type: TableType;
  formId: string;
  pageSize: number;
  formName: string;
  formType: FormTypeEnum;
  apiColumns: Array<any>;
  primaryKey: string;
  associatedForm?: boolean;
  apiConfig?: ApiConfig;
  releaseId: string;
  updateIds: Array<string>;
  columns: Array<TableColumnItem>;
  renderKey: number;
  systemComponent: {
    functionalModule: string;
    functionName: string;
    functionFormName: string;
  };
}
export interface tablePanesItem {
  title: string;
  type: TableType;
  key: string;
  closable: boolean;
  count: boolean;
  formId?: string;
  pageSize: number;
  current: number;
  total: number;
  formName?: string;
  formType: FormTypeEnum;
  apiColumns?: Array<any>;
  primaryKey?: string;
  associatedForm?: boolean;
  apiConfig?: ApiConfig;
  releaseId?: string;
  updateIds?: Array<string>;
  columns?: Array<TableColumnItem>;
  renderKey: number;
  systemComponent?: {
    functionalModule: string;
    functionName: string;
    functionFormName: string;
  };
}
export interface TabsTableConfig {
  title: string;
  renderKey: number;
  panes: Array<tablePanesItem>;
}
export interface TabsTableInfo extends BasicInfo {
  config: TabsTableConfig;
}
export interface TableInfo extends BasicInfo {
  config: TableConfig;
}
export interface ImageConfig {
  title: string;
  renderKey: number;
  showTitle: boolean;
  folderId: string;
}
export interface ImageInfo extends BasicInfo {
  config: ImageConfig;
}
export interface MapConfig {
  title: string;
  renderKey: number;
  showTitle: boolean;
  apiConfig?: ApiConfig;
  apiColumns?: Array<any>;
  primaryKey?: string;
  primaryName?: string;
  panelConfig?: PanelConfig;
}
export const enum PanelArrangeTypeEnum {
  ONE = 'one', //一行一字段
  TWO = 'two', //一行两字段
}
export const enum PanelTitleTypeEnum {
  DEFAULT = 'default', //固定标题
  API = 'api', //字段标题
}
export const enum PanelTableConfigFontAlignEnum {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
}
export interface PanelTableConfig {
  // sort: number; //排序
  // index: number; //序号
  label: string; //字段标题
  value: string; //字段值
  fontWeightBold: boolean; //标题加粗
  fontAlign: PanelTableConfigFontAlignEnum; //对齐方式
  hasFullRow: boolean; //独占一行
}
export interface PanelConfig {
  enabled: boolean; //是否启用
  arrangeType: PanelArrangeTypeEnum; //排列方式
  titleType: PanelTitleTypeEnum; //标题类型
  title: string; //标题内容
  titleField: string; //标题字段
  config: Array<PanelTableConfig>; //面板字段
  clickShow: boolean; //点击显示
}
export interface MapInfo extends BasicInfo {
  config: MapConfig;
}
export interface BasicInfo {
  x: number;
  y: number;
  w: number;
  h: number;
  i: string;
  maxH: number;
  minH: number;
  maxW: number;
  minW: number;
  type: DesktopComponent;
}
export interface ApiInfo {
  apiData: { [x: string]: number };
  apiColumns: any;
  apiConfig: ApiConfig;
}
export interface DesktopInfoItem extends BasicInfo {
  config: DesktopConfig | MixLineBarConfig;
}

// 饼图 config
export interface PieConfig extends ApiInfo {
  title: string; //标题
  renderKey: number;
  labelKey: string; //维度
  valueKey: string; //指标
  colors: Array<string>; //颜色配置
  autoWidth?: boolean; //宽度自动
  echarts: EChartsOption;
}
// 柱状图 config
export interface ChartBarConfig extends ApiInfo {
  title: string; //标题
  renderKey: number;
  labelKey: string; //维度
  valueKey: string; //指标
  targetKey: string; //目标值
  unit: string;
}
// 甘特图 config

export interface CategoryStackConfig extends ApiInfo {
  labelKey?: string; //维度
  valueKey?: string; //指标
  targetKey?: string; //指标
}

export interface ColumnItem {
  id: string;
  name?: string;
  width: number;
  align: AlignType;
}
export interface InformationConfig extends ApiInfo {
  title: string; //标题
  renderKey: number;
  jumpId: string | undefined;
  path: string | undefined;
  maxRows: number;
  columns: Array<ColumnItem>;
  apiColumns: Array<ChartApiColumn>;
  apiConfig: ApiConfig;
}

export interface DashboardConfig extends ApiInfo {
  title: string; //标题
  renderKey: number;
  labelKey: string;
  valueKey: string;
  title1: string; //标题1
  title2: string; //标题2
  showType: DashboardShowType; //风格
  imgType: ImgType; //图片类型
  imgIcon: string; //图标
  imgIconColor: string; //图标颜色
  pictureUrl: string; //图片地址
  flagKey: string | undefined; //标记
  flagIsUp: number | undefined; //标记值
  mainIndex: string | undefined; //主指标
  mainIndexValue: number; //主指标值
  mainIndex2: string | undefined; //主指标2
  mainIndex2Value: number; //主指标2值
  secondaryIndex: string | undefined; //副指标
  secondaryIndexValue: number | undefined; ///副指标值
  jumpId: string | undefined;
  path: string | undefined;
  numColor: string | undefined;
  des: string | undefined;
}
export interface ButtonConfig {
  title: string;
  buttons: Array<ButtonItem>;
}
export interface ButtonInfo extends BasicInfo {
  config: ButtonConfig;
}
export interface DefaultInfo extends BasicInfo {
  config: DesktopConfig;
}
export interface ChartsInfo extends BasicInfo {
  config: DesktopConfig;
}
export interface DashboardInfo extends BasicInfo {
  config: DesktopConfig & DashboardConfig;
}
export interface InformationInfo extends BasicInfo {
  config: DesktopConfig & InformationConfig;
}

// 饼图配置
export interface PieItemInfo extends BasicInfo {
  config: DesktopConfig & PieConfig;
}
// 柱状图
export interface ChartBarInfo extends BasicInfo {
  config: DesktopConfig & ChartBarConfig;
}
// 甘特图
export interface CategoryStackInfo extends BasicInfo {
  config: DesktopConfig & CategoryStackConfig;
}
export interface ChartLineItemInfo extends BasicInfo {
  config: {
    title: string;
    echarts: EChartsOption;
  };
}
interface functionItem {
  id: string;
  name: string;
  icon: string;
  path: string;
  color: string;
}
export interface ModulesConfig {
  functions: Array<functionItem>;
}
export interface ModulesInfo extends BasicInfo {
  config: DesktopConfig & ModulesConfig;
}
export interface TodoListConfig {
  title: string;
  renderKey: number;
  maxRows: number;
  path: {};
}
export interface TodoListInfo extends BasicInfo {
  config: DesktopConfig & TodoListConfig;
}

export interface ChartXAxis {
  position: string;
  name: string;
  nameLocation: string;
  nameTextStyle: {
    color: string;
    fontSize: number;
    fontWeight: string;
  };
  type: string;
  axisLabel: {
    formatter: string;
    color: string;
  };
  axisLine: {
    show: boolean;
    lineStyle: {
      color: string;
    };
  };
  data: Array<any>;
}
export interface ChartYAxis {
  name: string;
  nameLocation: string;
  nameTextStyle: {
    color: string;
    fontSize: number;
    fontWeight: string;
  };
  min: string;
  max: string;
  interval: null;
  position: string;
  type: string;
  axisLabel: {
    formatter: string;
    color: string;
  };
  axisLine: {
    show: boolean;
    lineStyle: {
      color: string;
    };
  };
}

export interface ChartLabel {
  show: boolean;
  color: string;
  position: string;
  fontWeight: string;
  fontSize: number;
  formatter: string | null;
}
export interface ChartApiColumn {
  prop: string;
  label: string;
}
export interface ChartLegend {
  show: true;
  orient: OrientType;
  left: string;
  top: string;
  right: string;
  bottom: string;
  padding: number;
  icon: LegendType;
  itemWidth: number;
  itemHeight: number;
  textStyle: {
    color: string;
  };
  formatter: string;
  width?: string | number;
}

// 柱状图折线图混合
export interface MixLineIndicator {
  title: string;
  type: string;
  color: string;
  name: string;
  value: string;
  showAreaStyle: boolean; //是否显示面积图
  gradualStartColor: string;
  gradualEndColor: string;
}
export interface MixLineBarDataItem {
  title: string;
  valueKey: string;
  total: number;
  apiData: Array<any>;
  apiColumns: Array<ChartApiColumn>;
  apiConfig: ApiConfig;
  indicator: Array<MixLineIndicator>;
}
export interface EchartsSeriesItem {
  name: string;
  type: string;
  smooth: boolean;
  areaStyle: any;
  stack: string;
  label: {
    show: boolean;
    color: string;
  };
  data: Array<any>;
  showAreaStyle: boolean;
  gradualEndColor: string;
  gradualStartColor: string;
  symbol?: string;
}

export interface IndicatorItem {
  title: string;
  type: string;
  color: string;
  name: string;
  value: string;
}
export interface MixLineBarDataListItem {
  title: string;
  valueKey: string;
  apiData: Object;
  indicator: Array<IndicatorItem>;
  apiConfig: ApiConfig;
}
export interface EchartsItem {
  color: Array<string>;
  legend: {
    data: Array<string>;
  };
  xAxis: Array<{
    type: string;
    data: Array<any>;
  }>;
  yAxis: Array<{
    type: string;
  }>;
  series: Array<EchartsSeriesItem>;
}
export interface MixLineBarConfig {
  title: string;
  renderKey: number;
  condition: {
    color: string;
    selected: string;
  };
  legend: ChartLegend;
  isCrosswise: boolean; //是否横向
  // 统计
  count: {
    show: boolean;
    unit: string;
    title: string;
  };
  dataList: Array<MixLineBarDataItem>;
  bar: {
    stack: boolean; //是否显示堆叠
    label: {
      show: boolean;
      color: string;
    };
  };
  line: {
    smooth: boolean;
    stack: boolean; //是否显示堆叠
    showAreaStyle: boolean; //是否显示面积图
    gradualStartColor: string;
    gradualEndColor: string;
    showSymbol: boolean; //是否显示标记
    label?: ChartLabel;
  };
  label: ChartLabel;
  yAxis: Array<ChartYAxis>;
  xAxis: Array<ChartXAxis>;
  echarts: Array<EchartsItem>;
}
export interface MixLineBarInfo extends BasicInfo {
  config: MixLineBarConfig;
}
