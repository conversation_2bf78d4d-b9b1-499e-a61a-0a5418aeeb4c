import { ApiConfig } from '/@/components/ApiConfig/src/interface';
import { ParamType } from '/@/enums/workflowEnum';

export interface ParamAssignmentConfig {
  type: ParamType; //赋值类别
  value: string; // 如果是 type === 值 就存储值
  varValue: string; // 如果type === 变量 存储所选变量
  apiConfig: ApiConfig; // 如果type === api 存储api配置
  formConfig: FormAssignmentSourceConfig; // 如果type === 表单配置 存储api配置
  target: string; // 目标变量 存储 流程变量名 processConfig 的  id
}

export interface scriptNodeConfig {
  //脚本参数配置
  scriptConfig: {
    enabled: boolean; //是否开始
    scriptFormat: number; //脚本格式
    scriptContent: string; //脚本内容
  };
}

// 流程参数配置
export interface ProcessParamConfig {
  paramName: string; //流程参数
  paramType: ParamType; //参数类型
  paramValue: string; //如果是流程类型==值
  paramApi: ApiConfig; //如果是流程类型==api
  paramVariable: {}; //如果是流程类型==变量
  // ParamConfigs: Array<ProcessParamConfigItem>;
}
// 参数操作  表单赋值 来源 配置
export interface FormAssignmentSourceConfig {
  key: string; //唯一树id
  formId: string; // 表单id
  formField: string; //表单字段
}
// 参数操作  表单赋值 配置
export interface FormAssignmentConfig {
  source: string; // 来源 变量

  target: FormAssignmentSourceConfig; // 目标 表单字段
}
/**
 *  参数操作 参数赋值 配置
 */
export interface AssignmentConfig {
  formAssignmentConfigs: FormAssignmentConfig[]; //表单赋值
  paramAssignmentConfigs: ParamAssignmentConfig[]; //参数赋值
}
/**
 *  超时处理 配置
 */
export interface TimeOutConfig {
  isHandle: number; //超时处理
  rule: number; //超时机制
  type: number; //处理方式
  user: number; //指定人员
  auto: number; //无处理人
}
