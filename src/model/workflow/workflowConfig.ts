//流程配置json

import { InfoId } from './bpmnConfig';
import { FormConfigItem, formInitConfig, FormSettingItem } from './formSetting';
import { CountersignConfig, MemberConfig } from './memberSetting';
import { AssignmentConfig, TimeOutConfig } from './ParamSetting';
import { ApiConfig } from '/@/components/ApiConfig/src/interface';
import { MessageConfigure } from '/@/components/PushMessage/src/interface';

import {
  ApprovalOpinionDisplayType,
  AuthType,
  AutoAgreeRule,
  ButtonType,
  DesignatedApprover,
  ElectronicSignatureVerification,
  NoHandler,
  NoticePolicyType,
  OperationType,
  ProcessStatus,
  RecordType,
  TaskPermissions,
  RejectType,
  CallActivityType,
  FinishType,
  ExecutionType,
  MemberType,
  ApproveType,
  NodeEventExType,
} from '/@/enums/workflowEnum';
import { BpmnNodeKey } from '/@/enums/workflowEnum';

export interface ProcessJsonModel {
  processConfig: ProcessConfig; //流程属性配置
  //子节点配置
  childNodeConfig: ChildNodeConfig;
}
export type ChildNodeItem =
  | StartEventConfig
  | UserTaskConfig
  | ScriptTaskConfig
  | GatewayConfig
  | EndEventConfig;
export type ChildNodeConfig = Array<ChildNodeItem>;
// 专项菜单
export interface MenuConfig {
  enabled: boolean;
  code: string;
  name: string;
  system: undefined | string;
  parentId: undefined | string;
  icon: string;
  order: undefined | number;
  remark: string;
}

// 权限设置
interface AuthConfig {
  authType: AuthType; //权限设置 0 所有 1 指定
  authMemberConfigs?: MemberConfig[]; //必须authType === 1 权限指定的人员
}
//超时提醒
interface TimeoutConfig {
  enabled: boolean; //是否提醒
  hour: undefined | number; //超时时间
  interval: undefined | number; //消息间隔
  pushHits: undefined | number; //推送次数
  pushMemberConfigs: MemberConfig[]; //推送人
}

//关联任务
export interface RelationProcessConfig {
  id: string; //模板id
  name: string; //模板名称
  code: string; //模板编码
  definitionKey?: string; //定义可以
  processStatus?: ProcessStatus; //流程状态
  processAuth?: TaskPermissions; //任务权限
}
// 命名规则
export interface NameRuleItem {
  key: string;
  title: string;
}
//摘要信息
export interface summaryInfoConfig {
  formName: string; //表单名称
  fieldName: string; //字段名称
  fieldId: string; //字段id
  formKey: string; //表单key
  key: string; //唯一标识
}
export type NameRule = Array<NameRuleItem>;
export type ProcessConfigParameter = Array<ProcessParameter>;
export interface ProcessConfig {
  processId: string; //节点id
  type: BpmnNodeKey; //节点类型
  name: string; //节点名称
  parentId: string; //父节点（流程id）
  remark: string; //节点描述
  code: string; //模板编码
  category: undefined | string; //分类
  nameRule?: string; //命名规则
  nameRuleConfigs?: NameRule; // 命名规则列表
  autoAgreeRule?: Array<AutoAgreeRule>; //自动同意规则
  isPrevChooseNext: DesignatedApprover; //是否上一节点审批人指定下一节点审批人
  noHandler: NoHandler; //无对应处理人
  defaultFormList: Array<FormSettingItem>; //默认表单
  appShow: boolean; //移动端是否显示
  content?: string; //bpmn 设计 xml
  authConfig: AuthConfig; //权限设置
  menuConfig: MenuConfig; //是否启用菜单
  formInitConfig: formInitConfig; //表单发起流程
  timeoutRemidConfig: TimeoutConfig; //超时提醒配置
  relationProcessConfigs: Array<RelationProcessConfig>; //关联任务
  processParamConfigs: ProcessConfigParameter; //流程参数
  xmlContent: String; //xml
  summaryInfo?: Array<summaryInfoConfig>; //摘要
}

// 参数操作
export interface ProcessParameter {
  id: string;
  name: string;
  type: OperationType;
  value: string;
  apiConfig: ApiConfig;
}
// 输入参数

/**
 * 开始节点配置
 */
export interface StartEventConfig extends BasicNodeConfig {
  parentId: string; //父节点（流程id）
  assignmentConfig: AssignmentConfig; //参数操作
  formConfigs: Array<FormConfigItem>; // 表单配置
  subProcessInitiator: string; //子流程开始节点发起人
}

/**
 * 用户节点配置
 */

//按钮配置
export interface ButtonConfigItem {
  approveType: ApproveType; //审批类型
  buttonType: ButtonType; //按钮类型
  buttonName: string; //按钮名称
  buttonCode: string; //按钮编码
  checked: boolean; //选中
  scriptContent?: string; //脚本内容
  scriptLanguage?: number; //脚本格式
  apiConfig?: ApiConfig; //api配置
  buttonOpera?: RejectType; //驳回类型
}
// 意见簿
export interface OpinionConfig {
  enabled: boolean; //是否关联表单
  showType: ApprovalOpinionDisplayType; //审批意见展示类型 0 显示所有 1 显示最终结果
  signature: ElectronicSignatureVerification; //电子签章验证
  component: Array<string>; //关联意见框组件
}
/**
 * 用户任务配置
 */
export interface UserTaskConfig extends BasicNodeConfig {
  parentId: string; //父节点（流程id）
  currentProgress: undefined | number; //进度
  autoAgreeRule: Array<AutoAgreeRule>; //自动同意规则
  isPrevChooseNext: DesignatedApprover; //是否上一节点审批人指定下一节点审批人
  noHandler: NoHandler; //无对应处理人
  provisionalApprover: Boolean; //临时审批人
  noticePolicyConfigs: NoticePolicyType[]; //通知策略
  approverConfigs: MemberConfig[]; //审批人
  // authMember: MemberConfig; //审批人
  circulateConfigs: MemberConfig[]; //传阅人
  countersignConfig: CountersignConfig; //会签
  buttonConfigs: Array<ButtonConfigItem>;
  opinionConfig: OpinionConfig;
  formConfigs: Array<FormConfigItem>; // 表单配置
  assignmentConfig: AssignmentConfig; //参数操作
  timeOutHandle: TimeOutConfig; //超时处理
}
/**
 * 脚本节点配置
 */
export interface ScriptTaskConfig extends BasicNodeConfig {
  parentId: string; //父节点（流程id）
  noticePolicyConfigs: NoticePolicyType[]; //通知策略
  recordInfo: RecordType; //记录信息
  script: {
    enabled: boolean;
    scriptFormat: string; //脚本格式
    scriptContent: string; //脚本内容
  };
  api: {
    enabled: boolean;
    apiConfig: ApiConfig;
  };
  assignmentConfig: AssignmentConfig; //参数操作
}

/**
 * 网关配置
 */
export interface GatewayConfig extends BasicNodeConfig {
  parentId: string; //父节点（流程id）
}
/**
 * 结束配置
 */
export interface EndEventConfig extends BasicNodeConfig {
  parentId: string; //父节点（流程id）
  noticePolicyConfigs: NoticePolicyType[]; //通知策略
}

/**
 * 子流程配置
 */
export interface SubProcessConfig extends BasicNodeConfig {
  parentId: string; //父节点（流程id）
  //子流程发起人  （选择的是主流程的某个节点  所以存储的也是子流程节点的id）
  mainProcessNodeId: string;
}

/**
 * 流程线配置
 */
export interface SequenceFlowConfig extends BasicNodeConfig {
  parentId: string; //父节点（流程id）
  conditionConfigs: ConditionConfig[]; //变量来源
  sourceConfig?: SequenceFlowSourceConfig;
}
// 上一级节点信息
export interface SequenceFlowSourceConfig {
  id: string;
  type: BpmnNodeKey;
}
/**
 * 流转条件配置
 */
export interface ConditionConfig {
  nodeId: string;
  formId: string;
  formField: string;
}
export interface CallActivityParams {
  source: string;
  target: string;
}
/**
 * 外部流程
 */
export interface CallActivityConfig extends BasicNodeConfig {
  parentId: string; //父节点（流程id）
  schemaId: string; //被调用元素（外部流程schemaId）
  schemaName: string; //被调用元素（外部流程schemaName）
  callActivityType: CallActivityType.SINGLE; // 调用类型
  finishType: FinishType.ALL; //完成条件
  percentOf: undefined | Number; //百分比数值
  executionType: ExecutionType; //执行类型
  originatorType: MemberType; //发起人类型
  originatorNode: string; //如果调用类型为单实例 子流程发起人
  originatorConfig: string; //发起人表单数据配置
  inParams: CallActivityParams[]; //输入参数
  outParams: CallActivityParams[]; //输出参数
}

export type ValidateProcessConfigs = Array<{
  nodeName: string; //节点名称
  nodeType: BpmnNodeKey; //节点类型
  msg: string;
  validate: boolean;
}>;

export interface BasicNodeConfig {
  id: string; //节点id
  type: BpmnNodeKey; //节点类型
  name: string; //节点名称
  remark: string; //节点描述
  startEventConfigs: NodeEventConfig[];
  endEventConfigs: NodeEventConfig[];
  agreeEventConfigs?: NodeEventConfig[]; //同意事件
  rejectEventConfigs?: NodeEventConfig[]; //驳回事件
  withdrawEventConfigs?: NodeEventConfig[]; //撤回事件
  timeoutEventConfigs?: NodeEventConfig[]; //超时事件
}

export interface NodeEventConfig {
  type: NodeEventExType; //节点类型:
  apiConfig?: ApiConfig; //APi配置
  messageConfig?: MessageConfigure; //推送消息配置
  liteflowId?: string; //流程引擎
}

export interface RootElement {
  id: InfoId;
  name: string;
  $type: BpmnNodeKey;
  incoming?: Array<RootElement>;
  outgoing?: Array<RootElement>;
  flowElements?: Array<RootElement>;
}

export type RootElements = Array<RootElement>;
