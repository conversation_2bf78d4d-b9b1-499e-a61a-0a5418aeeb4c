/**
 * 手机端表单props
 */
export interface AppFormProps {
  rules: Object; //校验规则
  validateTrigger: string; //各组件配置
  labelPosition: string; //label 位置 top/left
  labelWidth: number; //label 宽度，单位 px 75
  labelAlign: string; //label 居中方式  left/center/right
  errShowType: string; //表单错误信息提示方式 undertext/toast/modal
  border: boolean; //是否显示分格线
  schemas: AppFormSchema[]; //组件模板
}

/**
 * 手机端组件配置模板
 */
export interface AppFormSchema {
  key: string; //唯一标识  用于处理某些无字段组件
  name: string; //表单域的属性名，在使用校验规则时必填
  field?: string; //字段名
  label?: string; //输入框左边的文字提示
  component?: AppComponentType; //组件类型
  labelWidth?: number; //label 宽度，单位 px
  labelAlign?: string; //label 居中方式
  rules?: {
    required: boolean;
    errorMessage: string;
  }[]; //表单校验规则
  required?: boolean; //label 右边显示红色"*"号，样式显示不会对校验规则产生效果
  defaultValue?: any; //默认值
  events?: {
    [key: string]: string;
  };
  componentProps?: any; //组件的所有配置信息
  layout?: any[]; //tab 等组件特有
  columns?: AppFormSchema[]; //子表单组件特有
  preloadType?: string; //子表单预加载类型
  itemId?: string; //子表单预加载数据字典
  apiConfig?: any; //子表单预加载api
  associateOptions?: any; //子表单按钮选数据数据选择的配置
  useSelectButton?: boolean; //子表单是否按钮选数据
  buttonName?: string; //子表单 按钮选数据按钮名称
  url?: string;
}

export enum AppComponentType {
  input = 'Input',
  inputNumber = 'InputNumber',
  picker = 'Picker',
  associatePicker = 'AssociatePicker',
  select = 'Select',
  checkbox = 'Checkbox',
  radio = 'Radio',
  switch = 'Switch',
  date = 'Date',
  dateTime = 'DateTime',
  dateRange = 'DateRange',
  dateTimeRange = 'DateTimeRange',
  collapse = 'Collapse',
  tab = 'Tab',
  segmented = 'Segmented',
  divider = 'Divider',
  subForm = 'SubForm',
  singleForm = 'SingleForm',
  autoCode = 'AutoCode',
  editor = 'Editor',
  cascader = 'Cascader',
  slider = 'Slider',
  timeRange = 'TimeRange',
  rate = 'Rate',
  image = 'Image',
  color = 'colorPicker',
  qrcode = 'QRcode',
  title = 'Title',
  map = 'SelectMap',
  upload = 'FileUpload',
  opinion = 'Opinion',
  organization = 'Organization',
  user = 'User',
  computation = 'Computation',
  moneyChinese = 'MoneyChinese',
  associatePopup = 'AssociatePopup',
  multiplePopup = 'MultiplePopup',
  info = 'Info',
  button = 'CustomButton',
  tableLayout = 'TableLayout',
  text = 'Text',
  formView = 'FormView',
  iframe = 'Iframe',
  TreeSelect = 'TreeSelect',
  TreeChecked = 'TreeChecked',
  signature = 'Signature',
  barcode = 'Barcode',
}
