/**
 * 表单设计器 代码优先/界面优先
 */
export interface TableStructureConfig {
  //表的唯一标识（主要用于区分子表）
  key?: string;
  //表名
  tableName: string;
  //表注释
  tableComment: string;
  //是否主表
  isMain: boolean;
  //表的字段配置
  tableFieldConfigs: TableFieldConfig[];
  //数据表操作（界面优先编辑时）
  operator?: number;
  //次级表格组件使用
  parentTable?: string;
  //次级表格组件使用
  parentKey?: string;
  pkField?: string;
}

export interface TableFieldConfig {
  key?: string;
  //字段名称
  fieldName?: string;
  //字段开始名称（范围组件特有）
  fieldStartName?: string;
  //字段结束名称（范围组件特有）
  fieldEndName?: string;
  //字段长度
  fieldLength: number | null;
  //字段类型 0:文本 （默认255） 1:长文本 （max） 2:数字 3:小数 4:日期 5:日期时间 6:外键 7:长整数
  fieldType: number;
  //字段备注
  fieldComment: string;
  tableName?: string;
}
