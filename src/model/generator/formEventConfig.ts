/**
 * 表单事件
 */
export interface FormEventStyleConfig {
  color?: string;
  icon?: string;
  text?: string;
  detail?: string;
  type?: string;
  bgcColor?: string;
  lineHeight?: string;
  isLast?: boolean;
  isUserDefined: boolean;
  isClick?: boolean;
  nodeInfo?: nodeInfoConfig;
  //是否日志记录
  isLogRecord?: boolean;
}

export interface FormEventColumnConfig {
  0: FormEventStyleConfig[];
  1: FormEventStyleConfig[];
  2: FormEventStyleConfig[];
  3: FormEventStyleConfig[];
  4: FormEventStyleConfig[];
}

export interface selectedNodeConfig {
  columnIndex: number;
  index: number;
}

export interface nodeInfoConfig {
  processEvent?: processEventConfig[];
}

export interface processEventConfig {
  operateConfig: any;
  operateType: string;
}
