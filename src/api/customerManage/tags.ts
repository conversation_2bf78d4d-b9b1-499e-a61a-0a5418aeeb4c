import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  tagPage = '/business/customer/page',
  tagDel = '/business/customer',
  tagAdd = '/business/customer/submit',
  tagConnect = '/business/customerTagRel/add',
  tagConnectCancel = '/business/customerTagRel/delete',
  accountPage = '/business/account/labelCustomerPage',
}

/**
 * @description: tag列表(分页)
 */
export async function getTagPage(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.tagPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除 tag
 */
export async function tagDel(ids?: any[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete(
    {
      url: Api.tagDel,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增/编辑 tag
 */
export async function tagAdd(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.tagAdd,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 客户关联
 */
export async function tagConnect(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.tagConnect,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 取消客户关联
 */
export async function tagConnectCancel(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.tagConnectCancel,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: account列表(分页)
 */
export async function getAccountList(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.accountPage,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
