import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  accountPage = '/business/account/page',
  accountRelation = '/business/account/relation',
  accountSyncAccount = '/business/account/syncAccount',
  tagList = '/business/customer/tagList',
  headList = '/business/account/headList',
  userList = '/organization/user/userList',
  export = '/business/account/export',
  getOtcReportList = '/wechat/sign/getPageList',
  getOtcWeekReportDetail = '/report/add/spec',
}

/**
 * @description: account列表(分页)
 */
export async function getAccountPage(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.accountPage,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 客户关联用户
 */
export async function setAccountRelation(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.accountRelation,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 同步数据
 */
export async function setAccountSyncAccount(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.accountSyncAccount,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 客户标签列表（不分页）
 */
export async function getTagList(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.tagList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 客户总部列表（不分页）
 */
export async function getHeadList(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.headList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 用户列表（不分页）
 */
export async function getUserList(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.userList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 导出
 */
export async function exportData(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.export,
      method: 'POST',
      data,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 周报列表
 */
export async function getOtcReportList(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
      {
          url: Api.getOtcReportList,
          data,
      },
      {
          errorMessageMode: mode,
      },
  );
}
/**
 * @description: 周报详情
 */
export async function getOtcWeekReportDetail(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
      {
          url: Api.getOtcWeekReportDetail,
        params,
      },
      {
          errorMessageMode: mode,
      },
  );
}
