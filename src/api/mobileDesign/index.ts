import { BasicPageParams } from '../model/baseModel';
import { AppFuncModel } from '../system/generator/model';

import { ErrorMessageMode } from '/#/axios';

import { MobileData } from '/@/model/mobileDesign/designer';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  Page = '/app/page-design/page',
  DataDisplay = '/app/page-design',
  DataDisplayDraft = '/app/page-design/draft',
  Info = '/app/page-design/info',
  EnableMark = '/app/page-design/update-enable-mark',
  FuncPage = '/app/func-design/page',
  FuncDesign = '/app/func-design',
  FuncInfo = '/app/func-design/info',
  FuncEnable = '/app/func-design/update-enable-mark',
  MenuCategory = '/app/menuCategory/list',
  AddMenuCategory = '/app/menuCategory/add',
  UpdateMenuCategory = '/app/menuCategory/update',
  DeleteMenuCategory = '/app/menuCategory/delete',
}

/**
 * @description: 查询移动数据设计分页列表
 */
export async function getPageList(params: BasicPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询移动功能设计分页列表
 */
export async function getFuncPageList(params: BasicPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.FuncPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增移动数据设计
 */
export async function addMobileData(info: MobileData, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.DataDisplay,
      params: info,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增移动数据设计草稿
 */
export async function addMobileDataDraft(info: MobileData, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.DataDisplayDraft,
      params: info,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增移动功能设计
 */
export async function addMobileFunc(info: AppFuncModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.FuncDesign,
      params: info,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 编辑移动数据设计
 */
export async function editMobileData(info: MobileData, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.DataDisplay,
      params: info,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 编辑移动功能设计
 */
export async function editMobileFuncData(info: AppFuncModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.FuncDesign,
      params: info,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除移动数据设计
 */
export async function deleteMobileData(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.DataDisplay,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除移动功能设计
 */
export async function deleteMobileFuncData(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.FuncDesign,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 修改移动数据设计启用状态
 */
export async function updateEnableMark(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    {
      url: Api.EnableMark,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 修改移动功能设计启用状态
 */
export async function updateFuncEnableMark(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    {
      url: Api.FuncEnable,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取移动数据设计信息
 */
export async function getMobileInfo(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取移动功能设计信息
 */
export async function getMobileFuncInfo(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.FuncInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function menuCategory(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.MenuCategory,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function addMenuCategory(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.AddMenuCategory,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function updateMenuCategory(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.UpdateMenuCategory,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function deleteMenuCategory(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.DeleteMenuCategory,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
