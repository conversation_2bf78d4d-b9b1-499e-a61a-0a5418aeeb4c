import { BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel';
import { FormType } from '/@/enums/workflowEnum';

export interface FormTemplateListParams {
  keyword?: string;
  type?: number;
}

/**
 * @description: Request list interface parameters
 */
export type FormTemplatePageParamsModel = BasicPageParams & FormTemplateListParams;

export interface FormTemplateListModel {
  id: string; //主键
  name: string; //名称
  category: string; //表单分类 关联 数据字典
  formType: number; //表单类型:0 系统表单 1 自定义表单
  formDesignType: number; //表单模板类型：0 数据优先 1 界面优先 2 简易模板
  sortCode: number; //排序号
  remark: string; //备注
  enabledMark: number; //禁用:0 禁用 1 启用
  functionalModule?: string; //功能模块
  functionName?: string; //功能名称
}

export interface FormTemplateModel {
  id: string; //主键
  name: string; //名称
  category: string; //表单分类 关联 数据字典
  formType: FormType; //表单类型:0 系统表单 1 自定义表单
  formDesignType: number; //表单模板类型：0 数据优先 1 界面优先 2 简易模板
  sortCode: number; //排序号
  remark: string; //备注
  formJson: string; //表单设计json
  functionalModule?: string; //功能模块
  functionName?: string; //功能名称
}
export interface versionModal {
  formId: string;
  id: string;
}
/**
 * @description: Request list return value
 */
export type FormTemplateListResultModel = BasicFetchResult<FormTemplateListModel>;
