import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

/**
 * @description: Demo分页参数 模型
 */
export interface DemoPageParams extends BasicPageParams {
  name?: string; //姓名
  mobile?: string; //手机号码
  userName?: string; //账户
  code?: number; //排序号
}

/**
 * @description: Demo分页返回值模型
 */
export interface DemoPageModel {
  id: string; //用户名
  fieldString: string; //用户名
  fieldInt: number; //姓名
  fieldDatetime: string; //手机号码
  fieldDouble: number; //邮箱
  fieldLong: number; //排序号
  remark: string; //备注
  sortCode: number; //备注
}

/**
 * @description: Demo表单类型
 */
export interface DemoModel {
  fieldString: string; //用户名
  fieldInt: number; //姓名
  fieldDatetime: string; //手机号码
  fieldDouble: number; //邮箱
  fieldLong: number; //排序号
  remark: string; //备注
  sortCode: number; //备注
}

/**
 * @description: Demo分页返回值结构
 */
export type DemoPageResult = BasicFetchResult<DemoPageModel>;
