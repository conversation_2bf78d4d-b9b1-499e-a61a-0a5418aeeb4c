import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { AuthPageListResultModel, AuthPageListSearchModel, AddLogModel } from './model';

enum Api {
  Page = '/system/log/page',
  clear = '/system/log/from',
  Route = '/system/log',
  Port = '/system/log/export',
}

/**
 * @description: 查询系统日志分页列表
 */
export async function getLogPageList(
  params: AuthPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<AuthPageListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除日志（批量删除）
 */
export async function deleteLog(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Route,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 清空日志
 */
export async function clearLog(type: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.clear + '?type=' + type,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 导出日志
 */
export async function exportLog(ids, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Port,
      method: 'POST',
      params: ids,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增日志
 */
export async function addLog(data: AddLogModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Route,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
