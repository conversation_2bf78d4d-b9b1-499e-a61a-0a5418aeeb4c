export interface DeveloperParamsModel {
  id?: string;
  code: string; //开发者编码
  name: string; //开发者名称
  appId: string; //appId
  appSecret: string; //appSecret
  appName: string; //应用名称
  validateSign: string; //验证签名
  expireDate?: string; //使用期限
  enabledMark: string; //状态
  whiteList?: string; //白名单
  remark?: string; //备注
}

export interface AuthParamsModel {
  id: string; //开发者id
  interfaceIds: string[]; //授权id
}
