import { MenuButtonModel } from '../menuButton/model';
import { MenuModel, MenuTreeModel, MenuTreeParams, SystemMenuTreeParams } from './model';
import { ErrorMessageMode } from '/#/axios';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  Page = '/system/menu/page',
  Tree = '/system/menu/tree',
  AllTree = '/system/menu/all-tree',
  jumpMenuTree = '/system/menu/child-tree',
  SimpleTree = '/system/menu/simple-tree',
  Menu = '/system/menu',
  Button = '/system/menu/button',
  Column = '/system/menu-colum/list',
  Form = '/system/menu-form/list',
  APPMenuList = '/app/menu/simple-tree',
  APPMenu = '/app/menu',
  AppButton = '/app/menu/button-list',
  AppColumn = '/app/menu/column-list',
  AppForm = '/app/menu/form-list',
  UserMenuColumnShow = '/system/user-menu-column-show', //新增或者编辑用户列展示数据
  UserMenuColumnShowInfo = '/system/user-menu-column-show/info', // 根据菜单id获取用户列展示数据
}

// /**
//  * @description: 查询角色所有 不分页
//  */
// export async function getRoleAllList(mode: ErrorMessageMode = 'modal') {
//   return defHttp.get<RolePageListResultModel>(
//     {
//       url: Api.GetAll,
//     },
//     {
//       errorMessageMode: mode,
//     }
//   );
// }

/**
 * @description: 查询菜单树
 */
export async function getMenuTree(params?: SystemMenuTreeParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<MenuTreeModel[]>(
    {
      url: Api.AllTree,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询调整菜单
 */
export async function getJumpMenuMenuTree(
  params?: MenuTreeParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<MenuTreeModel[]>(
    {
      url: Api.jumpMenuTree,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询菜单树
 */
export async function getMenuSimpleTree(params?: MenuTreeParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.SimpleTree,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除菜单（批量删除）
 */
export async function deleteMenu(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Menu,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增菜单
 */
export async function addMenu(menu: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Menu,
      data: menu,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取菜单信息
 */
export async function getMenu(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<MenuModel>(
    {
      url: Api.Menu,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新菜单
 */
export async function updateMenu(menu: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.Menu,
      data: menu,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据Menuid 查询 按钮
 */
export async function getMenuButtonById(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<MenuButtonModel[]>(
    {
      url: Api.Button,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据Menuid 查询 字段
 */
export async function getMenuColumnById(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<MenuButtonModel[]>(
    {
      url: Api.Column,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 根据Menuid 查询 表单项
 */
export async function getMenuFormById(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<MenuButtonModel[]>(
    {
      url: Api.Form,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询移动端菜单
 */
export async function getAppMenuTree(
  params?: SystemMenuTreeParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<MenuTreeModel[]>(
    {
      url: Api.APPMenuList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 根据Menuid 查询 按钮
 */
export async function getAppMenuButtonById(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<MenuButtonModel[]>(
    {
      url: Api.AppButton,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据Menuid 查询 字段
 */
export async function getAppMenuColumnById(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<MenuButtonModel[]>(
    {
      url: Api.AppColumn,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 根据Menuid 查询 表单项
 */
export async function getAppMenuFormById(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<MenuButtonModel[]>(
    {
      url: Api.AppForm,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除App菜单（批量删除）
 */
export async function deleteAppMenu(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.APPMenu,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增App菜单
 */
export async function addAppMenu(menu: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.APPMenu,
      data: menu,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 更新菜单
 */
export async function updateAppMenu(menu: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.APPMenu,
      data: menu,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据菜单id获取用户列展示数据
 */
export async function getUserMenuColumnShowInfo(menuId, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.UserMenuColumnShowInfo,
      params: { menuId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增用户列展示数据
 */
export async function postUserMenuColumnShow(menuId, columnJson, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<any>(
    {
      url: Api.UserMenuColumnShow,
      data: { menuId, columnJson },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改用户列展示数据
 */
export async function putUserMenuColumnShow(
  id,
  menuId,
  columnJson,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<any>(
    {
      url: Api.UserMenuColumnShow,
      data: { id, menuId, columnJson },
    },
    {
      errorMessageMode: mode,
    },
  );
}
