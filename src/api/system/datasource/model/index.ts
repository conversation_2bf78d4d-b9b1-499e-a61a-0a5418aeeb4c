import { BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel';

/**
 * 数据源模型
 */
export interface DatasourceModel {
  id: string;
  code: string;
  name: string;
  databaselinkId: string;
  sqlScript: string;
  sortCode?: number;
  remark?: string;
}

/**
 * 查询参数模型
 */
export interface DatasourceParams {
  code?: string;
  name?: string;
}

export interface DatasourceByIdParams {
  order?: string;
  orderField?: string;
  id: string;
}

/**
 * @description: Request page interface parameters
 */
export type DatasourcePageParamsModel = BasicPageParams & DatasourceParams;

export type DatasourceByIdParamsModel = BasicPageParams & DatasourceByIdParams;

/**
 * @description: Request list return value
 */
export type DatasourcePageResultModel = BasicFetchResult<DatasourceModel>;
