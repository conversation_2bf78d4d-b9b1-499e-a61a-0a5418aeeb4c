import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  FilePageListParams,
  FilePageListSearchModel,
  FilePageListResultModel,
  FilePageListModel,
} from './model';
enum Api {
  File = '/system/file',
  Info = '/system/file/info',
  List = '/system/file',
  Page = '/system/file/page',
  DeleteFile = '/system/file/delete-single',
}

/**
 * @description: 查询文件分页
 */
export async function getFilePage(
  params: FilePageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<FilePageListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除文件（批量删除）
 */
export async function deleteFile(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.File,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除单个文件
 */
export async function deleteSingleFile(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<string>(
    {
      url: Api.DeleteFile,
      data: id,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询文件列表
 */
export async function getFileList(params: FilePageListParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FilePageListModel[]>(
    {
      url: Api.List,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
