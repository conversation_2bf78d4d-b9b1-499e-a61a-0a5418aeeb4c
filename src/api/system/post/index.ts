import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  PostInfo,
  PostListModel,
  PostModel,
  PostPageListParams,
  PostPageListParamsModel,
  PostPageListResultModel,
} from './model';

enum Api {
  Page = '/organization/post/page',
  List = '/organization/post/list',
  Tree = '/organization/post/tree',
  Info = '/organization/post/info',
  Post = '/organization/post',
  MultiInfo = '/organization/post/info/multi',
  Users = '/organization/post/get-post-user',
  UpdatePost = '/organization/post/update-user-post-batch',
  Switch = '/organization/post/switch-post',
}

/**
 * @description: 查询岗位分页列表
 */
export async function getPostPageList(
  params: PostPageListParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<PostPageListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询岗位用户列表
 */
export async function getPostUserList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Users,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询岗位列表
 */
export async function getTreePostList(
  params?: PostPageListParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<PostListModel>(
    {
      url: Api.Tree,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询岗位列表树形结构
 */
export async function getPostList(params: PostPageListParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<PostListModel>(
    {
      url: Api.List,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除岗位（批量删除）
 */
export async function deletePost(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Post,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增岗位
 */
export async function addPost(post: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Post,
      data: post,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取岗位信息
 */
export async function getPost(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<PostModel>(
    {
      url: Api.Post,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新岗位
 */
export async function updatePost(post: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.Post,
      data: post,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 添加岗位人员
 */
export async function updatePostByUser(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.UpdatePost,
      data: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询岗位树
 */

export async function getPostTree(
  params: { limit: number; size: number; departmentId: string },
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<PostPageListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取岗位信息
 */
export async function getPostInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<PostInfo>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 批量获取岗位信息
 */
export async function getPostMulti(ids: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<PostInfo[]>(
    {
      url: Api.MultiInfo,
      params: { ids },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 切换岗位
 */
export async function changePost(postId: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.Switch,
      data: { postId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
