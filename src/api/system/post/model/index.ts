import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface PostPageListParams {
  name?: string; //岗位名
  code?: string; //编码
  enabledMark?: number; //状态
  deptId?: string;
}

/**
 * @description: Request list interface parameters
 */
export type PostPageListParamsModel = BasicPageParams & PostPageListParams;

export interface PostPageListModel {
  id: string;
  name: string; //岗位名
  code: string; //编码
  enabledMark: number; //状态
  remark: string; //备注
}

export interface PostListModel {
  id: string; //岗位ID
  name: string; //岗位名
  code: string; //编码
  enabledMark: number; //状态
  remark: string; //备注
}

export interface PostPageListParams {
  menuIds: string[]; //菜单ids
  buttonIds: string[]; //按钮ids
}

export interface PostModel {
  name: string; //岗位名
  code: string; //编码
  enabledMark: number; //状态
  remark: string; //备注
  sortCode: number; //排序码
}

/**
 * @description: Request list return value
 */
export type PostPageListResultModel = BasicFetchResult<PostPageListModel>;

export interface PostInfo {
  id: string;
  name: string; //岗位名
  code: string; //编码
}
