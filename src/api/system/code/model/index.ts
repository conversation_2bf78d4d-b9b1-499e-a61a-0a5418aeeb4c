/**
 * @description: Request list interface parameters
 */

export interface CodeRuleListParams {
  keyword?: string; //关键字
}
export interface CodeListParams {
  keyword?: string; //关键字
  limit?: number; //页标
  order?: string; //排序方式 asc desc
  orderField?: string; //排序字段
  size?: number; //每页大小
}

export interface CodeListModel {
  id: number;
  code: string;
  name: string;
  currentNumber: string;
  formatJson: string;
  sortCode: number;
  description: string;
  createDate: string;
  createUserName: string;
}

export interface CodeRuleParams {
  code?: string; //编号
  description?: string; //备注
  enabledMark?: number; //有效标志
  formatJson?: string; //规则格式 JSON
  name?: string; //名称
  sortCode?: number; //排序码
}
