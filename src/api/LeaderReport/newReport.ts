import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  getOtcWeekReportDetail = '/report/add/detail',
  saveOtcWeekReport = '/report/add/save',
  getOtcWeekReportTaskDetail = '/report/add/spec',
}

export async function getOtcWeekReportDetail(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getOtcWeekReportDetail,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function saveOtcWeekReport(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.saveOtcWeekReport,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function getOtcWeekReportTaskDetail(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getOtcWeekReportTaskDetail,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
