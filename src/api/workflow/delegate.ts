import { WorkflowPageParams, WorkflowPageResult } from './model/index';
import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  DelegatePage = '/workflow/delegate/page',
  Delegate = '/workflow/delegate',
  DelegateInfo = '/workflow/delegate/info',
}

/**
 * @description: 查询流程委托列表
 */

export async function getProcessDelegatePage(
  params: WorkflowPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<WorkflowPageResult>(
    {
      url: Api.DelegatePage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增流程委托
 */

export async function postDelegate(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Delegate,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 编辑流程委托
 */

export async function putDelegate(id, params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Delegate,
      params: { id, ...params },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除流程委托
 */
export async function deleteDelegate(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Delegate,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取流程委托详情
 */
export async function getDelegateInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.DelegateInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
