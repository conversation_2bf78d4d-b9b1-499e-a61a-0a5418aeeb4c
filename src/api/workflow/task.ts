import { FinishedTask, ApproveUserItem } from './model/index';
import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  BatchApproverUsersParams,
  FlowInfo,
  SubmittingProcessData,
  PostApprovalData,
  RelationTaskPage,
  rejectNodeItem,
  PostBatchApprovalData,
  BatchApproverInfo,
  GetBatchApprovalInfo,
  UrgeParams,
} from '/@/model/workflow/bpmnConfig';
import { BasicPageParams } from '../model/baseModel';

enum Api {
  StartProcessInfo = '/workflow/execute/start-process-info',
  Launch = '/workflow/execute/new-launch',
  reLaunch = '/workflow/execute/relaunch',
  RelationTasks = '/workflow/execute/relation-task/page',
  RelationTaskInfo = '/workflow/execute/relation-task/info',
  TaskInfo = '/workflow/execute/task-info',
  ProcessInfo = '/workflow/execute/process-info',
  FinishedTask = '/workflow/execute/process/finished-task',
  ApproveUserList = '/workflow/execute/approve-user',
  BatchSetApprover = '/workflow/execute/set-approve-multi',
  ApprovalProcessInfo = '/workflow/execute/approve-process-info',
  ViewProcessInfo = '/workflow/execute/view-process-info',
  Approval = '/workflow/execute/new-approve',
  BatchApproval = '/workflow/execute/approve/multi',
  BatchApprovalInfo = '/workflow/execute/approve/multi-info',
  Transfer = '/workflow/execute/transfer',
  RejectNode = '/workflow/execute/reject-node',
  SetSign = '/workflow/execute/set-sign',
  Withdraw = '/workflow/execute/my-task/withdraw',
  SetAssignee = '/workflow/execute/set-assignee',
  PushMessage = '/workflow/execute/message-send',
  Urge = '/workflow/execute/process-urge',
  Record = '/workflow/execute/process/deal/record',
  Staging = '/workflow/execute/process-staging',
}

/**
 * @description: 获取撤回节点列表
 */

export async function getRejectNodeList(
  processId: string,
  taskId: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<Array<any>>(
    {
      url: Api.RejectNode,
      params: { processId, taskId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 撤回
 */
export async function withdraw(processId, activityId, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Withdraw,
      params: { processId, activityId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取发起流程开始节点信息
 */
export async function getStartProcessInfo(schemaId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FlowInfo>(
    {
      url: Api.StartProcessInfo,
      params: { schemaId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取重新发起开始节点信息
 */
export async function getReStartProcessInfo(
  taskId: string,
  processId: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<FlowInfo>(
    {
      url: taskId ? Api.ApprovalProcessInfo : Api.ViewProcessInfo,
      params: { taskId, processId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 发起流程
 */
export async function postLaunch(
  schemaId: string,
  formData: Recordable,
  relationTasks: Array<{
    schemaId: string;
    taskId: string;
  }> = [],
  fileFolderIds: Array<string>,
  isOldSystem: Object,
  taskId?: string,
  draftId?: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<SubmittingProcessData>(
    {
      url: Api.Launch,
      params: { formData, schemaId, relationTasks, fileFolderIds, isOldSystem, taskId, draftId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 重新发起流程
 */
export async function reLaunch(
  processId: string,
  schemaId: string,
  formData: Recordable,
  relationTasks: Array<{
    schemaId: string;
    taskId: string;
  }> = [],
  fileFolderIds: Array<string>,
  isOldSystem: Object,
  isWithdraw: boolean,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<SubmittingProcessData>(
    {
      url: Api.reLaunch,
      params: {
        processId,
        formData,
        schemaId,
        relationTasks,
        fileFolderIds,
        isOldSystem,
        isWithdraw,
      },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 指定下一节点审批人(批量)
 */
export async function batchApproverUsers(
  schemaId: string,
  approveUserList: BatchApproverUsersParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<boolean>(
    {
      url: Api.BatchSetApprover,
      params: { schemaId, approveUserList },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 审批流程 信息
 */

export async function getApprovalProcess(
  taskId: string,
  processId: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<FlowInfo>(
    {
      url: taskId ? Api.ApprovalProcessInfo : Api.ViewProcessInfo,
      params: { taskId, processId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 审批流程
 */

export async function postApproval(params: PostApprovalData, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<SubmittingProcessData>(
    {
      url: Api.Approval,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 批量审批流程
 */

export async function getBatchApprovalInfo(
  params: GetBatchApprovalInfo,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<BatchApproverInfo>(
    {
      url: Api.BatchApprovalInfo,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 批量审批流程
 */

export async function postBatchApproval(
  params: PostBatchApprovalData,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<SubmittingProcessData>(
    {
      url: Api.BatchApproval,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 加签减签
 */

export async function postSetSign(
  schemaId: string,
  taskId: string,
  userIds: Array<string>,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<SubmittingProcessData>(
    {
      url: Api.SetSign,
      params: { schemaId, taskId, userIds },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 转办
 */

export async function postTransfer(
  taskId: string,
  userId: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<SubmittingProcessData>(
    {
      url: Api.Transfer,
      params: { taskId, userId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
//
/**
 * @description: 获取可以驳回的节点
 */
export async function getRejectNode(
  processId: string,
  taskId: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<Array<rejectNodeItem>>(
    {
      url: Api.RejectNode,
      params: { processId, taskId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询流程模板关联的任务分页
 */
export async function getRelationTasks(
  schemaId: string,
  relationSchemaId: string,
  params: BasicPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<RelationTaskPage>(
    {
      url: Api.RelationTasks,
      params: { schemaId, relationSchemaId, ...params },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询流程模板关联的任务 详情
 */
export async function getRelationTaskInfo(taskId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FlowInfo>(
    {
      url: Api.RelationTaskInfo,
      params: { taskId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询流程模任务 详情
 */
export async function getTaskInfo(taskId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FlowInfo>(
    {
      url: Api.TaskInfo,
      params: { taskId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据流程id查询流程任务 详情
 */
export async function getProcessInfo(processId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FlowInfo>(
    {
      url: Api.ProcessInfo,
      params: { processId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询已经完成节点
 */

export async function getFinishedTask(processId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FinishedTask>(
    {
      url: Api.FinishedTask,
      params: { processInstanceId: processId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取节点审批人
 */

export async function getApproveUserList(
  schemaId: string,
  taskId: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<Array<ApproveUserItem>>(
    {
      url: Api.ApproveUserList,
      params: { schemaId, taskId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 指派审核人（给任务添加审批人）
 */

export async function postSetAssignee(taskId, assignees, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.SetAssignee,
      params: { taskId, assignees },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 消息推送接口
 */

export async function MessageSend(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.PushMessage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 催办
 */
export async function processUrge(params: UrgeParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Urge,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取流程处理信息
 */

export async function getProcessRecord(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<Array<any>>(
    {
      url: Api.Record,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 暂存流程
 */

export async function stagingProcess(params: PostApprovalData, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Staging,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
