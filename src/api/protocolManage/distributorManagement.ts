import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  getContractList = '/business/contractDeliveryMerchant/page',
  addList = '/business/contractDeliveryMerchant/add',
  updateList = '/business/contractDeliveryMerchant/update',
  infoList = '/business/contractDeliveryMerchant/getById',
  deleteList = '/business/contractDeliveryMerchant/deleteById',
  accountList = '/business/contractDeliveryMerchant/accountSyncContract',
}

export async function getContractList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getContractList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function addFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.addList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function updateFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.updateList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function deleteFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.deleteList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function infoFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.infoList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function accountFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.accountList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}



