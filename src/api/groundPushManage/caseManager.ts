import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  getCaseCollectPageList = '/wechat/sign/getCaseCollectPageList',
  getCaseCollectDetail = '/wechat/sign/getCaseCollectDetail'
}
export async function getCaseCollectPageList(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.getCaseCollectPageList,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getCaseCollectDetail(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.getCaseCollectDetail,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
