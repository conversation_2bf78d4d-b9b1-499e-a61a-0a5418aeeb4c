export const recordTableColumns = {
  1: [
    {
      title: '序号',
      isHasIndex: true,
      key: 'No',
      align: 'center',
      width: 50,
    },
    {
      title: '员工姓名',
      dataIndex: 'personName',
      key: 'personName',
      align: 'center',
    },
    {
      title: 'EDP工号',
      dataIndex: 'personCode',
      key: 'personCode',
      align: 'center',
    },
    {
      title: '拜访类型',
      dataIndex: 'categoryName',
      key: 'categoryName',
      align: 'center',
    },
    {
      title: '客户名称',
      dataIndex: 'purMerchantName',
      key: 'purMerchantName',
      align: 'center',
    },
    {
      title: '客户地址',
      dataIndex: 'destination',
      key: 'destination',
      align: 'center',
    },
    {
      title: '签到时间',
      dataIndex: 'signDate',
      key: 'signDate',
      align: 'center',
    },
    {
      title: '签到结果',
      dataIndex: 'sign',
      key: 'sign',
      isSlot: true,
      slotName: 'sign',
      align: 'center',
    },
    {
      title: '签退时间',
      dataIndex: 'signOutDate',
      key: 'signOutDate',
      align: 'center',
    },
    {
      title: '签退结果',
      dataIndex: 'signOut',
      key: 'signOut',
      isSlot: true,
      slotName: 'sign',
      align: 'center',
    },
    {
      title: '在店时长',
      dataIndex: 'betWeenMinus',
      key: 'betWeenMinus',
      align: 'center',
      isSlot: true,
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
    },
  ],
  2: [
    {
      title: '序号',
      isHasIndex: true,
      key: 'No',
      align: 'center',
      width: 50,
    },
    {
      title: '协访人',
      dataIndex: 'personName',
      key: 'personName',
      align: 'center',
    },
    {
      title: 'EDP工号',
      dataIndex: 'coachedPersonCode',
      key: 'coachedPersonCode',
      align: 'center',
    },
    {
      title: '协访类型',
      dataIndex: 'categoryName',
      key: 'categoryName',
      align: 'center',
    },
    {
      title: '协访对象',
      dataIndex: 'coachedPersonName',
      key: 'coachedPersonName',
      align: 'center',
    },
    {
      title: 'EDP工号',
      dataIndex: 'personCode',
      key: 'personCode',
      align: 'center',
    },
    {
      title: '客户名称',
      dataIndex: 'purMerchantName',
      key: 'purMerchantName',
      align: 'center',
    },
    {
      title: '客户地址',
      dataIndex: 'destination',
      key: 'destination',
      align: 'center',
    },
    {
      title: '签到时间',
      dataIndex: 'signDate',
      key: 'signDate',
      align: 'center',
    },
    {
      title: '签到结果',
      dataIndex: 'sign',
      key: 'sign',
      isSlot: true,
      slotName: 'sign',
      align: 'center',
    },
    {
      title: '签退时间',
      dataIndex: 'signOutDate',
      key: 'signOutDate',
      align: 'center',
    },
    {
      title: '签退结果',
      dataIndex: 'signOut',
      key: 'signOut',
      isSlot: true,
      slotName: 'sign',
      align: 'center',
    },
    {
      title: '在店时长',
      dataIndex: 'betWeenMinus',
      key: 'betWeenMinus',
      align: 'center',
      isSlot: true,
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
    },
  ],
};

export const reasonList = [
  {
    name: '正常',
    value: 0,
  },
  {
    name: '超范围打卡',
    value: 1,
  },
  {
    name: '未打卡',
    value: 2,
  },
  {
    name: '迟到打卡',
    value: 3,
  },
  {
    name: '早退',
    value: 4,
  },
];
