<template>
  <ResizePageWrapper dense contentFullHeight fixedHeight contentClass="flex">
    <template #resizeLeft>
      <BasicTree
        :title="t('菜单分类')"
        :clickRowToExpand="true"
        :treeData="treeData"
        :fieldNames="{ key: 'id', title: 'none' }"
        @select="handleSelect"
        :selectedKeys="selectedKeys"
      >
        <template #headerTitle>
          <div class="flex justify-between items-center w-full">
            {{ t('菜单分类') }}
            <a-button type="primary" @click="handleAddCategory">
              {{ t('新增') }}
            </a-button>
          </div>
        </template>
        <template #title="title">
          <div class="flex justify-between w-full">
            {{ title.name }}
            <TableAction
              :actions="[
                {
                  icon: 'clarity:note-edit-line',
                  onClick: (event) => {
                    event.stopPropagation();
                    handleEditCategory(title);
                  },
                },
                {
                  icon: 'ant-design:delete-outlined',
                  color: 'error',
                  onClick: (event) => {
                    event.stopPropagation();
                    handleDeleteCategory(title.id);
                  },
                },
              ]"
            />
          </div>
        </template>
      </BasicTree>
    </template>
    <template #resizeRight>
      <BasicTable @register="registerTable">
        <template #toolbar>
          <a-button type="primary" v-auth="'appMenu:add'" @click="handleCreate">
            {{ t('新增菜单') }}
          </a-button>
        </template>
        <template #action="{ record, index }">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                auth: 'appMenu:edit',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                auth: 'appMenu:delete',
                color: 'error',
                popConfirm: {
                  title: t('是否确认删除'),
                  visible: popVisible(index),
                  onVisibleChange: handleVisibleChange.bind(null, index),
                  confirm: handleDelete.bind(null, record),
                  cancel: handleCancel,
                },
              },
            ]"
          />
        </template>
      </BasicTable>
      <MenuDrawer @register="registerDrawer" @success="handleSuccess" />
    </template>
    <MenuCategoryModal @register="registerModal" @success="handleCategorySuccess" />
  </ResizePageWrapper>
</template>
<script lang="ts">
  import { createVNode, defineComponent, h, onMounted, ref } from 'vue';

  import { BasicColumn, BasicTable, FormSchema, TableAction, useTable } from '/@/components/Table';
  import { deleteAppMenu, getAppMenuTree } from '/@/api/system/menu';
  import { ResizePageWrapper } from '/@/components/Page';
  import { useDrawer } from '/@/components/Drawer';
  import MenuDrawer from './components/MenuDrawer.vue';
  // import Icon from '/@/components/Icon';
  import { Modal, Tag } from 'ant-design-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { BasicTree, TreeItem } from '/@/components/Tree';
  import { deleteMenuCategory, menuCategory } from '/@/api/mobileDesign';
  import MenuCategoryModal from '/@/views/mobileDesign/menu/MenuCategoryModal.vue';
  import { useModal } from '/@/components/Modal';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

  const { t } = useI18n();

  export const columns: BasicColumn[] = [
    // {
    //   title: t('菜单名称'),
    //   dataIndex: 'title',
    //   width: 200,
    //   align: 'left',
    // },
    {
      title: t('菜单分类'),
      dataIndex: 'categoryName',
      align: 'left',
    },
    {
      title: t('菜单名称'),
      dataIndex: 'name',
      align: 'left',
    },
    {
      title: t('编码'),
      dataIndex: 'code',
      align: 'left',
    },
    // {
    //   title: t('图标'),
    //   dataIndex: 'icon',
    //   align: 'left',
    //   width: 50,
    //   customRender: ({ record }) => {
    //     return h(Icon, { icon: record.icon });
    //   },
    // },
    // {
    //   title: t('组件'),
    //   dataIndex: 'component',
    //   align: 'left',
    // },

    {
      title: t('状态'),
      dataIndex: 'enabledMark',
      width: 80,
      align: 'left',
      customRender: ({ record }) => {
        const enabledMark = record.enabledMark;
        const enable = ~~enabledMark === 1;
        const color = enable ? 'green' : 'red';
        const text = enable ? t('启用') : t('停用');
        return h(Tag, { color: color }, () => text);
      },
    },
    {
      title: t('备注'),
      dataIndex: 'remark',
      align: 'left',
      width: 180,
    },
  ];

  export const searchFormSchema: FormSchema[] = [
    {
      field: 'name',
      label: t('菜单名称'),
      component: 'Input',
      colProps: { lg: 8, md: 12, sm: 12 },
    },
    {
      field: 'code',
      label: t('	编码'),
      component: 'Input',
      colProps: { lg: 8, md: 12, sm: 12 },
    },
    {
      field: 'enabledMark',
      label: t('状态'),
      component: 'Select',
      componentProps: {
        options: [
          { label: t('启用'), value: 1 },
          { label: t('停用'), value: 0 },
        ],
      },
      colProps: { lg: 8, md: 12, sm: 12 },
    },
  ];

  export default defineComponent({
    name: 'MenuManagement',
    components: {
      MenuCategoryModal,
      BasicTable,
      MenuDrawer,
      TableAction,
      ResizePageWrapper,
      BasicTree,
    },
    setup() {
      const { notification } = useMessage();
      const { hasPermission } = usePermission();
      const selectedKeys = ref(['1673143239934681090']);
      const treeData = ref<TreeItem[]>([]);
      const selectParentId = ref('');
      const [registerDrawer, { openDrawer }] = useDrawer();
      const [registerTable, { reload }] = useTable({
        title: t('菜单列表'),
        api: getAppMenuTree,
        columns,
        rowKey: 'id',
        formConfig: {
          rowProps: {
            gutter: 16,
          },
          schemas: searchFormSchema,
          showResetButton: false,
        },
        pagination: {
          pageSize: 20,
        },
        striped: false,
        useSearchForm: true,
        showTableSetting: true,
        showIndexColumn: false,
        actionColumn: {
          width: 80,
          title: t('操作'),
          dataIndex: 'action',
          slots: { customRender: 'action' },
          fixed: undefined,
        },
        tableSetting: {
          size: false,
        },
        beforeFetch: (params) => {
          //发送请求默认新增  左边树结构所选机构id
          return { ...params, detailId: selectParentId.value };
        },
        customRow: (record) => {
          return {
            ondblclick: () => {
              if (hasPermission('appMenu:edit')) {
                handleEdit(record);
              }
            },
          };
        },
      });
      const isVisible = ref<boolean>(false);
      const deleteIndex = ref();
      const [registerModal, { openModal }] = useModal();

      onMounted(async () => {
        await getTreeData();
      });
      function handleCreate() {
        openDrawer(true, {
          isUpdate: false,
        });
      }
      function handleEdit(record: Recordable) {
        const childIds = [];
        openDrawer(true, {
          record,
          isUpdate: true,
          childIds,
        });
      }

      function handleDelete(record: Recordable) {
        isVisible.value = false;
        const permissionStore = usePermissionStore();
        deleteAppMenu([record.id]).then(async (_) => {
          await permissionStore.buildRoutesAction(false);
          reload();
          notification.success({
            message: t('提示'),
            description: t('删除成功'),
          }); //提示消息
        });
      }

      function handleSuccess() {
        reload();
      }

      function handleCancel() {
        isVisible.value = false;
      }

      function popVisible(index) {
        return isVisible.value && index === deleteIndex.value;
      }

      function handleVisibleChange(index, bool) {
        if (!bool) {
          isVisible.value = false;
          return;
        }
        if (import.meta.env.VITE_GLOB_PRODUCTION === 'true') {
          notification.warning({
            message: '在线环境暂不允许该操作，请联系管理员。',
          });
          return;
        }
        isVisible.value = true;
        deleteIndex.value = index;
      }

      function handleSelect(componentRow) {
        selectParentId.value = componentRow[0];
        reload();
      }
      async function getTreeData() {
        let res = (await menuCategory({})) as unknown as TreeItem[];
        treeData.value = res;
      }
      function handleAddCategory() {
        openModal(true, {
          isUpdate: false,
        });
      }
      function handleEditCategory(data) {
        openModal(true, {
          ...data,
          isUpdate: true,
        });
      }
      function handleDeleteCategory(id) {
        Modal.confirm({
          title: '提示信息',
          icon: createVNode(ExclamationCircleOutlined),
          content: '是否确认删除？',
          okText: '确认',
          cancelText: '取消',
          onOk() {
            deleteMenuCategory([id]).then((_) => {
              handleCategorySuccess();
              notification.success({
                message: 'Tip',
                description: t('删除成功！'),
              });
            });
          },
          onCancel() {},
        });
      }
      function handleCategorySuccess() {
        getTreeData();
      }
      return {
        registerTable,
        registerDrawer,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        handleCancel,
        handleVisibleChange,
        popVisible,
        t,
        selectedKeys,
        handleSelect,
        treeData,
        handleAddCategory,
        handleEditCategory,
        handleDeleteCategory,
        registerModal,
        openModal,
        handleCategorySuccess,
      };
    },
  });
</script>
