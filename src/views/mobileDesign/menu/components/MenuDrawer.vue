<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    destroyOnClose
    :title="getTitle"
    width="50%"
    @ok="handleSubmit"
    @visible-change="
      () => {
        activeKey = '1';
      }
    "
  >
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" :tab="t('菜单信息')">
        <BasicForm @register="registerForm" />
      </a-tab-pane>
      <!--      <a-tab-pane key="2" :tab="t('按钮信息')">-->
      <!--        <ButtonTable-->
      <!--          :menuId="rowId"-->
      <!--          :hasMetaFormId="hasMetaFormId"-->
      <!--          ref="buttonTable"-->
      <!--          v-if="activeKey"-->
      <!--        />-->
      <!--      </a-tab-pane>-->
      <!--      <a-tab-pane key="3" :tab="t('表格信息')">-->
      <!--        <ColumnTable :menuId="rowId" ref="columnTable" v-if="activeKey" />-->
      <!--      </a-tab-pane>-->
      <!--      <a-tab-pane key="4" :tab="t('表单信息')">-->
      <!--        <FormTable :menuId="rowId" ref="formTable" v-if="activeKey" />-->
      <!--      </a-tab-pane>-->
    </a-tabs>
  </BasicDrawer>
</template>
<script lang="ts">
  import { computed, defineComponent, ref } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicColumn } from '/@/components/Table';

  import ButtonTable from './ButtonTable.vue';
  import ColumnTable from './ColumnTable.vue';
  import FormTable from './FormTable.vue';
  import {
    addAppMenu,
    getAppMenuButtonById,
    getAppMenuColumnById,
    getAppMenuFormById,
    updateAppMenu,
  } from '/@/api/system/menu';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { MenuButtonModel } from '/@/api/system/menuButton/model';

  import { usePermissionStore } from '/@/store/modules/permission';
  import { menuCategory } from '/@/api/mobileDesign';

  const { t } = useI18n();

  const isUpdate = ref<boolean>(false);
  const componentType = ref<number>(0);
  const hasMetaFormId = ref(false);
  export const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: t('菜单名称'),
      component: 'Input',
      required: true,
    },
    {
      field: 'code',
      label: t('编码'),
      component: 'Input',
      required: true,
    },
    {
      field: 'sortCode',
      label: t('排序'),
      component: 'InputNumber',
      required: true,
    },
    {
      field: 'categoryId',
      label: t('菜单分类'),
      component: 'Select',
      required: true,
      componentProps: {
        fieldNames: {
          label: 'name',
          value: 'id',
        },
        getPopupContainer: () => document.body,
        allowClear: false,
      },
    },
    {
      field: 'url',
      label: t('路由地址'),
      component: 'Input',
      required: false,
      colProps: { lg: 24, md: 24 },
    },
    {
      field: 'icon',
      component: 'Upload2',
      label: '图标',
      colProps: {
        span: 8,
      },
      componentProps: {
        'list-type': 'picture-card',
        'max-count': 1,
        accept: 'image/*',
      },
    },
    {
      field: 'component',
      label: t('组件路径'),
      component: 'Input',
      colProps: { lg: 24, md: 24 },
      required: true,
      ifShow: ({ values }) => {
        return values.outLink === 0 && values.componentType === 0;
      },
    },
    {
      field: 'remark',
      label: t('备注'),
      component: 'InputTextArea',
      colProps: { lg: 24, md: 24 },
    },
    {
      field: 'enabledMark',
      label: t('状态'),
      component: 'RadioButtonGroup',
      componentProps: {
        options: [
          { label: t('启用'), value: 1 },
          { label: t('禁用'), value: 0 },
        ],
      },
    },
  ];

  export const columns: BasicColumn[] = [
    {
      title: t('按钮名称'),
      dataIndex: 'name',
      width: 100,
      align: 'left',
    },
    {
      title: t('编码'),
      dataIndex: 'code',
      width: 100,
    },
    {
      title: t('地址'),
      dataIndex: 'url',
      width: 200,
    },
    {
      title: t('请求方式'),
      dataIndex: 'method',
      width: 180,
      format: (text: string | number) => {
        if (text === 0) return 'GET';
        else if (text === 1) return 'POST';
        else if (text === 2) return 'PUT';
        else return 'DELETE';
      },
    },
  ];

  export default defineComponent({
    name: 'MenuDrawer',
    components: {
      BasicDrawer,
      BasicForm,
      FormTable,

      ButtonTable,
      ColumnTable,
    },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const permissionStore = usePermissionStore();
      const { notification } = useMessage();
      const rowId = ref('');
      const activeKey = ref('1');
      const buttonTable = ref();
      const columnTable = ref();
      const formTable = ref();
      const buttonDatas = ref<MenuButtonModel[]>([]);
      const columnDatas = ref<MenuButtonModel[]>([]);
      const formDatas = ref<MenuButtonModel[]>([]);
      const childIds = ref([]);
      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: { lg: 12, md: 24 },
      });

      const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
        resetFields();
        setDrawerProps({ confirmLoading: false });
        isUpdate.value = !!data?.isUpdate;

        if (isUpdate.value) {
          rowId.value = data.record.id;
          childIds.value = data.childIds;
          if (data.record && data.record.meta && data.record.meta.formId) {
            hasMetaFormId.value = true;
          }
          componentType.value = data.record.componentType;
          if (data.record.outLink == 1) {
            data.record.url = data.record.component;
          }
          if (data.record.parentId) {
            data.record.systemId = null;
          }
          setFieldsValue({
            ...data.record,
            icon: data.record.icon ? [{ url: data.record.icon }] : [],
          });
          getAppMenuButtonById({ menuId: data.record.id }).then((res) => {
            buttonDatas.value = res;
          });
          getAppMenuColumnById({ menuId: data.record.id }).then((res) => {
            columnDatas.value = res;
          });
          getAppMenuFormById({ menuId: data.record.id }).then((res) => {
            formDatas.value = res;
          });
        } else {
          rowId.value = '';
          componentType.value = 0;
          setFieldsValue({
            menuType: 1,
            enabledMark: 1,
            outLink: 0,
            display: 1,
            componentType: 0,
            systemId: import.meta.env.VITE_GLOB_PRODUCTION === 'true' ? '' : '1',
          });
        }

        const system = await menuCategory({});
        updateSchema([
          {
            field: 'categoryId',
            componentProps: {
              options: system,
            },
          },
        ]);
      });

      const getTitle = computed(() => (!isUpdate.value ? t('新增菜单') : t('编辑菜单')));

      async function handleSubmit() {
        try {
          const values = await validate();
          values.icon = values.icon && values.icon.length ? values.icon[0].url : null;
          if (values.outLink == 1) {
            values.component = values.url;
          }
          values.buttonList = buttonTable.value
            ? buttonTable.value.getDataSource()
            : buttonDatas.value;
          values.columnList = columnTable.value
            ? columnTable.value.getDataSource()
            : columnDatas.value;
          values.formList = formTable.value ? formTable.value.getDataSource() : formDatas.value;
          setDrawerProps({ confirmLoading: true });
          // TODO custom api
          if (!isUpdate.value) {
            //false 新增
            await addAppMenu(values);
            notification.success({
              message: t('提示'),
              description: t('新增菜单成功'),
            }); //提示消息
          } else {
            values.id = rowId.value;
            values.childIds = childIds.value;
            await updateAppMenu(values);
            notification.success({
              message: t('提示'),
              description: t('修改菜单成功'),
            }); //提示消息
          }
          await permissionStore.buildRoutesAction();
          closeDrawer();
          emit('success');
        } catch (error) {
          setDrawerProps({ confirmLoading: false });
        }
      }
      return {
        registerDrawer,
        registerForm,
        getTitle,
        hasMetaFormId,
        handleSubmit,
        buttonTable,
        rowId,
        columnTable,
        formTable,
        activeKey,
        t,
      };
    },
  });
</script>
