<template>
  <div class="box">
    <BasicTree
      :title="t('菜单分类')"
      :clickRowToExpand="true"
      :treeData="treeData.list"
      :fieldNames="{ key: 'id', title: 'name' }"
      @select="handleSelect"
      :selectedKeys="treeData.selectedKeys"
    />
  </div>
</template>
<script lang="ts">
  import { defineComponent, onMounted, reactive } from 'vue';
  import { BasicTree, TreeItem } from '/@/components/Tree';
  import { getDicDetailList } from '/@/api/system/dic';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export default defineComponent({
    name: 'DeptTree',
    components: { BasicTree },

    emits: ['select'],
    setup(_, { emit }) {
      const treeData = reactive({
        list: [],
        selectedKeys: [],
      });

      async function fetch() {
        treeData.list = (await getDicDetailList({
          itemId: '1673142942973763585',
        })) as unknown as TreeItem[];
        let id = treeData.list[0].id;
        treeData.selectedKeys.push(id);
        emit('select', id);
      }

      function handleSelect(keys: string) {
        emit('select', keys[0]);
      }

      onMounted(() => {
        fetch();
      });
      return { treeData, handleSelect, t };
    },
  });
</script>
<style lang="less" scoped>
  .box {
    height: 100vh;
  }
</style>
