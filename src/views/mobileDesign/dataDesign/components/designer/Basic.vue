<template>
  <div class="basic-box">
    <div class="form-box">
      <BasicForm @register="register" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, onMounted } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';

  import { MobileBasicData } from '/@/model/mobileDesign/designer';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const formSchemaCode: FormSchema[] = [
    {
      field: 'name',
      label: t('页面名称'),
      required: true,
      component: 'Input',
      title: t('基本信息'),
      colProps: { span: 24 },
      componentProps: {
        placeholder: t('请填写页面名称'),
      },
    },
    {
      field: 'code',
      label: t('页面编码'),
      required: true,
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: t('请填写页面编码'),
      },
      rules: [
        {
          pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
          message: t('页面编码只能是数字、字母和下划线组成，必须以英文字母开头'),
        },
      ],
      dynamicDisabled: () => {
        return props.isEdit;
      },
    },
    {
      field: 'isMenu',
      label: t('菜单'),
      component: 'Switch',
      required: false,
      colProps: { span: 24 },
      componentProps: ({ formModel }) => {
        return {
          checkedValue: 1,
          unCheckedValue: 0,
          onChange: () => {
            formModel.categoryId = null;
            formModel.icon = '';
            formModel.sortCode = 0;
          },
        };
      },
    },
    {
      field: 'categoryId',
      label: t('功能类别'),
      component: 'DicSelect',
      required: true,
      defaultValue: null,
      componentProps: {
        placeholder: t('请选择功能类别'),
        itemId: '1673142942973763585',
        isDefaultValue: true,
      },
      colProps: { span: 24 },
      ifShow: ({ values }) => {
        return !!values.isMenu;
      },
    },
    {
      field: 'icon',
      label: t('图标'),
      component: 'IconPicker',
      required: false,
      colProps: { span: 24 },
      componentProps: {
        placeholder: t('请选择图标'),
      },
      ifShow: ({ values }) => {
        return !!values.isMenu;
      },
    },
    {
      field: 'sortCode',
      label: t('排序'),
      component: 'InputNumber',
      required: false,
      colProps: { span: 24 },
      componentProps: {
        placeholder: t('请填写排序'),
      },
      ifShow: ({ values }) => {
        return !!values.isMenu;
      },
    },
    {
      field: 'remark',
      label: t('备注'),
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: t('请填写备注'),
      },
    },
  ];
  const [register, { validate, getFieldsValue, clearValidate, setFieldsValue }] = useForm({
    labelWidth: 100,
    schemas: formSchemaCode,
    showActionButtonGroup: false,
  });
  const props = withDefaults(
    defineProps<{
      basicData: MobileBasicData;
      isEdit: boolean;
    }>(),
    {
      basicData: () => {
        return {
          code: '', //编码
          name: '', //名称
          icon: '', //图标
          categoryId: null,
          sortCode: 1, //排序
          isMenu: 0, //菜单
          remark: '', //描述
        };
      },
      isEdit: false,
    },
  );

  onMounted(() => {
    nextTick(() => {
      setFieldsValue(props.basicData);
      clearValidate();
    });
  });

  defineExpose({
    validate,
    getFieldsValue,
  });
</script>

<style lang="less" scoped>
  .basic-box {
    height: 100%;
    padding: 24px;
  }

  .form-box {
    height: calc(100vh - 114px);
    width: 100%;
    max-width: 794px;
    overflow: hidden auto;
    background-color: #fff;
    border-radius: 4px;
    margin: auto;
    padding: 24px;
  }
</style>
