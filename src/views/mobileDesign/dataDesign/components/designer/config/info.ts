import {
  defaultProperties,
  dashboardProperties,
  chartLineProperties,
  pieProperties,
  radarProperties,
  gaugeProperties,
  funnelProperties,
  chartBarProperties,
  myTaskProperties,
  todoListProperties,
  modulesProperties,
  bannerProperties,
} from './properties';
import { DesktopComponent } from '/@/enums/desktop';
import {
  ChartBarInfo,
  DesktopInfoItem,
  MixLineBarInfo,
  PieItemInfo,
} from '/@/model/mobileDesign/designer';
// 默认属性
export const defaultInfo = {
  i: '0',
  type: DesktopComponent.DASHBOARD,
  config: {
    ...defaultProperties,
  },
};

//数据面板
export const dashboardInfo = {
  i: '0',
  type: DesktopComponent.DASHBOARD,
  config: {
    ...dashboardProperties,
  },
};

//Banner图
export const bannerInfo = {
  i: '0',
  h: 260,
  type: DesktopComponent.BANNER,
  config: {
    ...bannerProperties,
  },
};

//折线/柱状图
export const chartLineInfo: MixLineBarInfo = {
  i: '0',
  h: 260,
  type: DesktopComponent.CHARTLINE,
  config: chartLineProperties,
};

//饼图
export const pieInfo: PieItemInfo = {
  i: '0',
  h: 400,
  type: DesktopComponent.PIE,
  config: {
    ...pieProperties,
  },
};

//雷达图

export const radarInfo = {
  i: '0',
  h: 400,
  type: DesktopComponent.RADAR,
  config: {
    ...radarProperties,
  },
};

//仪表盘

export const gaugeInfo = {
  i: '0',
  h: 300,
  type: DesktopComponent.GAUGE,
  config: {
    ...gaugeProperties,
  },
};

//漏斗图

export const funnelInfo = {
  i: '0',
  h: 400,
  type: DesktopComponent.FUNNEL,
  config: {
    ...funnelProperties,
  },
};

//柱状百分比

export const chartBarInfo: ChartBarInfo = {
  i: '0',
  h: 500,
  type: DesktopComponent.CHARTBAR,
  config: {
    ...chartBarProperties,
  },
};
//我的任务

export const myTaskInfo = {
  i: '0',
  type: DesktopComponent.MYTASK,
  config: {
    ...myTaskProperties,
  },
};

//待办事项

export const todoListInfo = {
  i: '0',
  type: DesktopComponent.TODOLIST,
  config: {
    ...todoListProperties,
  },
};

//常用功能

export const modulesInfo = {
  i: '0',
  type: DesktopComponent.MODULES,
  config: {
    ...modulesProperties,
  },
};

// 获取组件属性
export const InfoByType: Map<DesktopComponent, DesktopInfoItem> = new Map([
  [DesktopComponent.DASHBOARD, dashboardInfo],
  [DesktopComponent.BANNER, bannerInfo],
  [DesktopComponent.CHARTLINE, chartLineInfo],
  [DesktopComponent.PIE, pieInfo],
  [DesktopComponent.RADAR, radarInfo],
  [DesktopComponent.GAUGE, gaugeInfo],
  [DesktopComponent.FUNNEL, funnelInfo],
  [DesktopComponent.CHARTBAR, chartBarInfo],
  [DesktopComponent.MYTASK, myTaskInfo],
  [DesktopComponent.TODOLIST, todoListInfo],
  [DesktopComponent.MODULES, modulesInfo],
]);
