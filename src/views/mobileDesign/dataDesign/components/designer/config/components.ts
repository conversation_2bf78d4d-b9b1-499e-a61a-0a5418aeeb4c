import { DesktopComponent } from '/@/enums/desktop';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();

export const componentTitleFromType = new Map([
  [DesktopComponent.DASHBOARD, t('数据指标')],
  [DesktopComponent.BANNER, t('Banner图')],
  [DesktopComponent.CHARTLINE, t('折线/柱状图')],
  [DesktopComponent.PIE, t('饼图')],
  [DesktopComponent.RADAR, t('雷达图')],
  [DesktopComponent.GAUGE, t('仪表盘')],
  [DesktopComponent.FUNNEL, t('漏斗图')],
  [DesktopComponent.CHARTBAR, t('柱状百分比')],
  [DesktopComponent.MYTASK, t('我的任务')],
  [DesktopComponent.TODOLIST, t('待办事项')],
  [DesktopComponent.MODULES, t('常用功能')],
]);

export const basicComponents = [
  {
    type: DesktopComponent.DASHBOARD,
    label: t('数据'),
    icon: 'icon2',
  },
  {
    type: DesktopComponent.BANNER,
    label: t('Banner'),
    icon: 'icon3',
  },
];

export const chartComponents = [
  {
    type: DesktopComponent.CHARTLINE,
    label: t('折/柱'),
    icon: 'icon4',
  },
  {
    type: DesktopComponent.PIE,
    label: t('饼图'),
    icon: 'icon5',
  },
  {
    type: DesktopComponent.RADAR,
    label: t('雷达图'),
    icon: 'icon6',
  },
  {
    type: DesktopComponent.GAUGE,
    label: t('仪表盘'),
    icon: 'icon7',
  },
  {
    type: DesktopComponent.FUNNEL,
    label: t('漏斗图'),
    icon: 'icon8',
  },
  {
    type: DesktopComponent.CHARTBAR,
    label: t('百分比'),
    icon: 'icon9',
  },
];

export const systemComponents = [
  {
    type: DesktopComponent.MYTASK,
    label: t('任务'),
    icon: 'icon10',
  },
  {
    type: DesktopComponent.TODOLIST,
    label: t('待办'),
    icon: 'icon11',
  },
  {
    type: DesktopComponent.MODULES,
    label: t('功能'),
    icon: 'icon12',
  },
];
