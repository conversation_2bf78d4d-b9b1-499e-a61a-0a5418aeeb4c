import {
  DesktopComponent,
  LegendType,
  OrientType,
  RadarShapeType,
  JumpToPathType,
} from '/@/enums/desktop';
import {
  ChartBarConfig,
  ChartLegend,
  DesktopConfig,
  PieConfig,
} from '/@/model/mobileDesign/designer';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();
// 默认属性
export const defaultProperties = {
  title: '',
  renderKey: 0,
};
export const legendProperties: ChartLegend = {
  show: true,
  orient: OrientType.HORIZONTAL,
  left: 'center',
  top: 'bottom',
  right: 'auto',
  bottom: 'bottom',
  padding: 8,
  icon: LegendType.CIRCLE,
  itemWidth: 25,
  itemHeight: 14,
  textStyle: {
    color: '#000',
  },
  formatter: '{name}',
  position: 3,
};

//  数据视图属性
export const apiConfigInfo = {
  apiData: {},
  apiColumns: [],
  apiConfig: {
    id: '',
    name: '',
    method: '',
    path: '',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};
//  数据视图属性
export const dataViewInfo = {
  labelKey: '',
  valueKey: '',
};
//
//数据面板
export const dashboardProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  title: t('数据面板'),
  numColor: '#666666',
  labelColor: '#999999',
  dashboard: [
    {
      name: '主营收入(元)',
      field: 'value1',
    },
    {
      name: '售卡金额(元)',
      field: 'value2',
    },
  ],
  apiConfig: {
    id: '9555bdda7b4b46408871ebc772f1f982',
    method: 'GET',
    name: '数据面板',
    path: 'MobileDesign/data-panel',
    requestParamsConfigs: [],
    requestHeaderConfigs: [],
    requestBodyConfigs: [],
  },
};
//banner
export const bannerProperties = {
  ...defaultProperties,
  title: t('Banner图'),
  imgs: [
    {
      url: '/src/assets/images/design/logo.png',
      index: 1,
    },
    {
      url: '/src/assets/images/design/logo.png',
      index: 2,
    },
    {
      url: '/src/assets/images/design/logo.png',
      index: 3,
    },
    {
      url: '/src/assets/images/design/logo.png',
      index: 4,
    },
  ],
};
//折线/柱状图
export const chartLineIndicatorConfig = {
  //title: '',
  type: 'line',
  color: '',
  name: '',
  value: '',
  showAreaStyle: false, //是否显示面积图
  gradualStartColor: '',
  gradualEndColor: '',
};
export const chartLineDataListsConfig = {
  title: '',
  valueKey: '',
  apiColumns: [],
  apiData: [],
  total: 0,
  indicator: [chartLineIndicatorConfig],
  apiConfig: {
    id: '',
    name: '',
    method: '',
    path: '',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};
export const chartLineSeriesConfig = {
  name: '',
  type: 'line',
  stack: '',
  smooth: false,
  areaStyle: {
    color: '',
  },
  label: {
    show: false,
    color: '#000000',
    position: 'outside',
    fontWeight: 'normal',
    fontSize: 12,
    formatter: null,
  },
  data: [],
  showAreaStyle: false,
  gradualEndColor: '',
  gradualStartColor: '',
};
export const chartLineEchartsConfig = {
  color: [],
  legend: {
    data: [],
  },
  tooltip: {
    trigger: 'axis',
  },
  grid: {
    left: '10',
    right: '10',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisLine: {
        show: true,
        lineStyle: {
          color: '',
        },
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: '',
        },
      },
    },
  ],
  series: [chartLineSeriesConfig],
};
export const chartLineYAxis = {
  name: '',
  nameLocation: 'end',
  nameTextStyle: {
    color: '',
    fontSize: 12,
    fontWeight: 'normal',
  },
  // min: 'dataMin',
  // max: 'dataMax',
  interval: null,
  position: 'left',
  type: 'value',
  axisLabel: {
    formatter: '{value}',
    color: '',
  },
  alignTicks: true,
  axisLine: {
    show: true,
    lineStyle: {
      color: '',
    },
  },
};
export const chartLineProperties = {
  ...defaultProperties,
  title: t('折线/柱状图'),
  condition: {
    color: '',
    selected: '',
  },
  legend: {
    ...legendProperties,
  },
  // 统计
  count: {
    show: false,
    unit: '',
    title: '',
  },
  dataList: [
    {
      title: '',
      valueKey: 'name',
      apiColumns: [],
      apiData: [],
      total: 0,
      indicator: [
        {
          title: '',
          type: 'line',
          color: '',
          name: '',
          value: 'quantity',
          showAreaStyle: false, //是否显示面积图
          gradualStartColor: '',
          gradualEndColor: '',
        },
      ],
      apiConfig: {
        id: 'f04205ad5aeb45f2a0fed431ac11227c',
        method: 'GET',
        name: '图表数据',
        path: 'MobileDesign/chart',
        requestParamsConfigs: [],
        requestHeaderConfigs: [],
        requestBodyConfigs: [],
      },
    },
  ],
  bar: {
    stack: false, //是否显示堆叠
    label: {
      show: true,
      color: '',
    },
  },
  line: {
    smooth: false,
    stack: false, //是否显示堆叠
    showAreaStyle: false, //是否显示面积图
    gradualStartColor: '',
    gradualEndColor: '',
    showSymbol: true,
  },
  label: {
    show: false,
    color: '#000000',
    // position: 'outside',
    // fontWeight: 'normal',
    fontSize: 12,
    formatter: '',
  },
  yAxis: [chartLineYAxis],
  xAxis: [
    {
      position: 'bottom',
      name: '',
      nameLocation: 'end',
      nameTextStyle: {
        color: '',
        fontSize: 12,
        fontWeight: 'normal',
      },
      type: 'category',
      axisLabel: {
        formatter: '{value}',
        color: '',
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '',
        },
      },
      data: [],
    },
  ],
  echarts: [chartLineEchartsConfig],
  // isCrosswise: false,
};
//图
export const chartProperties: PieConfig = {
  ...defaultProperties,
  ...apiConfigInfo,
  ...dataViewInfo,
  colors: [],
  title: t('饼图'),
  echarts: {
    alignTicks: true,
  },
};
//饼图
export const pieProperties: PieConfig = {
  ...defaultProperties,
  ...apiConfigInfo,
  colors: [],
  title: t('饼图'),
  autoWidth: true,
  labelKey: 'name',
  valueKey: 'quantity',
  echarts: {
    alignTicks: true,
    legend: {
      ...legendProperties,
      width: 'auto',
    },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        type: 'pie',
        roseType: undefined,
        selectedMode: undefined,
        radius: [0, '50%'],
        center: ['50%', '50%'],
        label: {
          show: true,
          color: '#000000',
          // position: 'outside',
          // fontWeight: 'normal',
          fontSize: 12,
          formatter: '{b}: {d}',
        },
        itemStyle: {
          borderRadius: 0,
        },
        data: [],
      },
    ],
  },
  apiConfig: {
    id: 'f04205ad5aeb45f2a0fed431ac11227c',
    method: 'GET',
    name: '图表数据',
    path: 'MobileDesign/chart',
    requestParamsConfigs: [],
    requestHeaderConfigs: [],
    requestBodyConfigs: [],
  },
};

//雷达图

export const radarProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  labelKey: null,
  title: t('雷达图'),
  colors: [],
  echarts: {
    alignTicks: true,
    legend: {
      data: [],
      ...legendProperties,
      formatter: '',
    },
    radar: {
      radius: 100,
      shape: RadarShapeType.CIRCLE,
      indicator: [
        { value: 'quantity', name: '指标1' },
        { value: 'price', name: '指标2' },
        { value: 'unitPrice', name: '指标3' },
      ],
    },
    showAreaStyle: 0,
    series: [
      {
        type: 'radar',
        itemStyle: {
          borderRadius: 0,
        },
        label: {
          show: false,
          //color: '#000000',
          //position: 'outside',
          //fontWeight: 'normal',
          //fontSize: 12,
          //formatter: '',
        },
        symbol: LegendType.CIRCLE,
        areaStyle: {},
        data: [
          {
            name: t('指标1'),
            value: [],
          },
          {
            name: t('指标2'),
            value: [],
          },
          {
            name: t('指标3'),
            value: [],
          },
        ],
      },
    ],
  },
  apiConfig: {
    id: 'f04205ad5aeb45f2a0fed431ac11227c',
    method: 'GET',
    name: '图表数据',
    path: 'MobileDesign/chart',
    requestParamsConfigs: [],
    requestHeaderConfigs: [],
    requestBodyConfigs: [],
  },
};

//仪表盘

export const gaugeProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  title: t('仪表盘'),
  valueKey: 'quantity',
  echarts: {
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c}',
    },
    series: [
      {
        name: t('仪表盘'),
        type: 'gauge',
        progress: {
          show: true,
        },
        data: [
          {
            name: t('仪表盘'),
            value: 0,
          },
        ],
      },
    ],
  },
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};

//漏斗图

export const funnelProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  title: t('漏斗图'),
  labelKey: 'name',
  valueKey: 'price',
  colors: [],
  autoWidth: true,
  echarts: {
    alignTicks: true,
    legend: {
      ...legendProperties,
      width: 'auto',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c}',
    },
    series: [
      {
        name: '',
        type: 'funnel',
        left: 40,
        right: 40,
        top: 60,
        bottom: 100,
        //width: '80%',
        // min: 0,
        // max: 100,
        // minSize: '0%',
        // maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          fontSize: 12,
          color: '#000',
          position: 'left',
          formatter: '{c}',
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid',
          },
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
        },
        emphasis: {
          label: {
            fontSize: 20,
          },
        },
        data: [],
      },
    ],
  },
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};

//柱状百分比

export const chartBarProperties: ChartBarConfig = {
  ...defaultProperties,
  ...apiConfigInfo,
  labelKey: 'name',
  valueKey: 'unitPrice',
  targetKey: 'price', //目标值
  title: t('柱状百分比'),
  unit: t('万元'),
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};

//我的任务

export const myTaskProperties = {
  ...defaultProperties,
  title: t('我的任务'),
};

//待办事项

export const todoListProperties = {
  ...defaultProperties,
  title: t('待办事项'),
  maxRows: 2,
  path: JumpToPathType.PROCESSTASKS,
};

//常用功能

export const modulesProperties = {
  ...defaultProperties,
  title: t('常用功能'),
  isShowTitle: true,
  functions: [
    {
      id: '',
      name: '多租户',
      url: '',
      color: '#5e95ff',
      icon: 'fa-solid:users',
    },
    { id: '', name: '云存储', url: '', color: '#5e95ff', icon: 'zondicons:cloud' },
    { id: '', name: '微服务', url: '', color: '#5e95ff', icon: 'solar:link-circle-line-duotone' },
    { id: '', name: '销售汇报', url: '', color: '#5e95ff', icon: 'fontisto:bar-chart' },
  ],
};

// 获取组件属性
export const propertiesByType: Map<DesktopComponent, DesktopConfig> = new Map([
  [DesktopComponent.DASHBOARD, dashboardProperties],
  [DesktopComponent.BANNER, bannerProperties],
  [DesktopComponent.CHARTLINE, chartLineProperties],
  [DesktopComponent.PIE, pieProperties],
  [DesktopComponent.RADAR, radarProperties],
  [DesktopComponent.GAUGE, gaugeProperties],
  [DesktopComponent.FUNNEL, funnelProperties],
  [DesktopComponent.CHARTBAR, chartBarProperties],
  [DesktopComponent.MYTASK, myTaskProperties],
  [DesktopComponent.TODOLIST, todoListProperties],
  [DesktopComponent.MODULES, modulesProperties],
]);
