<template>
  <div class="item-title" v-if="title">
    <div class="title-style">{{ title }}</div>
  </div>
</template>

<script setup lang="ts">
  withDefaults(
    defineProps<{
      title: string | null;
    }>(),
    {
      title: null,
    },
  );
</script>

<style lang="less" scoped>
  .item-title {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: #666;
    font-size: 14px;
    padding-left: 10px;
    font-weight: 700;

    .title-style {
      margin: 12px 0 0;
      line-height: 18px;
      padding-left: 6px;
      border-left: 6px solid #5e95ff;
    }
  }
</style>
