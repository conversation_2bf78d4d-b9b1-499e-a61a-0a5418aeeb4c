<template>
  <Box v-if="data.show" style="margin-top: 60px">
    <a-form-item :label="t('标题')" :colon="false" labelAlign="right">
      <a-input v-model:value="data.info.config.title" />
    </a-form-item>
    <a-form-item label="显示标题" :colon="false" labelAlign="right">
      <a-switch v-model:checked="data.info.config.isShowTitle" />
    </a-form-item>
    <BasicTable @register="registerTable" class="dataAuthDrag">
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="['name', 'color'].includes(column.dataIndex)">
          <div>
            <TreeSelect
              v-if="column.dataIndex == 'name'"
              v-model:value="record.id"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :placeholder="t('请选择菜单')"
              allow-clear
              tree-default-expand-all
              :tree-data="treeData"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              @select="
                (_, node) => {
                  record.name = node.name;
                  record.icon = node.icon;
                  record.url = node.url;
                  changeData();
                }
              "
            />
            <SelectColor
              v-else-if="column.dataIndex == 'color'"
              v-model:value="record[column.dataIndex]"
              @change="changeData"
            />
          </div>
        </template>
        <template v-else-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                title: t('删除'),
                popConfirm: {
                  title: t('是否确认删除'),
                  confirm: deleteFunction.bind(null, index),
                },
              },
            ]"
          />
        </template>
        <template v-else> {{ text }} </template>
      </template>
    </BasicTable>
    <div class="ml-5px">
      <a-button @click="add" class="w-full mb-6"> <plus-outlined />{{ t('新增') }} </a-button>
    </div>
  </Box>
</template>

<script setup lang="ts">
  import { nextTick, onMounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import { TreeSelect } from 'ant-design-vue';
  import { modulesInfo } from '../config/info';
  import SelectColor from './SelectColor.vue';

  import { ModulesInfo, functionItem } from '/@/model/mobileDesign/designer';

  import { PlusOutlined } from '@ant-design/icons-vue';

  import { BasicTable, useTable, TableAction, BasicColumn } from '/@/components/Table';

  import { useI18n } from '/@/hooks/web/useI18n';
  import { getAppFuncList, getDicDetailList } from '/@/api/system/dic';
  const { t } = useI18n();
  const tableData = ref<functionItem[]>([]);
  const props = withDefaults(
    defineProps<{
      info: ModulesInfo;
    }>(),
    {
      info: () => {
        return modulesInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  watch(
    () => tableData.value,
    (val) => {
      if (val) data.info.config.functions = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    show: boolean;
    info: ModulesInfo;
    renderKey: number;
  } = reactive({
    show: false,
    info: modulesInfo,
    renderKey: 0,
  });

  const columns: BasicColumn[] = [
    {
      title: t('绑定功能'),
      dataIndex: 'name',
      width: 100,
    },
    {
      title: t('绑定颜色'),
      dataIndex: 'color',
      width: 100,
    },
  ];
  const [registerTable, { getDataSource, setTableData }] = useTable({
    title: '',
    dataSource: tableData.value,
    columns,
    pagination: false,
    striped: false,
    useSearchForm: false,
    bordered: true,
    showIndexColumn: false,
    canResize: false,
    actionColumn: {
      width: 40,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });
  const treeData: any = ref([
    {
      name: '流程应用',
      disabled: true,
      id: 1,
      children: [
        {
          id: 2,
          name: '发起流程',
          type: 'purple',
          icon: 'ant-design:apartment-outlined',
          url: '/pages/workflow/launchList',
        },
        {
          id: 3,
          name: '我的待办',
          type: 'green',
          icon: 'ant-design:exception-outlined',
          url: '/pages/workflow/index?type=todo',
        },
        {
          id: 4,
          name: '我的传阅',
          type: 'orange',
          icon: 'ant-design:read-outlined',
          url: '/pages/workflow/index?type=circulated',
        },
        {
          id: 5,
          name: '我的任务',
          type: 'cyan',
          icon: 'ant-design:file-done-outlined',
          url: '/pages/workflow/index?type=my',
        },
        {
          id: 6,
          name: '我的已办',
          type: 'blue',
          icon: 'ant-design:bars-outlined',
          url: '/pages/workflow/index?type=finished',
        },
      ],
    },
  ]);
  onMounted(() => {
    data.info = props.info;
    nextTick(() => {
      setTableData(data.info.config.functions);
    });
    getDicDetailList({ itemId: '1673142942973763585' }).then((res) => {
      console.log(res);

      res.forEach(async (k) => {
        let obj = {
          name: k.name,
          disabled: true,
          children: [] as any,
          id: k.id,
        };
        await getAppFuncList({ categoryId: k.id }).then((rs) => {
          rs.forEach((o) => {
            obj.children.push({
              name: o.name,
              type: 'blue',
              icon: o.icon,
              url: o.url,
              id: o.id,
            });
          });
        });
        treeData.value.push(obj);
      });
    });

    data.show = true;
  });
  function add() {
    let data: functionItem[] = getDataSource();
    data.push({
      id: '',
      name: '',
      icon: '',
      url: '',
      color: '#5e95ff',
    });
    setTableData(data);
    tableData.value = data;
  }
  function deleteFunction(index: number) {
    tableData.value.splice(index, 1);
    setTableData(tableData.value);
    data.renderKey++;
  }
  function changeData() {
    data.info.config.functions = getDataSource();
  }
</script>

<style lang="less" scoped>
  .card-box {
    .card-item {
      position: relative;
      margin: 10px 0;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
    }

    .close-icon {
      position: absolute;
      top: -10px;
      right: -6px;
    }
  }
</style>
