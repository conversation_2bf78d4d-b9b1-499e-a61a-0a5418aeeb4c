<template>
  <a-form :label-col="labelCol">
    <a-form-item :label="t('高度')" :colon="false" labelAlign="left">
      <a-input-number v-model:value="data.info" @change="changeInfo" />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
  import { reactive, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const labelCol = { style: { width: '70px' } };
  const props = withDefaults(
    defineProps<{
      info: number;
    }>(),
    {
      info: () => 260,
    },
  );

  const emit = defineEmits(['update:info']);
  const data = reactive({
    info: props.info,
  });
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  function changeInfo() {
    emit('update:info', data.info);
  }
</script>

<style scoped></style>
