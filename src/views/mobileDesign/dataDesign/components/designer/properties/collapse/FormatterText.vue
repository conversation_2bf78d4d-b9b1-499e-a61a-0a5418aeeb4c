<template>
  <a-tooltip :title="props.formatterText" color="#545454">
    <Icon
      icon="ant-design:question-circle-outlined"
      :size="20"
      color="#8c8c8c"
      style="display: inline-flex; align-items: center; justify-content: center"
    />
  </a-tooltip>
</template>

<script setup lang="ts">
  import Icon from '/@/components/Icon/index';
  const props = withDefaults(
    defineProps<{
      formatterText: string;
    }>(),
    {
      formatterText: '',
    },
  );
</script>

<style scoped></style>
