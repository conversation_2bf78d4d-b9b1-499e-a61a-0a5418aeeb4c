<template>
  <a-form :label-col="labelCol">
    <a-form-item :label="t('是否显示')" :colon="false" labelAlign="left">
      <a-switch v-model:checked="data.label.show" @change="changeLabel" />
    </a-form-item>
    <template v-if="data.label.show">
      <a-form-item
        :label="t('文本颜色')"
        :colon="false"
        labelAlign="left"
        v-if="data.label.color !== undefined"
      >
        <SelectColor v-model:value="data.label.color" @change="changeLabel" />
      </a-form-item>
      <a-form-item
        :label="t('文本位置')"
        :colon="false"
        labelAlign="left"
        v-if="data.label.position !== undefined"
      >
        <a-select
          v-model:value="data.label.position"
          style="width: 100%"
          allowClear
          @change="changeLabel"
        >
          <a-select-option
            v-for="(item, index) in positionOptions || positions"
            :key="index"
            :value="item.value"
            >{{ item.label }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item
        :label="t('文本粗细')"
        :colon="false"
        labelAlign="left"
        v-if="data.label.fontWeight !== undefined"
      >
        <a-select
          v-model:value="data.label.fontWeight"
          style="width: 100%"
          allowClear
          @change="changeLabel"
        >
          <a-select-option
            v-for="(item, index) in fontWeightOptions"
            :key="index"
            :value="item.value"
            >{{ item.label }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item
        :label="t('字体大小')"
        :colon="false"
        labelAlign="left"
        v-if="data.label.fontSize !== undefined"
      >
        <a-input-number v-model:value="data.label.fontSize" :min="0" @change="changeLabel" />
      </a-form-item>
      <a-form-item
        :label="t('格式文本')"
        :colon="false"
        labelAlign="left"
        v-if="data.label.formatter != undefined"
      >
        <div class="flex">
          <a-input v-model:value="data.label.formatter" @change="changeLabel" />
          <FormatterText :formatterText="props.formatterText" />
        </div>
      </a-form-item>
    </template>
  </a-form>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import SelectColor from './../SelectColor.vue';
  import FormatterText from './FormatterText.vue';
  import { ChartLabel } from '/@/model/mobileDesign/designer';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const labelCol = { style: { width: '70px' } };
  const props = withDefaults(
    defineProps<{
      label: ChartLabel;
      formatterText: string;
      positionOptions?: Array<any>;
    }>(),
    {
      label: () => {
        return {
          show: false,
          color: '#000000',
          position: 'outside',
          fontWeight: 'normal',
          fontSize: 12,
          formatter: null,
        };
      },
      formatterText: '',
    },
  );

  const emit = defineEmits(['update:label', 'change']);
  const data = reactive({
    label: props.label,
  });
  function changeLabel() {
    if (!data.label.formatter?.trim()) data.label.formatter = null; //如果formatter为空字符串 文本标签无效
    emit('update:label', data.label);
    emit('change');
  }
  const positions = [
    {
      label: t('外侧'),
      value: 'outside',
    },
    {
      label: t('内部'),
      value: 'inside',
    },
    {
      label: t('中心'),
      value: 'center',
    },
  ];
  const fontWeightOptions = [
    {
      label: t('正常'),
      value: 'normal',
    },
    {
      label: t('加粗'),
      value: 'bold',
    },
    {
      label: t('更粗'),
      value: 'bolder',
    },
    {
      label: t('细'),
      value: 'lighter',
    },
  ];
</script>

<style scoped></style>
