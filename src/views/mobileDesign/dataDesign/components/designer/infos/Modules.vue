<template>
  <div class="box" :style="!config.isShowTitle ? 'padding-top:0' : ''" v-if="data.show">
    <div class="item-title" v-if="config.isShowTitle">
      <div class="title-style">{{ props.title }}</div>
    </div>
    <ScrollContainer>
      <div v-if="config.functions.length > 0" class="menu-box">
        <div class="menu-item" v-for="(item, index) in config.functions" :key="index">
          <router-link :to="item.path || ''" class="menu-link">
            <div class="menu-icon">
              <div class="bg" :style="'background-color:' + item.color + ''"></div>
              <Icon
                :icon="item.icon || 'ant-design:align-center-outlined'"
                :size="26"
                :color="item.color"
                class="icon"
              />
            </div>
            <span class="menu-title">{{ item.name }}</span>
          </router-link>
        </div>
      </div>
    </ScrollContainer>
  </div>
</template>

<script setup lang="ts">
  import Icon from '/@/components/Icon/index';
  import { modulesProperties } from '../config/properties';
  import { DesktopComponent } from '/@/enums/desktop';
  import { ModulesConfig } from '/@/model/mobileDesign/designer';
  import { onMounted, reactive } from 'vue';
  import { ScrollContainer } from '/@/components/Container';
  const props = withDefaults(
    defineProps<{
      type: DesktopComponent;
      title: string;
      config: ModulesConfig;
    }>(),
    {
      type: DesktopComponent.DEFAULT,
      title: '',
      config: () => {
        return modulesProperties;
      },
    },
  );
  const data: {
    show: boolean;
  } = reactive({
    show: false,
  });
  onMounted(() => {
    data.show = true;
  });
</script>

<style lang="less" scoped>
  .box {
    padding-top: 40px;
    background: transparent;
    box-shadow: none;

    .item-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      color: #666;
      font-size: 14px;
      padding-left: 10px;
      font-weight: 700;

      .title-style {
        margin: 12px 0 0;
        line-height: 18px;
        padding-left: 6px;
        border-left: 6px solid #5e95ff;
      }
    }

    .menu-box {
      display: flex;
      align-items: center;
      padding: 10px;

      .menu-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 0 2px;

        .menu-link {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .menu-icon {
          position: relative;
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;

          .bg {
            border-radius: 50%;
            opacity: 0.1;
            height: 50px;
            width: 50px;
            position: relative;
            background: @primary-color;
          }

          .icon {
            position: absolute;
            top: 0;
            left: 0;
            height: 50px;
            width: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        .menu-title {
          color: #666;
          font-size: 12px;
          font-weight: 700;
          margin: 2px 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 76px;
          text-align: center;
        }
      }
    }
  }
</style>
