<template>
  <MobileLayout v-if="data.show">
    <template #head>
      <DesignHead :title="t('移动设计 - 数据展示页')">
        <template #steps
          ><a-steps :current="current">
            <a-step v-for="item in steps" :key="item.title" :title="item.title" />
          </a-steps>
        </template>
        <template #buttons>
          <div class="design-button">
            <a-button v-if="current == 1" class="mr-5px" @click="prev">{{ t('上一步') }}</a-button>
            <a-button v-if="current == 0" class="mr-5px" @click="next">{{ t('下一步') }}</a-button>
            <a-button class="mr-5px" type="warning" v-if="current == 1" @click="onSave(false)">{{
              t('保存草稿')
            }}</a-button>
            <a-button type="primary" v-if="current == 1" class="mr-5px" @click="onSave(true)">{{
              t('完成')
            }}</a-button>
            <a-button type="error" @click="$emit('close')">{{ t('关闭') }}</a-button>
          </div>
        </template>
      </DesignHead>
    </template>
    <Basic
      v-if="current == 0"
      ref="basic"
      :isEdit="editId ? true : false"
      :basicData="data.basicData"
    />
    <Design v-if="current == 1" ref="design" :list="data.list" />
    <LoadingBox v-if="spinning" />
  </MobileLayout>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref } from 'vue';

  import DesignHead from './designer/layout/Head.vue';
  import MobileLayout from './designer/layout/Containers.vue';
  import Design from './designer/Design.vue';
  import Basic from './designer/Basic.vue';

  import { LoadingBox } from '/@/components/ModalPanel/index';
  import { DesktopInfoItem, MobileBasicData, MobileData } from '/@/model/mobileDesign/designer';
  import { message } from 'ant-design-vue';

  import { useI18n } from '/@/hooks/web/useI18n';
  import {
    addMobileData,
    addMobileDataDraft,
    editMobileData,
    getMobileInfo,
  } from '/@/api/mobileDesign';
  import { buildAppDataCode } from '/@/utils/helper/appDataDesignHelper';
  const { t } = useI18n();
  let emits = defineEmits(['close']);
  const props = withDefaults(
    defineProps<{
      editId: string;
    }>(),
    {
      editId: '',
    },
  );
  const steps = [
    {
      title: t('基本信息'),
      content: t('基本信息'),
    },
    {
      title: t('页面设计'),
      content: t('页面设计'),
    },
  ];
  const basic = ref();
  const design = ref();

  const current = ref<number>(0);

  const spinning = ref<boolean>(false);
  const data: {
    show: boolean;
    basicData: MobileBasicData;
    jsonContent: string;
    list: Array<DesktopInfoItem>;
    enabledMark: number;
    appMenuId: string;
  } = reactive({
    show: false,
    basicData: {
      code: '', //编码
      name: '', //名称
      icon: '', //图标
      categoryId: null, //功能类别
      sortCode: 0, //排序
      isMenu: 0, //菜单
      remark: '', //描述
    },
    jsonContent: '',
    list: [],
    enabledMark: 1,
    appMenuId: '',
  });

  onMounted(async () => {
    if (props.editId) {
      let res = await getMobileInfo(props.editId);
      data.jsonContent = res.jsonContent;
      data.basicData = {
        code: res.code,
        name: res.name,
        icon: res.icon,
        categoryId: res.categoryId,
        sortCode: res.sortCode,
        isMenu: res.isMenu,
        remark: res.remark,
      };
      data.list = JSON.parse(res.jsonContent);
      data.show = true;
      data.enabledMark = res.enabledMark;
      data.appMenuId = res.appMenuId;
    } else {
      data.show = true;
      data.enabledMark = 1;
      data.list = [];
      data.appMenuId = '';
      current.value = 0;
    }
  });
  const next = async () => {
    if (current.value == 0) {
      try {
        let validate = await basic.value.validate();
        if (validate) {
          data.basicData = basic.value.getFieldsValue();
          current.value++;
        }
      } catch (error) {}
    }
  };
  const prev = async () => {
    data.list = await design.value.saveDesignData(false);
    current.value--;
  };

  async function onSave(close: boolean) {
    spinning.value = true;
    setTimeout(() => {
      if (close) {
        save();
      } else {
        saveDraft();
      }
    }, 100);
  }

  async function saveDraft() {
    let list = await design.value.saveDesignData();

    data.jsonContent = JSON.stringify(list);

    try {
      let res = false;
      if (props.editId) {
        let params: MobileData = {
          id: props.editId,
          ...data.basicData,
          jsonContent: data.jsonContent,
          enabledMark: -1,
        };
        if (data.basicData.isMenu) params.appMenuId = data.appMenuId;
        res = await addMobileDataDraft(params);
      } else {
        res = await addMobileDataDraft({
          ...data.basicData,
          jsonContent: data.jsonContent,
          enabledMark: -1,
        });
      }
      if (res) {
        message.success(t('保存成功'));
        emits('close');
      }
      spinning.value = false;
    } catch (error) {
      spinning.value = false;
    }
  }
  async function save() {
    let list = await design.value.saveDesignData();
    let frontCode = buildAppDataCode(list);
    data.jsonContent = JSON.stringify(list);

    try {
      let res = false;
      if (props.editId) {
        let params: MobileData = {
          id: props.editId,
          ...data.basicData,
          jsonContent: data.jsonContent,
          enabledMark: data.enabledMark == -1 ? 1 : data.enabledMark,
          pageCode: frontCode,
        };
        if (data.basicData.isMenu) params.appMenuId = data.appMenuId;
        res = await editMobileData(params);
      } else {
        res = await addMobileData({
          ...data.basicData,
          jsonContent: data.jsonContent,
          enabledMark: data.enabledMark,
          pageCode: frontCode,
        });
      }
      if (res) {
        message.success(t('保存成功'));
        emits('close');
      }
      spinning.value = false;
    } catch (error) {
      spinning.value = false;
    }
  }
</script>

<style lang="less" scoped>
  .head {
    height: 56px;
    width: 100%;
    box-shadow: 0 3px 6px 1px rgb(0 0 0 / 16%);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
  }

  .design-containers {
    position: fixed;
    inset: 0;
    z-index: 999;
    background-color: #f5f5f5;
  }

  [data-theme='dark'] .flow-containers {
    background-color: #151515;
  }

  .clean-icon {
    background-color: @clear-color;
    border-color: @clear-color;
  }

  .layout {
    position: relative;
    width: 100%;
    height: 100%;
    top: 56px;
  }

  :deep(.ant-steps) {
    width: 280px;
  }

  .design-button {
    display: flex;
    margin: 0 20px;
  }
  @keyframes rotation {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
  @keyframes rotationReverse {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(-360deg);
    }
  }

  :deep(.ant-steps-item-process) {
    .ant-steps-item-icon {
      border: 2px solid #fff;
      outline: 2px dashed #1890ff !important;
      line-height: 30px;
      animation: rotation 4s linear infinite;

      .ant-steps-icon {
        display: inline-block;
        animation: rotationReverse 4s linear infinite;
      }
    }
  }

  :deep(.ant-steps-item-container) {
    padding: 2px 0 2px 2px;
  }
</style>
