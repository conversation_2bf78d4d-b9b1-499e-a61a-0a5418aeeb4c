<template>
  <div class="step1-form">
    <BasicForm @register="register" />
  </div>
</template>
<script lang="ts" setup>
  import { inject, onMounted } from 'vue';
  import { FormSchema, useForm, BasicForm } from '/@/components/Form';
  import { GeneratorConfig } from '/@/model/generator/generatorConfig';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const generatorConfig = inject<GeneratorConfig>('generatorConfig') as GeneratorConfig;

  const formSchema: FormSchema[] = [
    {
      field: 'code',
      label: t('菜单编号'),
      required: true,
      title: t('菜单信息'),
      component: 'Input',
      componentProps: {
        placeholder: t('请输入菜单编号'),
      },
      colProps: { span: 24 },
    },
    {
      field: 'name',
      label: t('菜单名称'),
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: t('请输入菜单名称'),
      },
      colProps: { span: 24 },
    },
    {
      field: 'parentId',
      label: t('功能类别'),
      component: 'DicSelect',
      required: true,
      defaultValue: null,
      componentProps: {
        placeholder: t('请选择功能类别'),
        itemId: '1673142942973763585',
        isDefaultValue: true,
      },
      colProps: { span: 24 },
    },
    {
      field: 'sortCode',
      label: t('排序'),
      required: true,
      component: 'InputNumber',
      componentProps: {
        placeholder: t('请输入排序号'),
        min: 0,
      },
      colProps: { span: 24 },
    },
    {
      field: 'icon',
      label: t('菜单图标'),
      required: true,
      component: 'IconPicker',
      componentProps: {
        placeholder: t('请选择图标'),
      },
      colProps: { span: 24 },
    },
    {
      field: 'remark',
      label: t('备注'),
      component: 'InputTextArea',
      componentProps: {
        placeholder: t('请填写备注'),
      },
      colProps: { span: 24 },
    },
  ];

  const [register, { validate, getFieldsValue, setFieldsValue }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
  });
  onMounted(() => {
    setFieldsValue(generatorConfig.menuConfig);
  });
  //验证当前步骤的数据
  const validateStep = async (): Promise<boolean> => {
    try {
      const formData = await validate();
      setData(formData);
    } catch (error) {
      return false;
    }
    return true;
  };
  function getFormData() {
    const formData = getFieldsValue();
    setData(formData);
  }
  function setData(formData) {
    generatorConfig.menuConfig!.code = formData.code;
    generatorConfig.menuConfig!.name = formData.name;
    generatorConfig.menuConfig!.parentId = formData.parentId;
    generatorConfig.menuConfig!.remark = formData.remark;
    generatorConfig.menuConfig!.sortCode = formData.sortCode;
    generatorConfig.menuConfig!.icon = formData.icon;
  }
  defineExpose({ validateStep, getFormData });
</script>
<style lang="less" scoped>
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
