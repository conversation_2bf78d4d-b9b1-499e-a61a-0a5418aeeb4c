<template>
  <PageWrapper dense fixedHeight contentFullHeight>
    <BasicTable @register="registerTable" isMenuTable>
      <template #toolbar>
        <a-button type="primary" v-auth="'liteFlow:add'" @click="handleCreate">
          {{ t('新增') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                auth: 'liteFlow:edit',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                auth: 'liteFlow:delete',
                color: 'error',
                ifShow: record.code !== 'chinese',
                popConfirm: {
                  title: t('是否确认删除？'),
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <LgTypeDrawer @register="registerDrawer" @success="handleSuccess" />
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';

  import { BasicTable, useTable, TableAction, BasicColumn, FormSchema } from '/@/components/Table';

  import { useModal } from '/@/components/Modal';
  import LgTypeDrawer from './components/liteFLowModal.vue';

  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';

  import { PageWrapper } from '/@/components/Page';
  import { getLiteflowPage, deleteLiteflow } from '/@/api/liteflow/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export const columns: BasicColumn[] = [
    {
      title: t('规则名称'),
      dataIndex: 'chainName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('应用名称'),
      dataIndex: 'applicationName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('备注'),
      dataIndex: 'chainDesc',
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建时间'),
      dataIndex: 'createTime',
      align: 'left',
      resizable: true,
    },
  ];

  export const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入规则名称或应用名称'),
      },
    },
  ];

  export default defineComponent({
    name: 'TaskLog',
    components: { BasicTable, LgTypeDrawer, TableAction, PageWrapper },
    setup() {
      const { notification } = useMessage();
      const { hasPermission } = usePermission();

      const [registerDrawer, { openModal }] = useModal();

      const [registerTable, { reload }] = useTable({
        title: t('规则管理列表'),
        api: getLiteflowPage,
        columns,
        formConfig: {
          rowProps: {
            gutter: 16,
          },
          schemas: searchFormSchema,
          showResetButton: false,
        },
        useSearchForm: true,
        showTableSetting: true,
        striped: false,
        pagination: {
          hideOnSinglePage: true,
        },
        actionColumn: {
          width: 80,
          title: t('操作'),
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
        tableSetting: {
          size: false,
        },
        customRow: (record) => {
          return {
            ondblclick: () => {
              if (hasPermission('liteFlow:edit')) {
                handleEdit(record);
              }
            },
          };
        },
      });

      function handleCreate() {
        openModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        openModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        deleteLiteflow([record.id]).then((_) => {
          reload();
          notification.success({
            message: t('提示'),
            description: t('删除成功'),
          }); //提示消息
        });
      }

      function handleSuccess() {
        reload();
      }

      return {
        registerTable,
        registerDrawer,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        t,
      };
    },
  });
</script>
