<template>
  <div class="box">
    <div class="tag-box">
      <div class="tag-item"
        ><span class="tag-title"> {{ props.viewData?.releaseTime }}</span></div
      >
      <div class="tag-item"><span class="tag-title hr"> |</span></div>
      <div class="tag-item"
        ><span class="tag-title">{{ props.viewData?.categoryName }}</span></div
      >
      <div class="tag-item" v-if="props.viewData?.categoryName"
        ><span class="tag-title hr"> |</span></div
      >
      <div class="tag-item"
        ><span class="tag-title">{{
          props.viewData?.typeId == OaType.NEWS
            ? '作者：' + props.viewData?.authorName
            : '信息来源：' + props.viewData?.sourceName
        }}</span></div
      >
      <div class="tag-item"><span class="tag-title hr"> |</span></div>
      <div class="tag-item"
        ><span class="tag-title">{{
          props.viewData?.typeId == OaType.NEWS
            ? '编辑：' + props.viewData?.compileName
            : '信息地址：' + props.viewData?.sourceAddress
        }}</span></div
      >
    </div>
    <div class="content-box">
      <div class="content" v-html="props.viewData?.newsContent"> </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { OaType } from '../../../../enums/oa';
  const props = defineProps({
    viewData: {
      type: Object as PropType<any>,
    },
  });
</script>

<style scoped>
  .box {
    padding: 10px;
  }

  .tag-box {
    margin-bottom: 10px;
    display: flex;
  }

  .tag-item .tag-title {
    font-size: 12px;
    color: #a8a8a8;
  }

  .tag-item .hr {
    margin: 0 10px;
  }

  .content-box {
    color: #000;
    background-color: #f7f7f7;
    padding: 20px;
    font-size: 14px;
    height: 440px;
  }
</style>
