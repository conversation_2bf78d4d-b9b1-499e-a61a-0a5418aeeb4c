<template>
  <PageWrapper dense contentFullHeight fixedHeight contentClass="flex">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'codeRule:add'">{{
          t('新增')
        }}</a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              auth: 'codeRule:edit',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              auth: 'codeRule:delete',
              color: 'error',
              popConfirm: {
                title: t('是否确认删除'),
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <CodeModal @register="registerModal" @success="handleSuccess" :width="800" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';
  import { getCodeList, deleteCodeRule } from '/@/api/system/code';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useModal } from '/@/components/Modal';
  import CodeModal from './components/CodeModal.vue';
  import { usePermission } from '/@/hooks/web/usePermission';

  // import DeptTree from './components/DeptTree.vue';
  const { t } = useI18n();
  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('编码名称'),
      component: 'Input',
      colProps: { xs: 12, md: 8 },
      componentProps: {
        placeholder: t('请输入编码名称'),
      },
    },
  ];

  const columns: BasicColumn[] = [
    {
      title: t('编码编号'),
      dataIndex: 'code',
      width: 120,
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('编码名称'),
      dataIndex: 'name',
      width: 120,
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('当前流水号'),
      dataIndex: 'currentNumber',
      width: 120,
      align: 'left',
      sorter: true,
      resizable: true,
    },
    {
      title: t('创建用户'),
      dataIndex: 'createUserName',
      width: 120,
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      width: 180,
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('编码说明'),
      dataIndex: 'description',
      width: 180,
      align: 'left',
      sorter: true,
      resizable: true,
    },
  ];

  const { notification } = useMessage();
  const { hasPermission } = usePermission();

  const selectRuleId = ref('');

  const [registerModal, { openModal }] = useModal();
  const [registerTable, { reload }] = useTable({
    title: t('自动编码列表'),
    api: getCodeList,
    rowKey: 'id',
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },
    useSearchForm: true,
    striped: false,
    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('codeRule:edit')) {
            handleEdit(record);
          }
        },
      };
    },
  });

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  function handleDelete(record: Recordable) {
    deleteCodeRule([record.id]).then((_) => {
      reload({ searchInfo: { id: selectRuleId.value } });
      notification.success({
        message: t('提示'),
        description: t('删除成功'),
      }); //提示消息
    });
  }

  function handleSuccess() {
    reload({ searchInfo: { id: selectRuleId.value } });
  }
</script>
