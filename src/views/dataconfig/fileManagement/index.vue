<template>
  <PageWrapper dense contentFullHeight fixedHeight contentClass="flex">
    <BasicTable @register="registerTable" class="complex-style">
      <template #toolbar>
        <a-button @click="handleUpload" type="primary" v-auth="'file:upload'"> 上传</a-button>
        <a-button @click="handleMulDownload" v-auth="'file:multipleDownload'"> 下载 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'fileName'">
          <div class="flex items-center">
            <img :src="getImg(record.fileType)" class="w-5 mr-1.5" /> {{ record.fileName }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                img: viewPng,
                tooltip: '查看',
                auth: 'file:view',
                onClick: handleView.bind(null, record),
              },
              {
                img: downloadPng,
                tooltip: '下载',
                auth: 'file:download',
                onClick: handleDownload.bind(null, record),
              },
              {
                img: authPng,
                tooltip: '权限',
                auth: 'file:auth',
                onClick: handleAuth.bind(null, record),
              },
              {
                img: renamePng,
                tooltip: '重命名',
                auth: 'file:rename',
                onClick: handleRename.bind(null, record),
              },

              {
                img: deletePng,
                tooltip: '删除',
                auth: 'file:delete',
                popConfirm: {
                  title: t('是否确认删除'),
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PreviewModal @register="registerPreviewModal" @success="reload" />
    <RenameModal @register="registerRenameModal" @success="handleNameSuccess" />
    <AuthModal @register="registerAuthModal" />
    <ImportModal
      @register="registerImportModal"
      importUrl="/system/file-management"
      :isDefaultModal="false"
      accept=""
      @success="reload"
    />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';
  import { getFilePageList, deleteFile, updateFileName } from '/@/api/system/fileManagement';
  import { downloadByUrl } from '/@/utils/file/download';
  import { ImportModal } from '/@/components/Import';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useModal } from '/@/components/Modal';
  import PreviewModal from './components/PreviewModal.vue';
  import RenameModal from './components/RenameModal.vue';
  import AuthModal from './components/AuthModal.vue';
  import { usePermission } from '/@/hooks/web/usePermission';
  import viewPng from '/@/assets/file/view.png';
  import downloadPng from '/@/assets/excel/download.png';
  import authPng from '/@/assets/excel/auth.png';
  import renamePng from '/@/assets/excel/rename.png';
  import deletePng from '/@/assets/excel/delete.png';
  import pdfPng from '/@/assets/file/pdf.png';
  import wordPng from '/@/assets/file/word.png';
  import txtPng from '/@/assets/file/txt.png';
  import imgPng from '/@/assets/file/img.png';
  import excelPng from '/@/assets/file/excel.png';
  import bgPng from '/@/assets/file/bg.png';
  import uploadPng from '/@/assets/file/upload.png';

  const { t } = useI18n();
  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: '',
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        prefix: 'ant-design:search-outlined',
        placeholder: '请输入文件名称',
      },
    },
  ];

  const columns: BasicColumn[] = [
    {
      title: '名称',
      dataIndex: 'fileName',
      align: 'left',
      width: 350,
      resizable: true,
    },
    {
      title: '大小',
      dataIndex: 'fileSize',
      align: 'left',
      width: 200,
      resizable: true,
      customRender: ({ record }) => {
        return !!record.fileSize ? (record.fileSize / 1024).toFixed(1) + 'KB' : record.fileSize;
      },
    },
    {
      title: '创建者',
      dataIndex: 'createUserName',
      align: 'left',
      resizable: true,
    },
    {
      title: '更新时间',
      dataIndex: 'modifyDate',
      align: 'left',
      sorter: true,
      resizable: true,
    },
  ];
  const { notification } = useMessage();
  const { hasPermission } = usePermission();

  const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();
  const [registerRenameModal, { openModal: openRenameModal }] = useModal();
  const [registerAuthModal, { openModal: openAuthModal }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerTable, { reload, getSelectRows }] = useTable({
    title: '文件管理',
    api: getFilePageList,
    rowKey: 'id',
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
    },
    useSearchForm: true,
    striped: false,
    showIndexColumn: false,
    actionColumn: {
      width: 300,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    rowSelection: {
      type: 'checkbox',
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('file:view')) {
            handleView(record);
          }
        },
      };
    },
    trBgUrl: bgPng,
  });

  const getImg = (type) => {
    switch (type) {
      case 'pdf':
        return pdfPng;
      case 'doc':
      case 'docx':
        return wordPng;
      case 'txt':
        return txtPng;
      case 'png':
      case 'jpg':
      case 'jpeg':
        return imgPng;
      default:
        return excelPng;
    }
  };

  const handleUpload = () => {
    openImportModal(true, {
      title: '文件上传',
      imgSrc: uploadPng,
    });
  };

  const handleView = async (record) => {
    openPreviewModal(true, {
      fileUrl: record.fileUrl,
      fileName: record.fileName,
    });
  };

  const handleDelete = (record: Recordable) => {
    deleteFile([record.id]).then(() => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功'),
      }); //提示消息
    });
  };

  const handleNameSuccess = async (info) => {
    await updateFileName(info);
    reload();
  };

  const handleMulDownload = async () => {
    if (!getSelectRows().length) {
      notification.warning({
        message: t('警告'),
        description: t('必须选中一行！'),
      });
      return false;
    }
    getSelectRows().forEach((item) => {
      handleDownload(item);
    });
  };

  const handleRename = (record) => {
    openRenameModal(true, {
      fileName: record.fileName,
      id: record.id,
      title: '重命名',
    });
  };

  const handleDownload = async (record) => {
    downloadByUrl({ url: record.fileUrl, fileName: record.fileName });
  };

  const handleAuth = (record) => {
    openAuthModal(true, {
      id: record.id,
    });
  };
</script>
