<template>
  <PageWrapper dense fixedHeight contentFullHeight>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="'ruleCode:add'" @click="handleCreate">
          {{ t('新增') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                auth: 'ruleCode:edit',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                auth: 'ruleCode:delete',
                color: 'error',
                popConfirm: {
                  title: t('是否确认删除？'),
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ruleCodeModal @register="registerModal" @success="handleSuccess" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, BasicColumn, FormSchema } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import ruleCodeModal from './components/ruleCodeModal.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { PageWrapper } from '/@/components/Page';
  import { getRuleCodePageList, deleteRuleCode } from '/@/api/system/ruleCode';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const columns: BasicColumn[] = [
    {
      title: '规则代码编号',
      dataIndex: 'codeNumber',
      align: 'left',
      resizable: true,
    },
    {
      title: '规则代码方法名',
      dataIndex: 'methodName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('备注'),
      dataIndex: 'remark',
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建人'),
      dataIndex: 'createUserName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      align: 'left',
      resizable: true,
    },
    {
      title: t('最后修改人'),
      dataIndex: 'modifyUserName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('最后修改时间'),
      dataIndex: 'modifyDate',
      align: 'left',
      resizable: true,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入规则名称或应用名称'),
      },
    },
  ];

  const { notification } = useMessage();
  const { hasPermission } = usePermission();

  const [registerModal, { openModal }] = useModal();

  const [registerTable, { reload }] = useTable({
    title: '规则代码',
    api: getRuleCodePageList,
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('ruleCode:edit')) {
            handleEdit(record);
          }
        },
      };
    },
  });

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      id: record.id,
      isUpdate: true,
    });
  }

  function handleDelete(record: Recordable) {
    deleteRuleCode([record.id]).then((_) => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功'),
      });
    });
  }

  function handleSuccess() {
    reload();
  }
</script>
