<template>
  <PageWrapper title="表单基础示例" contentFullHeight>
    <CollapseContainer title="基础示例">
      <SimpleForm ref="formRef" :formProps="getFormProps" :formModel="formData">
        <template #buttonBefore>
          <a-button @click="handleChangeProps"> 修改表单props</a-button>
        </template>
        <template #buttonAfter>
          <a-button @click="handleReset"> 按钮之后</a-button>
        </template>
      </SimpleForm>
      <div>
        <a-button type="primary" @click="handleSubmit"> 提交</a-button>
        <a-button style="margin-left: 10px" @click="handleReset">重置</a-button>
      </div>
    </CollapseContainer>
  </PageWrapper>
</template>
<script lang="ts" setup>
  import SimpleForm from '/@/components/SimpleForm/src/SimpleForm.vue';
  import { CollapseContainer } from '/@/components/Container';
  import { PageWrapper } from '/@/components/Page';
  import { getFormProps } from './data';
  import { reactive, ref } from 'vue';

  const formRef = ref();

  const formData = reactive<Recordable>({
    fieldDouble: '12',
    fieldInt: '12',
    fieldDatetimeStartTime: '2022-06-29',
    fieldDatetimeEndTime: '2022-07-29',
    fieldLong: '12',
    fieldString: '12',
    fieldUpload: '6958325054052503552',
    // postId: '1419276795215351808',
    subform: [
      {
        fieldString: 'zzzzzzzzzzzzzzzz',
        fieldInt: true,
      },
    ],
  });

  const handleSubmit = async () => {
    const res = await formRef.value?.validate();
    console.log('通过formRef  调用表单提交方法', res);
  };
  const handleReset = () => {
    formRef.value!.resetFields();
    console.log('通过formRef  调用表单重置方法');
  };

  let isDisabled = false;
  const handleChangeProps = async () => {
    {
      //-------------------禁用表单------------------------
      isDisabled = !isDisabled;
      await formRef.value?.setProps({
        disabled: isDisabled,
      });
      //-------------重置整个表单的元素-----------------
      // formRef.value?.removeSchemaByFiled('fieldString');
      //-------------重置整个表单的元素-----------------
      // formRef.value?.resetSchema({
      //   field: 'fieldString',
      //   label: '字符串1111',
      //   component: 'InputNumber',
      //   rules: [{ required: true, message: 'Please input your fieldString!', trigger: 'blur' }],
      //   colProps: { span: 24 },
      // });
      //--------------修改单个表单元素-------------------
      // formRef.value?.updateSchema({
      //   field: 'fieldString',
      //   label: '字符串1111',
      //   component: 'InputNumber',
      //   rules: [{ required: true, message: 'Please input your fieldString!', trigger: 'blur' }],
      //   colProps: { span: 24 },
      // });
      //----------------表单新增元素---------------
      // formRef.value?.appendSchemaByField(
      //   {
      //     field: 'fieldString111',
      //     label: '字符串',
      //     component: 'Input',
      //     rules: [{ required: true, message: 'Please input your fieldString!', trigger: 'blur' }],
      //     colProps: { span: 24 },
      //   },
      //   'fieldString',
      // );
      //--------------修改表单配置-----------------
      // await formRef.value?.setProps({
      //   labelWidth: 150,
      //   showResetButton: false,
      //   showSubmitButton: false,
      // });
    }
  };
</script>
