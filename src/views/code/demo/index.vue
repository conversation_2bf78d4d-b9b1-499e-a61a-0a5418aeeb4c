<template>
  <PageWrapper dense contentFullHeight contentClass="flex">
    <!-- <DeptTree class="w-1/4 xl:w-1/5" @select="handleSelect" /> -->
    <BasicTable @register="registerTable">
      <!--class="w-3/4 xl:w-4/5" -->
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">新增</a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '是否确认删除',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <DemoModal @register="registerModal" @success="handleSuccess" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';
  import { getDemoPage, deleteDemo } from '/@/api/code/demo';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useModal } from '/@/components/Modal';
  import DemoModal from './components/DemoModal.vue';
  // import DeptTree from './components/DeptTree.vue';

  const searchFormSchema: FormSchema[] = [
    {
      field: 'fieldString',
      label: '字符串',
      component: 'Input',
      colProps: { span: 8 },
    },
    {
      field: 'fieldInt',
      label: '整型',
      component: 'Input',
      colProps: { span: 8 },
    },
    {
      field: 'fieldDatetime',
      label: '时间',
      component: 'RangePicker',
      colProps: { span: 8 },
    },
    {
      field: 'fieldDouble',
      label: '浮点',
      component: 'Input',
      colProps: { span: 8 },
    },
    {
      field: 'fieldLong',
      label: '长整数',
      component: 'Input',
      colProps: { span: 8 },
    },
  ];

  const columns: BasicColumn[] = [
    {
      title: '字符串',
      dataIndex: 'fieldString',
      // width: 120,
    },
    {
      title: '整型',
      dataIndex: 'fieldInt',
      // width: 120,
    },
    {
      title: '时间',
      dataIndex: 'fieldDatetime',
      // width: 120,
    },
    {
      title: '浮点',
      dataIndex: 'fieldDouble',
      // width: 120,
    },
    {
      title: '长整数',
      dataIndex: 'fieldLong',
      // width: 180,
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
  ];

  defineEmits(['register']);
  const { notification } = useMessage();
  const { t } = useI18n();
  const selectDeptId = ref(''); //左侧树选择

  const [registerModal, { openModal }] = useModal();
  const [registerTable, { reload }] = useTable({
    title: 'Demo列表',
    api: getDemoPage,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      fieldMapToTime: [
        ['fieldDatetime', ['fieldDatetimeStart', 'fieldDatetimeEnd'], 'YYYY-MM-DD HH:mm:ss'],
      ],
    },
    // beforeFetch: (params) => {
    //   //发送请求默认新增  左边树结构所选机构id
    //   return { ...params, departmentId: selectDeptId.value };
    // },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    // console.log(record);
    openModal(true, {
      id: record.id,
      isUpdate: true,
    });
  }

  function handleDelete(record: Recordable) {
    deleteDemo([record.id]).then((_) => {
      reload({ searchInfo: { departmentId: selectDeptId.value } });
      notification.success({
        message: 'Tip',
        description: t('删除成功！'),
      }); //提示消息
    });
  }

  function handleSuccess() {
    reload({ searchInfo: { departmentId: selectDeptId.value } });
  }
</script>
