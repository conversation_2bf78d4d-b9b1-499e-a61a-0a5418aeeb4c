<template>
  <PageWrapper dense contentFullHeight fixedHeight>
    <BasicTable @register="registerTable" isMenuTable>
      <template #toolbar>
        <a-button @click.stop="add" type="primary" v-auth="'delegate:add'">{{
          t('新增')
        }}</a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              auth: 'delegate:edit',
              onClick: edit.bind(null, record.id),
            },
            {
              icon: 'ant-design:delete-outlined',
              auth: 'delegate:delete',
              color: 'error',
              popConfirm: {
                title: t('是否确认删除'),
                confirm: handleDelete.bind(null, record.id),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <DelegateProcess v-if="delegateData.visible" :id="delegateData.id" @close="close" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { reactive } from 'vue';

  import DelegateProcess from './components/DelegateProcess.vue';
  import { getProcessDelegatePage, deleteDelegate } from '/@/api/workflow/delegate';

  import { notification } from 'ant-design-vue';
  import { usePermission } from '/@/hooks/web/usePermission';

  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';

  import { PageWrapper } from '/@/components/Page';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const configColumns: BasicColumn[] = [
    {
      title: t('被委托人'),
      dataIndex: 'delegateUserNames',
      width: 180,
      align: 'left',
      resizable: true,
      sorter: {
        multiple: 1,
      },
    },
    {
      title: t('开始时间'),
      dataIndex: 'startTime',
      width: 160,
      align: 'left',
      resizable: true,
      sorter: {
        multiple: 2,
      },
    },
    {
      title: t('结束时间'),
      dataIndex: 'endTime',
      width: 160,
      align: 'left',
      resizable: true,
      sorter: {
        multiple: 3,
      },
    },
    {
      title: t('委托人'),
      dataIndex: 'delegator',
      width: 120,
      align: 'left',
      resizable: true,
      sorter: {
        multiple: 3,
      },
    },
    {
      title: t('创建时间'),
      align: 'left',
      resizable: true,
      dataIndex: 'createDate',
    },
    {
      title: t('委托说明'),
      align: 'left',
      resizable: true,
      dataIndex: 'remark',
    },
  ];
  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入关键字'),
      },
    },
  ];
  const { hasPermission } = usePermission();

  let delegateData = reactive({
    id: '',
    visible: false,
  });
  const [registerTable, { reload }] = useTable({
    title: t('流程委托列表'),
    api: getProcessDelegatePage,
    columns: configColumns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },
    striped: false,
    useSearchForm: true,
    showTableSetting: true,
    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('delegate:edit')) {
            edit(record.id);
          }
        },
      };
    },
  });

  function add() {
    delegateData.id = '';
    delegateData.visible = true;
  }
  function edit(id: string) {
    delegateData.id = id;
    delegateData.visible = true;
  }
  function close() {
    delegateData.visible = false;
    reload();
  }
  async function handleDelete(id: string) {
    try {
      let res = await deleteDelegate([id]);
      if (res) {
        notification.open({
          type: 'success',
          message: t('删除'),
          description: t('删除成功'),
        });
        reload();
      } else {
        notification.open({
          type: 'error',
          message: t('删除'),
          description: t('删除失败'),
        });
      }
    } catch (error) {}
  }
</script>
