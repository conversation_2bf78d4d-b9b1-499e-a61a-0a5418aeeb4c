<template>
  <div class="box">
    <NodeHead nodeName="前置任务" class="title" />
    <div v-for="(item, index) in props.relationTasks" :key="index" class="task-box">
      <div class="label-box">
        <span>任务名称：</span>
        <span>{{ item.schemaName }}</span>
      </div>
      <a-input
        :value="item.taskName"
        placeholder="点击选择前置任务"
        @click="open(index, item.schemaId)"
        style="width: 100%"
      >
        <template #suffix>
          <Icon icon="ant-design:ellipsis-outlined" />
        </template>
      </a-input>
    </div>

    <a-modal
      :width="1000"
      v-model:visible="visible"
      title="选择前置任务"
      :maskClosable="false"
      @ok="submit"
      @cancel="close"
    >
      <div class="p-5">
        <a-table
          v-if="visible"
          :dataSource="data.dataSource"
          :columns="columns"
          rowKey="processId"
          :row-selection="{
            selectedRowKeys: data.selectedRowKeys,
            onChange: onSelectChange,
            type: 'radio',
          }"
          :pagination="data.pagination"
          :scroll="{ y: '400px' }"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { NodeHead } from '/@/components/ModalPanel/index';
  import { Icon } from '/@/components/Icon';
  import { reactive, ref } from 'vue';
  import { PredecessorTaskItem, SchemaTaskItem, TaskItem } from '/@/model/workflow/bpmnConfig';
  import { getRelationTasks } from '/@/api/workflow/task';
  const emit = defineEmits(['change']);
  const props = withDefaults(
    defineProps<{ relationTasks: Array<SchemaTaskItem>; schemaId: string }>(),
    {
      relationTasks: () => {
        return [];
      },
    },
  );
  let visible = ref(false);
  let data: {
    dataSource: Array<PredecessorTaskItem>;
    selectedRowKeys: string[];
    selectedTask: TaskItem;
    pagination: { current: number; total: number; pageSize: number };
    selectIndex: number;
  } = reactive({
    dataSource: [],
    selectedRowKeys: [],
    selectedTask: {
      taskId: '',
      taskName: '',
      processId: '',
    },
    pagination: {
      current: 1,
      total: 0,
      pageSize: 15,
    },
    selectIndex: -1,
  });

  let columns = [
    {
      title: '序号',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 60,
      align: 'center',
    },
    {
      title: '任务',
      dataIndex: 'schemaName',
      key: 'schemaName',
    },
    {
      title: '标题',
      dataIndex: 'taskName',
      key: 'taskName',
      ellipsis: true,
    },
    // {
    //   title: '等级',
    //   dataIndex: 'level',
    //   key: 'level',
    // },
    {
      title: '发起人',
      dataIndex: 'originator',
      key: 'originator',
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
  ];
  async function open(index: number, relationSchemaId: string) {
    data.selectIndex = index;
    let res = await getRelationTasks(props.schemaId, relationSchemaId, {
      limit: data.pagination.current,
      size: data.pagination.pageSize,
    });
    data.dataSource = res.list;
    data.pagination.total = res.total;
    visible.value = true;
  }
  function close() {
    data.selectIndex = -1;
    data.dataSource = [];
    data.pagination.total = 0;
    visible.value = false;
  }
  function submit() {
    let list = props.relationTasks;
    list[data.selectIndex].taskId = data.selectedTask.taskId;
    list[data.selectIndex].taskName = data.selectedTask.taskName;
    list[data.selectIndex].processId = data.selectedTask.processId;
    emit('change', list);
    close();
  }
  const onSelectChange = (selectedRowKeys: string[], selectedRows: Array<PredecessorTaskItem>) => {
    let { taskId, taskName, processId } = selectedRows[0];
    data.selectedTask.taskId = taskId;
    data.selectedTask.taskName = taskName;
    data.selectedTask.processId = processId;
    data.selectedRowKeys = selectedRowKeys;
  };
</script>

<style lang="less" scoped>
  .box {
    margin-bottom: 10px;

    .title {
      margin-bottom: 10px;
    }

    .task-box {
      margin: 8px 0;

      .label-box {
        margin-bottom: 8px;
        color: rgba(102, 102, 102, 0.996);
      }
    }
  }
</style>
