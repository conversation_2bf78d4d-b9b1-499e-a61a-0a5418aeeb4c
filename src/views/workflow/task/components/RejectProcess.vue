<template>
  <span @click.stop="rejectNode"
    ><slot></slot>
    <a-modal
      v-model:visible="data.visible"
      :title="t('请选择撤回到的节点')"
      width="700px"
      @ok="submit"
      @cancel="close"
      :okText="t('确认')"
      :cancelText="t('取消')"
      @click.stop=""
    >
      <div class="box" v-if="data.visible">
        <div
          class="item"
          :class="data.checkedIds.includes(item.activityId) ? 'activity' : ''"
          v-for="(item, index) in data.list"
          :key="index"
          @click="check(item.activityId)"
          >{{ item.activityName }}</div
        >
      </div>
      <a-alert
        message="外部流程节点、子流程内部节点、会签节点都不支持撤回；撤回到开始节点需要二次确认！"
        type="warning"
      />
    </a-modal>
  </span>
</template>

<script setup lang="ts">
  import { reactive, onMounted } from 'vue';
  import { getRejectNodeList, withdraw } from '/@/api/workflow/task';
  import { notification } from 'ant-design-vue';
  import { getStartNodeId } from '/@bpmn/config/property';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{ processId: string; taskId: string; visible?: boolean }>(),
    {
      processId: '',
      taskId: '',
      visible: false,
    },
  );
  let emits = defineEmits(['close', 'restart']);
  const data: {
    visible: boolean;
    checkedIds: Array<string>;
    list: Array<{
      activityId: string;
      activityName: string;
    }>;
  } = reactive({
    visible: false,
    list: [],
    checkedIds: [],
  });
  onMounted(() => {
    if (props.visible) {
      rejectNode();
    }
  });
  async function submit() {
    if (data.checkedIds.length > 0) {
      if (getStartNodeId == data.checkedIds[0]) {
        emits('restart', data.checkedIds[0]);
        data.visible = false;
      } else {
        try {
          let res = await withdraw(props.processId, data.checkedIds[0]);
          if (res) {
            notification.open({
              type: 'success',
              message: t('撤回'),
              description: t('撤回成功'),
            });
            close();
          } else {
            notification.open({
              type: 'error',
              message: t('撤回'),
              description: t('撤回失败'),
            });
          }
        } catch (error) {
          close();
        }
      }
    } else {
      notification.open({
        type: 'error',
        message: t('撤回'),
        description: t('请选择一个节点进行撤回'),
      });
    }
  }
  async function rejectNode() {
    if (props.processId) {
      try {
        let res = await getRejectNodeList(props.processId, props.taskId);
        if (res && Array.isArray(res) && res.length > 0) {
          data.visible = true;
          data.list = res;
        }
      } catch (error) {
        close();
      }
    } else {
      notification.open({
        type: 'error',
        message: t('撤回'),
        description: t('请选择一个流程进行撤回'),
      });
    }
  }
  function check(activityId) {
    if (data.checkedIds.includes(activityId)) {
      data.checkedIds = [];
    } else {
      data.checkedIds = [activityId];
    }
  }
  function close() {
    data.visible = false;
    emits('close');
  }
</script>
<style lang="less" scoped>
  .box {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    height: 300px;
    overflow: auto;

    .item {
      width: 84px;
      height: 84px;
      border: 1px solid rgb(198 226 255 / 100%);
      margin-right: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 6px;
      color: rgb(0 0 0 / 60%);
    }

    .activity {
      color: rgb(158 203 251);
      border: 1px solid rgb(198 226 255 / 100%);
      background: rgb(236 245 255 / 100%);
    }
  }
</style>
