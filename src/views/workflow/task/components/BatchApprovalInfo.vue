<template>
  <BasicModal
    v-bind="$attrs"
    width="1000px"
    @register="registerModal"
    :title="t('批量审核')"
    :cancel-text="t('关闭')"
    @cancel="handlesubmit"
    :show-ok-btn="false"
    :closable="false"
  >
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>
<script setup lang="ts">
  import { Tag } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';

  import { useI18n } from '/@/hooks/web/useI18n';
  import { h } from 'vue';
  const { t } = useI18n();

  const columns: BasicColumn[] = [
    {
      title: t('流程任务名称'),
      dataIndex: 'schemaName',
      align: 'left',
    },
    {
      title: t('当前审批节点'),
      dataIndex: 'currentNodeName',
      align: 'left',
    },
    {
      title: t('发起人'),
      dataIndex: 'startUserName',
      align: 'left',
    },
    {
      title: t('审核结果'),
      dataIndex: 'approveResult',
      align: 'left',
      customRender: ({ record }) => {
        return h(
          Tag,
          {
            color: record.approveResult === '审核成功' ? 'green' : 'red',
          },
          () => t(`${record.approveResult}`),
        );
      },
    },
    {
      title: t('审核详情'),
      dataIndex: 'approveDetail',
      align: 'left',
    },
  ];
  const emit = defineEmits(['success', 'register']);
  const [registerTable, { setTableData }] = useTable({
    title: t('审核明细'),
    columns,
    useSearchForm: false,
    showTableSetting: false,
    striped: false,
    pagination: false,
  });
  const [registerModal, { closeModal }] = useModalInner((data) => {
    setTableData(data || []);
  });
  function handlesubmit() {
    closeModal();
    emit('success');
  }
</script>
