<template>
  <BasicTable @register="registerTable" @selection-change="selectionChange">
    <template #toolbar>
      <BatchApprovalProcess
        v-if="showBatchApproval"
        @close="BatchClearHandler"
        :selectedRows="data.selectedRows"
      >
        <a-button v-auth="'processtasks:batchApproval'">{{ t('批量审批') }}</a-button>
      </BatchApprovalProcess>
      <ApprovalProcess
        v-else
        :taskId="taskId"
        :processId="processId"
        :schemaId="schemaId"
        @close="clearHandler"
        :visible="false"
      >
        <a-button v-auth="'processtasks:approve'">{{ t('审批') }}</a-button>
      </ApprovalProcess>
    </template>
    <template #action="{ record }">
      <TableAction
        :actions="[
          {
            icon: 'ant-design:eye-outlined',
            auth: 'processtasks:view',
            tooltip: '查看',
            onClick: handleView.bind(null, record),
          },
        ]"
      />
    </template>
    <template #currentProgress="{ record }">
      <a-progress
        v-if="typeof record.currentProgress === 'number' && isFinite(record.currentProgress)"
        :percent="record.currentProgress"
        size="small"
      />
    </template>
  </BasicTable>
  <InfoModal @register="registerModal" @success="reload" />
  <LookProcess
    v-if="visibleLookProcess"
    :visible="visibleLookProcess"
    :taskId="taskIdProcess"
    :processId="processIdProcess"
    @close="handleLookClose"
  />
</template>

<script setup lang="ts">
  import userTaskTable from './../../hooks/userTaskTable';
  import { useModal } from '/@/components/Modal';
  import LookProcess from './../LookProcess.vue';
  import ApprovalProcess from './../ApprovalProcess.vue';
  import BatchApprovalProcess from './../BatchApprovalProcess.vue';
  import InfoModal from '../BatchApprovalInfo.vue';
  import { BasicTable, useTable, BasicColumn, TableAction } from '/@/components/Table';
  import { getSchemaTask } from '/@/api/workflow/process';
  import { TaskTypeUrl } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { unref, watch, h, ref } from 'vue';
  import { useRouter } from 'vue-router';
  const { t } = useI18n();
  const configColumns: BasicColumn[] = [
    {
      title: t('流水号'),
      dataIndex: 'serialNumber',
      width: 70,
      align: 'left',
      resizable: true,
    },
    {
      title: t('流程名称'),
      dataIndex: 'processName',
      width: 200,
      align: 'left',
      resizable: true,
    },
    {
      title: t('任务名称'),
      dataIndex: 'taskName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('当前任务耗时'),
      dataIndex: 'consumingTime',
      align: 'left',
      width: 150,
      resizable: true,
      customRender: ({ record }) => {
        const color = record.consumingTime === '审批结束' ? '#b0f323' : '';
        return h('span', { style: { color } }, record.consumingTime);
      },
    },
    {
      title: t('流程状态'),
      dataIndex: 'status',
      align: 'left',
      resizable: true,
      customRender: ({ record }) => {
        const color = record.status === '审批结束' ? '#b0f323' : '#69acff';
        return h('span', { style: { color } }, record.status);
      },
      width: 130,
    },
    {
      title: t('当前进度'),
      dataIndex: 'currentProgress',
      align: 'left',
      resizable: true,
      slots: { customRender: 'currentProgress' },
    },
    {
      title: t('摘要信息'),
      align: 'left',
      resizable: true,
      dataIndex: 'summaryInfo',
      width: 200,
    },
  ];

  const visibleLookProcess = ref(false);
  const taskIdProcess = ref('');
  const processIdProcess = ref('');

  const [registerModal, { openModal }] = useModal();
  const { formConfig, data, processId, taskId, schemaId, selectionChange, showBatchApproval } =
    userTaskTable();
  function BatchClearHandler(v) {
    if (v) {
      openModal(true, v);
    }
    clearSelectedRowKeys();
  }
  const clearHandler = () => {
    clearSelectedRowKeys();
    reload();
  };
  const [registerTable, { reload, clearSelectedRowKeys }] = useTable({
    title: t('待办任务列表'),
    api: getSchemaTask,
    rowKey: 'id',
    columns: configColumns,
    formConfig: formConfig(),
    beforeFetch: (params) => {
      return { data: params, taskUrl: TaskTypeUrl.PENDING_TASKS };
    },
    rowSelection: {
      type: 'checkbox',
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    pagination: {
      pageSize: 18,
    },
    showIndexColumn: false,
    tableSetting: {
      size: false,
    },
    actionColumn: {
      width: 60,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });
  const { currentRoute } = useRouter();
  watch(
    () => unref(currentRoute),
    (val) => {
      if (val.name == 'ProcessTasks') reload();
    },
    { deep: true },
  );

  const handleView = (record) => {
    visibleLookProcess.value = true;
    taskIdProcess.value = record.taskId;
    processIdProcess.value = record.processId;
  };

  const handleLookClose = () => {
    visibleLookProcess.value = false;
  };
</script>

<style scoped></style>
