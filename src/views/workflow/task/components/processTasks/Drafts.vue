<template>
  <BasicTable @register="registerTable">
    <template #action="{ record }">
      <TableAction
        :actions="[
          {
            icon: 'clarity:note-edit-line',
            auth: 'processtasks:edit',
            onClick: handleEdit.bind(null, record),
          },
          {
            icon: 'ant-design:delete-outlined',
            auth: 'processtasks:delete',
            color: 'error',
            popConfirm: {
              title: t('是否确认删除'),
              confirm: handleDelete.bind(null, record),
            },
          },
        ]"
      />
    </template>
  </BasicTable>
  <LaunchProcess
    v-if="processData.visible"
    :draftsId="processData.draftsId"
    :schemaId="processData.schemaId"
    :draftsJsonStr="processData.draftsJsonStr"
    @close="
      () => {
        processData.visible = false;
        reload();
      }
    "
  />
</template>

<script setup lang="ts">
  import userTaskTable from './../../hooks/userTaskTable';

  import LaunchProcess from './../LaunchProcess.vue';
  import { BasicTable, useTable, TableAction, BasicColumn } from '/@/components/Table';
  import { deleteDraft, getDraftInfo, getSchemaTask } from '/@/api/workflow/process';
  import { reactive } from 'vue';
  import { notification } from 'ant-design-vue';
  import { TaskTypeUrl } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const configColumns: BasicColumn[] = [
    {
      title: t('流程名称'),
      dataIndex: 'schemaName',
      align: 'left',
    },
    {
      title: t('发起者'),
      dataIndex: 'originator',
      sorter: true,
      align: 'left',
    },
    {
      title: t('发起时间'),
      dataIndex: 'createDate',
      align: 'left',
    },
  ];
  const processData = reactive({
    visible: false,
    schemaId: '',
    draftsJsonStr: '',
    draftsId: '',
  });
  const { formConfig } = userTaskTable();
  const [registerTable, { reload }] = useTable({
    title: t('草稿箱列表'),
    api: getSchemaTask,
    rowKey: 'id',
    columns: configColumns,
    formConfig: formConfig('Drafts'),
    beforeFetch: (params) => {
      return { data: params, taskUrl: TaskTypeUrl.DRAFT };
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    pagination: {
      pageSize: 18,
    },
    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });
  async function handleEdit(record: Recordable) {
    try {
      let res = await getDraftInfo(record.id);
      processData.draftsId = record.id;
      processData.schemaId = res.schemaId;
      processData.draftsJsonStr = res.formData;
      processData.visible = true;
    } catch (error) {}
  }

  async function handleDelete(record: Recordable) {
    try {
      let res = await deleteDraft([record.id]);
      if (res) {
        notification.open({
          type: 'success',
          message: t('删除'),
          description: t('删除成功'),
        });
        reload();
      } else {
        notification.open({
          type: 'error',
          message: t('删除'),
          description: t('删除失败'),
        });
      }
    } catch (error) {}
  }
</script>

<style scoped></style>
