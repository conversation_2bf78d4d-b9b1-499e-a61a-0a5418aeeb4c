<template>
  <BasicTable @register="registerTable">
    <template #currentProgress="{ record }">
      <a-progress
        v-if="typeof record.currentProgress === 'number' && isFinite(record.currentProgress)"
        :percent="record.currentProgress"
        size="small"
      />
    </template>
    <template #action="{ record }">
      <TableAction
        :actions="[
          {
            icon: 'chehui|svg',
            auth: 'processtasks:withdraw',
            tooltip: '撤回',
            onClick: handleWithdraw.bind(null, record),
          },
          {
            icon: 'ant-design:eye-outlined',
            auth: 'processtasks:view',
            tooltip: '查看',
            onClick: handleView.bind(null, record),
          },
          {
            icon: 'zhongxinfaqi|svg',
            auth: 'processtasks:relaunch',
            tooltip: '重新发起',
            onClick: restartProcess.bind(null, record),
          },
          {
            icon: 'cuiban|svg',
            auth: 'processtasks:urge',
            tooltip: '催办',
            onClick: handleUrge.bind(null, record),
          },
          {
            icon: 'ant-design:delete-outlined',
            auth: 'processtasks:delete',
            color: 'error',
            popConfirm: {
              title: t('移入回收站'),
              confirm: handleDelete.bind(null, record),
            },
          },
        ]"
      />
    </template>
  </BasicTable>
  <LaunchProcess
    v-if="restartProcessVisible"
    :schemaId="schemaIdProcess"
    :taskId="taskIdProcess"
    :processId="processIdProcess"
    :withdrawId="withdrawId"
    :isWithdraw="withdrawId === 'Event_start_node'"
    @close="restartProcessClose"
  />
  <RejectProcess
    v-if="visibleRejectProcess"
    :visible="visibleRejectProcess"
    :processId="processIdProcess"
    :taskId="taskIdProcess"
    @close="handleRejectClose"
    @restart="withdrawProcess"
  />
  <LookProcess
    v-if="visibleLookProcess"
    :visible="visibleLookProcess"
    :taskId="taskIdProcess"
    :processId="processIdProcess"
    @close="visibleLookProcess = false"
  />
  <UrgeProcess
    v-if="visibleUrgeProcess"
    :visible="visibleUrgeProcess"
    :taskId="taskIdProcess"
    :processId="processIdProcess"
    :status="urgeStatus"
    @close="handleUrgeClose"
  />
</template>

<script setup lang="ts">
  import userTaskTable from './../../hooks/userTaskTable';

  import { ref, unref, watch, h } from 'vue';

  import LookProcess from './../LookProcess.vue';
  import LaunchProcess from './../LaunchProcess.vue';
  import RejectProcess from './../RejectProcess.vue';
  import UrgeProcess from './../UrgeProcess.vue';

  import { BasicTable, useTable, TableAction, BasicColumn } from '/@/components/Table';
  import { getSchemaTask, moveRecycle } from '/@/api/workflow/process';
  import { notification } from 'ant-design-vue';
  import { TaskTypeUrl } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useRouter } from 'vue-router';
  const { t } = useI18n();
  const restartProcessVisible = ref(false);
  const withdrawId = ref();
  const configColumns: BasicColumn[] = [
    {
      title: t('流水号'),
      dataIndex: 'serialNumber',
      width: 80,
      sorter: true,
    },
    {
      title: t('流程名称'),
      dataIndex: 'processName',
      align: 'left',
      sorter: true,
    },
    {
      title: t('任务名称'),
      dataIndex: 'currentTaskName',
      sorter: true,
      align: 'left',
    },
    {
      title: t('当前任务耗时'),
      dataIndex: 'consumingTime',
      align: 'left',
      customRender: ({ record }) => {
        const color = record.consumingTime === '审批结束' ? '#b0f323' : '';
        return h('span', { style: { color } }, record.consumingTime);
      },
    },
    {
      title: t('流程状态'),
      dataIndex: 'status',
      align: 'left',
      customRender: ({ record }) => {
        const color = record.status === '审批结束' ? '#b0f323' : '#69acff';
        return h('span', { style: { color } }, record.status);
      },
      width: 130,
    },
    {
      title: t('当前进度'),
      dataIndex: 'currentProgress',
      sorter: true,
      slots: { customRender: 'currentProgress' },
      width: 140,
    },
    {
      title: t('摘要信息'),
      align: 'left',
      dataIndex: 'summaryInfo',
      width: 200,
    },
  ];

  const visibleLookProcess = ref(false);
  const visibleRejectProcess = ref(false);
  const visibleUrgeProcess = ref(false);

  const taskIdProcess = ref('');
  const processIdProcess = ref('');
  const schemaIdProcess = ref('');
  const urgeStatus = ref('');

  const { formConfig } = userTaskTable();

  const [registerTable, { reload }] = useTable({
    title: t('我的流程列表'),
    api: getSchemaTask,
    rowKey: 'id',
    columns: configColumns,
    formConfig: formConfig('MyProcess'),
    beforeFetch: (params) => {
      return { data: params, taskUrl: TaskTypeUrl.MY_PROCESS };
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    showIndexColumn: false,
    pagination: {
      pageSize: 18,
    },
    actionColumn: {
      width: 210,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
  });
  function withdrawProcess(v) {
    withdrawId.value = v;
    restartProcessVisible.value = true;
  }
  function restartProcess(record) {
    withdrawId.value = '';
    schemaIdProcess.value = record.schemaId;
    restartProcessVisible.value = true;
  }
  function restartProcessClose() {
    restartProcessVisible.value = false;
    reload();
  }
  async function handleDelete(record: Recordable) {
    if (record.processId) {
      try {
        let res = await moveRecycle(record.processId);
        if (res) {
          notification.open({
            type: 'success',
            message: t('移入回收站'),
            description: t('移入回收站成功'),
          });
          reload();
        } else {
          notification.open({
            type: 'error',
            message: t('移入回收站'),
            description: t('移入回收站失败'),
          });
        }
      } catch (error) {}
    }
  }

  function handleUrgeClose() {
    visibleUrgeProcess.value = false;
    reload();
  }

  const handleRejectClose = () => {
    visibleRejectProcess.value = false;
    reload();
  };

  const handleView = (record) => {
    visibleLookProcess.value = true;
    taskIdProcess.value = record.taskId;
    processIdProcess.value = record.processId;
  };

  const handleWithdraw = (record) => {
    visibleRejectProcess.value = true;
    taskIdProcess.value = record.taskId;
    schemaIdProcess.value = record.schemaId;
    processIdProcess.value = record.processId;
  };
  const handleUrge = (record) => {
    visibleUrgeProcess.value = true;
    taskIdProcess.value = record.taskId;
    processIdProcess.value = record.processId;
    urgeStatus.value = record.status;
  };
  const { currentRoute } = useRouter();
  watch(
    () => unref(currentRoute),
    (val) => {
      if (val.name == 'ProcessTasks') reload();
    },
    { deep: true },
  );
</script>

<style scoped></style>
