<template>
  <div class="wrap">
    <div class="empty-box">
      <IconFontSymbol icon="history" class="empty-icon" />
      <div class="title">{{ title }}</div>
      <div class="desc">{{ desc }}</div>
    </div>
  </div>
</template>
<script lang="ts">
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const defaultMsg = t('暂无数据');

  export default {};
</script>
<script setup lang="ts">
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  defineProps({
    title: {
      type: String,
      default: defaultMsg,
    },
    desc: {
      type: String,
      default: defaultMsg,
    },
  });
</script>

<style lang="less" scoped>
  .wrap {
    width: 100%;
    height: 100%;
    min-height: 80px;
  }

  .empty-box {
    height: 80vh;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .empty-icon {
    font-size: 60px;
  }

  .title {
    font-weight: 700;
    margin: 6px 0;
    color: #666666;
  }

  .desc {
    font-size: 14px;
    color: #999999;
    margin: 6px 0;
  }
</style>
