<template>
  <a-modal
    :width="1000"
    :visible="true"
    :title="t('下一节点审批人')"
    :maskClosable="false"
    :closable="false"
    :cancel-button-props="{
      disabled: true,
    }"
    :okText="t('确认')"
    :cancelText="t('取消')"
    @ok="submit"
  >
    <div class="p-5">
      <NodeHead :nodeName="t('信息')" />
      <div class="mt-5 mb-5 ml-5">{{
        t('请在十分钟内指定相关审批人员，时限内未完成指定的话，系统将按照现有默认人员进行处理。')
      }}</div>
      <a-tabs v-model:activeKey="data.index">
        <a-tab-pane v-for="(item, index) in data.tasks" :key="index" :tab="item.taskName">
          <SelectApproveUser
            :schemaId="props.schemaId"
            :taskId="item.taskId"
            :hasMoreBtn="item.provisionalApprover"
            v-model:select-ids="data.tasks[index].selectIds"
          />
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { onMounted, reactive } from 'vue';
  import SelectApproveUser from './SelectApproveUser.vue';
  import { batchApproverUsers } from '/@/api/workflow/task';
  import { NodeHead } from '/@/components/ModalPanel/index';
  import { ApproveTask, BatchApproverUsersParams } from '/@/model/workflow/bpmnConfig';
  import { notification } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      taskList: Array<ApproveTask>;
      schemaId: string | undefined;
    }>(),
    {
      taskList: () => {
        return [];
      },
      schemaId: '',
    },
  );

  let emits = defineEmits(['change']);
  let data: {
    index: number;
    tasks: Array<ApproveTask>;
  } = reactive({
    index: 0,
    tasks: [],
  });
  onMounted(() => {
    if (props.schemaId && props.taskList.length > 0) {
      props.taskList.forEach(async (item) => {
        data.tasks.push({
          taskId: item.taskId,
          taskName: item.taskName,
          provisionalApprover: item.provisionalApprover,
          selectIds: [],
        });
      });
    }
  });
  async function submit() {
    let approveUserList: BatchApproverUsersParams = [];
    let msgs: Array<string> = [];
    data.tasks.forEach((element) => {
      if (element.selectIds.length > 0) {
        approveUserList.push({
          taskId: element.taskId,
          approvedUsers: element.selectIds,
        });
      } else {
        msgs.push(t('任务：') + element.taskName + t('未选择下一节点审批人'));
      }
    });
    if (msgs.length > 0) {
      msgs.forEach((msg) => {
        notification.open({
          type: 'error',
          message: t('下一节点审批人'),
          description: msg,
        });
      });
    } else {
      try {
        if (props.schemaId) {
          let res = await batchApproverUsers(props.schemaId, approveUserList);
          if (res) {
            emits('change');
          } else {
            notification.open({
              type: 'error',
              message: t('下一节点审批人'),
              description: t('选择下一节点审批人失败'),
            });
          }
        }
      } catch (_error) {
        notification.open({
          type: 'error',
          message: t('下一节点审批人'),
          description: t('选择下一节点审批人失败'),
        });
      }
    }
  }
</script>

<style lang="less" scoped>
  :deep(.list-page-box) {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    padding: 10px 0;
  }
</style>
