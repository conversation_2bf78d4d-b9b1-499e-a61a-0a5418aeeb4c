<template>
  <a-button class="mr-2" @click="show">{{ t('加签或减签') }}</a-button>
  <a-modal
    :width="1000"
    :visible="data.visible"
    :title="t('加签或减签')"
    :maskClosable="false"
    @ok="submit"
    @cancel="cancel"
  >
    <div class="p-5 box" v-if="data.visible">
      <SelectApproveUser
        :schemaId="props.schemaId"
        :taskId="props.taskId"
        v-model:select-ids="data.selectedIds"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import SelectApproveUser from './SelectApproveUser.vue';
  import { postSetSign } from '/@/api/workflow/task';
  import { notification } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    schemaId: {
      type: String,
      // required: true,
    },
    processId: {
      type: String,
      // required: true,
    },
    taskId: {
      type: String,
      // required: true,
    },
  });
  let data: {
    visible: boolean;
    selectedIds: Array<string>;
  } = reactive({
    visible: false,
    selectedIds: [],
  });
  function show() {
    data.selectedIds = [];
    data.visible = true;
  }
  function cancel() {
    data.selectedIds = [];
    data.visible = false;
  }
  async function submit() {
    let msgs: Array<string> = [];
    if (msgs.length > 0) {
      msgs.forEach((msg) => {
        notification.open({
          type: 'error',
          message: t('加签减签'),
          description: msg,
        });
      });
    } else {
      try {
        if (props.schemaId && props.taskId) {
          await postSetSign(props.schemaId, props.taskId, data.selectedIds);
          cancel();
        }
      } catch (_error) {
        notification.open({
          type: 'error',
          message: t('加签减签'),
          description: t('选择加签减签失败'),
        });
      }
    }
  }
</script>

<style lang="less" scoped>
  .box {
    height: 500px;
  }

  .list-page-box {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    padding: 10px 0;
  }
</style>
