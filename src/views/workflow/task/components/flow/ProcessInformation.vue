<template>
  <!--  流程信息 -->
  <div class="flow-record-box">
    <div id="bpmnCanvas" class="canvas" ref="bpmnCanvas"></div>
    <div class="flow-record-mark"></div>
  </div>

  <div class="fixed-bottom">
    <ZoomInOrOut @in="zoomViewport(false)" @out="zoomViewport(true)" />
  </div>
  <ProcessDealInfo v-if="processId" :processId="processId" @btnClick="changeIsshow" />
</template>
<script lang="ts" setup>
  import CustomModeler from '/@bpmn/modeler';
  import { ZoomInOrOut } from '/@/components/ModalPanel';
  import { getFinishedTask } from '/@/api/workflow/task';
  import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';
  import { ref, reactive, onMounted } from 'vue';
  import ProcessDealInfo from './ProcessDealInfo.vue';

  const props = withDefaults(
    defineProps<{
      xml: string;
      processId: string;
    }>(),
    {
      xml: '',
      processId: '',
    },
  );
  const bpmnCanvas = ref();
  const height = ref('70vh');
  let data: {
    bpmnViewer: any;
    zoom: number;
    xmlString: string;
  } = reactive({
    bpmnViewer: null,
    zoom: 1,
    xmlString: '',
  });
  onMounted(() => {
    data.xmlString = props.xml;
    if (data.xmlString) initBpmnModeler();
  });

  async function initBpmnModeler() {
    data.bpmnViewer = await new CustomModeler({
      container: bpmnCanvas.value,
      additionalModules: [
        MoveCanvasModule,
        {
          labelEditingProvider: ['value', ''], //禁用节点编辑
          paletteProvider: ['value', ''], //禁用/清空左侧工具栏
          contextPadProvider: ['value', ''], //禁用图形菜单
          bendpoints: ['value', {}], //禁用连线拖动
          // zoomScroll: ['value', ''], //禁用滚动
          // moveCanvas: ['value', ''], //禁用拖动整个流程图
          move: ['value', ''], //禁用单个图形拖动
        },
      ],
    });
    await redrawing();
    if (props.processId) {
      let res = await getFinishedTask(props.processId);
      setColors(
        res.finishedNodes ? res.finishedNodes : [],
        res.currentNodes ? res.currentNodes : [],
      );
    }
  }
  async function redrawing() {
    try {
      await data.bpmnViewer.importXML(data.xmlString);
      let canvas = data.bpmnViewer.get('canvas');
      canvas.zoom('fit-viewport', 'auto');
    } catch (err) {
      console.log('err: ', err);
    }
  }
  function setColors(finishedIds: Array<string>, currentIds: Array<string>) {
    // finishedIds 完成的节点id
    // currentIds 进行中节点id
    let modeling = data.bpmnViewer.get('modeling');
    const elementRegistry = data.bpmnViewer.get('elementRegistry');
    if (finishedIds.length > 0) {
      finishedIds.forEach((it) => {
        let Event = elementRegistry.get(it);

        modeling.setColor(Event, {
          stroke: 'green',
          fill: 'white',
        });
      });
    }
    if (currentIds.length > 0) {
      currentIds.forEach((it) => {
        let Event = elementRegistry.get(it);
        modeling.setColor(Event, {
          stroke: '#409eff',
          fill: 'white',
        });
      });
    }
  }
  function zoomViewport(zoomIn = true) {
    data.zoom = data.bpmnViewer.get('canvas').zoom();
    data.zoom += zoomIn ? 0.1 : -0.1;
    data.bpmnViewer.get('canvas').zoom(data.zoom);
  }

  function changeIsshow(isShow) {
    height.value = isShow ? '50vh' : '70vh';
  }
</script>
<style lang="less">
  @import url('/@/assets/style/bpmn-js/diagram-js.css');
  @import url('/@/assets/style/bpmn-js/bpmn-font/css/bpmn.css');
  @import url('/@/assets/style/bpmn-js/bpmn-font/css/bpmn-codes.css');
  @import url('/@/assets/style/bpmn-js/bpmn-font/css/bpmn-embedded.css');

  .bjs-powered-by {
    display: none !important;
  }

  .flow-record-box {
    width: 100%;
    height: v-bind(height);
    position: relative;
    margin-top: 50px;
  }

  .flow-record-mark {
    position: absolute;
    inset: 0;
    z-index: 1;
  }

  /* 画布 */
  .canvas {
    width: 100%;
    height: 100%;
  }

  /* 按钮（放大 缩小 清除） */
  .fixed-bottom {
    position: absolute;
    top: 110px;
    font-size: 30px;
    left: 40%;
    display: flex;
  }

  :deep(.ant-tabs),
  :deep(.ant-tabs-content),
  .full-box {
    height: 100%;
  }
</style>
