<template>
  <a-tabs :tab-position="props.position" v-model:activeKey="activeKey">
    <a-tab-pane :key="1" :tab="t('表单信息')" force-render><slot></slot></a-tab-pane>
    <a-tab-pane :key="2" :tab="t('流程信息')">
      <ProcessInformation :xml="xml" :processId="processId"
    /></a-tab-pane>
    <a-tab-pane :key="3" :tab="t('流转记录')" style="overflow: auto"
      ><FlowRecord :list="taskRecords" :processId="processId"
    /></a-tab-pane>
    <a-tab-pane :key="4" :tab="t('附件汇总')"
      ><SummaryOfAttachments :processId="processId"
    /></a-tab-pane>
    <a-tab-pane :key="5 + index" v-for="(item, index) in predecessorTasks" :tab="item.schemaName">
      <LookRelationTask
        v-if="activeKey === 5 + index"
        :taskId="item.taskId"
        :processId="item.processId"
        position="left"
      />
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
  import FlowRecord from './FlowRecord.vue';
  import ProcessInformation from './ProcessInformation.vue';
  import SummaryOfAttachments from './SummaryOfAttachments.vue';
  import LookRelationTask from './LookRelationTask.vue';
  import { ref } from 'vue';
  import { SchemaTaskItem } from '/@/model/workflow/bpmnConfig';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let props = withDefaults(
    defineProps<{
      position: string;
      xml: string | undefined;
      taskRecords: Array<any> | undefined;
      processId: string | undefined;
      predecessorTasks: Array<SchemaTaskItem> | undefined;
    }>(),
    {
      xml: '',
      processId: '',
      predecessorTasks: () => {
        return [];
      },
    },
  );

  const activeKey = ref(1);
</script>
