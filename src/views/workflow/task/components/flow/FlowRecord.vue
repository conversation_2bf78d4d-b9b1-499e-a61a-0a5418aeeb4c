<template>
  <div class="flow-box">
    <!-- 流转记录 -->
    <EmptyBox
      v-if="results.length == 0"
      :title="t('当前无相关流转记录')"
      :desc="t('流程需发起后才会有流转记录产生')"
    />
    <div class="relative" v-else>
      <a-button type="primary" class="!absolute right-0 cursor-pointer z-99" @click="selfHandle">{{
        btnText ? '仅查看本人' : '查看所有'
      }}</a-button>
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane v-for="(parent, idx) in results" :key="idx" :tab="t(parent.schemaName)">
          <div class="content">
            <div class="icon-box">
              <IconFontSymbol icon="shijian1-copy" fill-color="#5e95ff" />
            </div>
            <div class="head-line-box"> </div>
            <div class="card-box" v-for="(item, index) in parent.records" :key="index">
              <div class="card-item">
                <div class="dot-box"
                  ><div class="line2"></div><div class="dot"></div><div class="line"></div
                ></div>
                <div class="card">
                  <div class="time-box">
                    <div class="time-title">{{ item.startTime }}</div>
                  </div>
                  <div class="node-box">
                    <div class="node-title">{{ t('节点名称') }}</div>
                    <div class="node-desc">{{ item.nodeName }}</div>
                  </div>
                  <div class="node-box" v-if="item.circulateMessage">
                    <div class="node-title">{{ t('传阅信息') }}</div>
                    <div class="node-desc">{{ item.circulateMessage }}</div>
                  </div>
                  <div class="node-box">
                    <div class="node-title">{{ t('审批信息') }}</div>
                    <div class="node-desc">{{ item.comment }}</div>
                  </div>
                </div>
              </div>
            </div></div
          >
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import EmptyBox from './EmptyBox.vue';
  import { getSelfRecords } from '/@/api/workflow/process';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { TaskRecordList } from '/@/model/workflow/bpmnConfig';
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  const { t } = useI18n();

  const props = defineProps({
    list: { type: Array as PropType<TaskRecordList[]> },
    processId: String,
  });
  const results = ref<TaskRecordList[]>(props.list || []);
  const activeKey = ref(0);
  const btnText = ref(true);
  onMounted(() => {
    if (!props.list) getRecords(0);
  });
  function selfHandle() {
    results.value = [];
    if (btnText.value) {
      getRecords(1);
    } else {
      if (props.list) {
        results.value = props.list;
      } else {
        getRecords(0);
      }
    }

    btnText.value = !btnText.value;
  }
  function getRecords(isSelf) {
    getSelfRecords({ processId: props.processId, onlySelf: isSelf }).then((res) => {
      if (res.taskRecords) {
        results.value.push({
          records: res.taskRecords,
          schemaName: '当前流程',
        });
      }

      if (res.otherProcessApproveRecord) {
        results.value = results.value.concat(res.otherProcessApproveRecord);
      }
    });
  }
</script>

<style lang="less" scoped>
  .flow-box {
    height: 80vh;
    padding: 0 20px;
  }

  .content {
    margin-left: 30px;
  }

  .icon-box {
    width: 30px;
    height: 30px;
    font-size: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: -4px;
  }

  .head-line-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 28px;
    border-left: 2px solid #f1f1f1;
    margin-left: 8px;
  }

  .dot-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .dot {
      width: 18px;
      height: 18px;
      border: 4px solid #e0eaff;
      border-radius: 50%;
      background-color: #5e95ff;
    }

    .line2 {
      width: 2px;
      height: 18px;
      background-color: #f1f1f1;
    }

    .line {
      width: 2px;
      flex: 1;
      background-color: #f1f1f1;
    }
  }

  .card-item {
    display: flex;
  }

  .card {
    padding: 0 20px;

    .time-box {
      margin: 20px 0;

      .time-title {
        font-weight: 500;
        font-size: 14px;
        color: #999;
      }
    }

    .node-box {
      display: flex;
      align-items: center;
      margin: 8px 0;

      .node-title {
        width: 58px;
        height: 20px;
        background: #ccc;
        border-radius: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 800;
        font-size: 12px;
        color: #fff;
      }

      .node-desc {
        font-weight: bold;
        font-size: 16px;
        color: #000;
        margin-left: 10px;
      }
    }
  }
</style>
