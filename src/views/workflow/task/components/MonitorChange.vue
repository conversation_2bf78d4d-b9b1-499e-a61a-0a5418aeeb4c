<template>
  <a-modal
    v-model:visible="visible"
    title="节点变更"
    width="400px"
    @ok="handleSubmit"
    @cancel="close"
    :maskClosable="false"
    :okText="t('确认')"
    :cancelText="t('取消')"
    :bodyStyle="{ padding: '20px', height: '400px', overflow: 'auto' }"
  >
    <div>节点列表</div>
    <a-divider />
    <a-radio-group v-model:value="nodeValue">
      <a-radio
        :style="radioStyle"
        v-for="item in nodeData"
        :key="item.activityId"
        :value="item.activityId"
      >
        {{ item.activityName }}
      </a-radio>
    </a-radio-group>
  </a-modal>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { getAllProcessNodes, changeNode } from '/@/api/workflow/monitor';
  import { AllNodesModal } from '/@/api/workflow/model/index';
  import { notification } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';

  const props = defineProps({
    processId: {
      type: String,
      default: '',
    },
    taskId: {
      type: String,
      default: '',
    },
  });
  const { t } = useI18n();
  const emits = defineEmits(['close', 'success']);
  const visible = ref(true);
  const nodeValue = ref('');
  const nodeData = ref<AllNodesModal[]>([]);
  const radioStyle = reactive({
    display: 'flex',
    height: '40px',
  });

  onMounted(async () => {
    nodeData.value = await getAllProcessNodes({
      processId: props.processId,
      taskId: props.taskId,
    });
  });
  const handleSubmit = async () => {
    if (!nodeValue.value) {
      notification.open({
        type: 'error',
        message: '变更',
        description: '请选择变更节点',
      });
      return;
    }

    await changeNode({
      processId: props.processId,
      taskId: props.taskId,
      activityId: nodeValue.value,
    });
    emits('success');
  };
  const close = () => {
    emits('close');
  };
</script>
