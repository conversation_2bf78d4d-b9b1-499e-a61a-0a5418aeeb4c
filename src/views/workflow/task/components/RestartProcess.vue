<template>
  <span @click.stop="restart"
    ><slot></slot>
    <LaunchProcess
      v-if="visibleRef"
      :schemaId="schemaId"
      :taskId="taskId"
      :processId="processId"
      @close="close"
    />
  </span>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import LaunchProcess from './LaunchProcess.vue';
  const props = defineProps({
    schemaId: {
      type: String,
      required: true,
    },
    taskId: {
      type: String,
    },
    processId: {
      type: String,
    },
    visible: {
      default: false,
      type: Boolean,
    },
  });

  onMounted(() => {
    if (props.visible) {
      restart();
    }
  });

  let emits = defineEmits(['close']);
  let visibleRef = ref(false);
  function restart() {
    if (props.taskId) {
      visibleRef.value = true;
    } else {
      // 只能选一个
      visibleRef.value = false;
    }
  }
  function close() {
    visibleRef.value = false;
    emits('close');
  }
</script>
