<template>
  <template v-if="componentType === 'form'">
    <a-row :style="borderColorStyle">
      <a-col :span="3" :style="borderColorStyle">
        {{ item?.label }}
      </a-col>
      <a-col :span="21" :style="borderColorStyle">
        <a-table
          :dataSource="item?.value"
          :columns="item?.columns"
          :pagination="false"
          tableLayout="fixed"
          v-if="item?.value.length"
        />
        <!-- <table :cellpadding="10" :cellspacing="10" v-if="item?.value.length">
          <thead>
            <tr>
              <th :style="borderColorStyle" v-for="title in Object.keys(item?.value[0])">
                {{ title }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="value in item?.value">
              <td :style="borderColorStyle" v-for="subFormInfo in Object.values(value)">
                {{ subFormInfo }}
              </td>
            </tr>
          </tbody>
        </table> -->
      </a-col>
    </a-row>
  </template>
  <table
    :cellpadding="10"
    :cellspacing="10"
    v-else-if="item?.type === 'tab' || item?.type === 'card'"
  >
    <template v-if="Object.keys(item?.value)">
      <thead>
        <tr>
          <th
            :style="borderColorStyle"
            v-for="(title, index) in Object.keys(item?.value)"
            :key="index"
          >
            {{ title }}
          </th>
        </tr>
      </thead>
      <tbody>
        <template v-for="(value, index) in item?.value" :key="index">
          <tr v-if="!!value.length">
            <td :style="borderColorStyle" :colspan="Object.keys(item?.value).length">
              <template v-for="(info, idx) in Object.values(value)" :key="idx">
                <TableStyle :item="info" :componentType="info.type" />
              </template>
            </td>
          </tr>
        </template>
      </tbody>
    </template>
  </table>
  <div class="grid-box" v-else-if="item?.type === 'grid'">
    <div class="grid-box-left">
      <TableStyle
        v-for="(gridItem, index) in item.value[0]"
        :key="index"
        :item="gridItem"
        :componentType="gridItem.type"
      />
    </div>
    <div class="w-1/2">
      <TableStyle
        v-for="(gridItem, index) in item.value[1]"
        :key="index"
        :item="gridItem"
        :componentType="gridItem.type"
      />
    </div>
  </div>
  <div
    v-else-if="noBorderComponent.includes(item?.type)"
    :style="item?.type === 'title' ? underlineStyle : ''"
    class="noborder-box"
  >
    <a-divider
      v-if="item?.type === 'divider'"
      :orientation="item?.componentProps?.orientation"
      :style="item?.componentProps?.style"
    >
      {{ item?.label }}
    </a-divider>
    <h2
      v-else-if="item?.type === 'title'"
      :align="item.componentProps.align"
      :style="{
        fontWeight: 'bold',
        fontSize: item.componentProps.fontSize + 'px',
        color: item.componentProps.color,
        ...item.componentProps.style,
      }"
    >
      {{ item?.label }}
    </h2>
  </div>
  <template v-else>
    <a-row :style="borderColorStyle">
      <a-col :span="3" :style="borderColorStyle">
        {{ item?.label }}
      </a-col>
      <a-col :span="21">
        <span>{{ item?.value }}</span>
      </a-col>
    </a-row>
  </template>
</template>
<script lang="ts" setup>
  import { inject, StyleValue, Ref } from 'vue';

  defineProps({
    item: Object,
    componentType: String,
  });
  const borderColorStyle = inject<Ref<StyleValue>>('borderColorStyle')!;
  const underlineStyle = inject<Ref<StyleValue>>('underlineStyle')!;
  const noBorderComponent = ['title', 'divider'];
</script>
<style lang="less" scoped>
  table {
    width: 100%;

    thead {
      text-align: left;
    }

    td,
    th {
      border: 1px solid;
    }
  }

  :deep(.ant-table-thead) tr th {
    border: 1px solid;
    background: #fff;
  }

  :deep(.ant-table-tbody) tr td {
    border: 1px solid;
    border-bottom: 1px solid #000;
  }

  .noborder-box {
    width: 100%;
    padding: 10px;
  }

  .grid-box {
    display: flex;
    border: 1px solid;
    width: 100%;

    .grid-box-left {
      border-right: 1px solid;
      width: 50%;
    }

    .ant-row {
      border: 0 !important;
    }

    .ant-row:not(:last-child) {
      border-bottom: 1px solid !important;
    }
  }
</style>
