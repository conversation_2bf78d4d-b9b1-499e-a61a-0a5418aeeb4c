<template>
  <ProcessLayout class="wrap" @click.stop="">
    <template #title> {{ t('查看流程') }} </template>
    <template #close>
      <a-button type="primary" class="clean-icon" @click.stop="close">{{ t('关闭') }}</a-button>
    </template>
    <template #full>
      <LookTask :processId="props.processId" />
    </template>
  </ProcessLayout>
</template>

<script setup lang="ts">
  import ProcessLayout from './flow/Layout.vue';
  import LookTask from './flow/LookTask.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let props = defineProps({
    processId: String,
  });
  let emits = defineEmits(['close']);

  function close() {
    emits('close');
  }
</script>
