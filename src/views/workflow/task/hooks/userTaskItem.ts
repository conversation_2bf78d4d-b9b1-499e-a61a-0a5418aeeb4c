import { reactive } from 'vue';
import { ApproveTask, BpmnFlowForm, FlowInfo } from '/@/model/workflow/bpmnConfig';
import { notification } from 'ant-design-vue';
import { getDicDetailList } from '/@/api/system/dic';
import { TreeItem } from '/@/components/Tree';
import { ElectronicSignatureVerification, FlowCategory } from '/@/enums/workflowEnum';

import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
export default function () {
  const approveUserData: {
    visible: boolean;
    list: Array<ApproveTask>;
    schemaId: string;
  } = reactive({
    visible: false,
    list: [],
    schemaId: '',
  });
  const data: BpmnFlowForm = reactive({
    xml: '',
    item: { id: '', code: '', categoryName: '', name: '', remark: '' }, //工作流模板信息
    formInfos: [],
    relationTasks: [],
    taskRecords: [],
    taskApproveOpinions: {},
    predecessorTasks: [],
    opinions: [],
    opinionsComponents: [],
    hasStamp: false,
    hasStampPassword: false,
    submitLoading: false,
    formAssignmentData: null,
  });
  function initProcessData(res: FlowInfo, isStaging = false) {
    data.item.id = res.schemaInfo.id;
    data.item.name = res.schemaInfo.name;
    data.item.code = res.schemaInfo.code;
    data.item.remark = res.schemaInfo.remark;
    data.taskApproveOpinions = {};
    data.predecessorTasks = [];
    data.opinions = [];
    data.opinionsComponents = [];
    data.hasStamp = false;
    data.hasStampPassword = false;
    data.submitLoading = false;
    data.xml = '';
    if (res.schemaInfo.xmlContent) {
      data.xml = res.schemaInfo.xmlContent;
    }
    data.formInfos = [];
    if (res.formInfos) {
      if (isStaging && res.approveDto) {
        res.formInfos.map((info) => {
          info.formData = res.approveDto?.formData[info.formConfig.key];
        });
      }
      data.formInfos = res.formInfos;
    }
    data.taskRecords = [];
    if (res.taskRecords) {
      data.taskRecords.push({
        records: res.taskRecords,
        schemaName: '当前流程',
      });
    }

    if (res.otherProcessApproveRecord) {
      data.taskRecords = data.taskRecords.concat(res.otherProcessApproveRecord);
    }
    data.taskApproveOpinions = {};
    if (res.taskApproveOpinions) {
      data.taskApproveOpinions = res.taskApproveOpinionListMap || {};
    }

    if (res.formAssignmentData) {
      data.formAssignmentData = res.formAssignmentData;
    }

    if (res.relationTasks) {
      data.relationTasks = res.relationTasks;
      data.relationTasks.forEach((element) => {
        data.predecessorTasks.push({
          schemaId: element.schemaId,
          schemaName: element.schemaName,
          taskId: '',
          taskName: '',
          processId: '',
        });
      });
    }
    if (res.opinionConfig) {
      if (res.opinionConfig.enabled) {
        data.hasStamp = true;
        if (res.opinionConfig.signature === ElectronicSignatureVerification.PASSWORD_REQUIRED) {
          data.hasStampPassword = true;
        }
        if (res.opinionConfig.component && res.opinionConfig.component.length > 0) {
          data.opinionsComponents =
            typeof res.taskApproveOpinionListMap === 'object'
              ? Object.keys(res.taskApproveOpinionListMap)
              : [];
          getOpinionFormData();
        }
      }
    }
    if (res.formAssignmentData) {
      data.formAssignmentData = res.formAssignmentData;
    }
    setCategoryName(res.schemaInfo.category);
  }
  async function setCategoryName(id: string) {
    const res = (await getDicDetailList({
      itemId: FlowCategory.ID,
    })) as unknown as TreeItem[];
    const categoryItem = res.filter((ele) => {
      return ele.id === id;
    });
    data.item.categoryName =
      categoryItem.length > 0 ? (categoryItem[0].name ? categoryItem[0].name : '') : '';
  }
  function notificationError(title: string, description = t('失败')) {
    notification.open({
      type: 'error',
      message: title,
      description,
    });
  }
  function notificationSuccess(title: string) {
    notification.open({
      type: 'success',
      message: title,
      description: title + t('成功'),
    });
  }
  function getOpinionFormData() {
    data.opinions = data.taskApproveOpinions;
  }
  return {
    data,
    approveUserData,
    initProcessData,
    notificationError,
    notificationSuccess,
  };
}
