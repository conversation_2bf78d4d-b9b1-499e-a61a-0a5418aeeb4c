<template>
  <a-modal
    v-model:visible="data.visible"
    :title="t('历史记录')"
    :width="900"
    :scroll="{ y: '400px' }"
    :footer="null"
    @cancel="close"
    @click.stop=""
  >
    <a-table class="p-4" :pagination="false" :dataSource="data.dataSource" :columns="configColumns">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'activityFlag'"
          >{{ record.activityFlag == 1 ? t('当前版本') : '' }}
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <div class="flex">
            <a-button
              size="small"
              type="primary"
              class="mr-2"
              @click="preview(record.xmlContent)"
              >{{ t('预览') }}</a-button
            >
            <a-button size="small" type="primary" @click="update(record.id)">{{
              t('更新到此版本')
            }}</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <Preview v-if="data.previewVisible" :xml="data.xml" @close="data.previewVisible = false" />
  </a-modal>

  <a-modal
    v-model:visible="data.reusltVisible"
    :title="t('变更详情')"
    :width="900"
    :scroll="{ y: '400px' }"
    :footer="null"
    @cancel="close"
    @click.stop=""
  >
    <a-table
      class="p-4"
      :pagination="false"
      :dataSource="data.resultDataSource"
      :columns="reulstColumns"
    />
  </a-modal>
</template>

<script setup lang="ts">
  import { reactive, onMounted } from 'vue';
  import Preview from './Preview.vue';
  import { getHistory, updateHistorySetCurrent } from '/@/api/workflow/design';
  import { ChangeResultModel, HistoryModel } from '/@/api/workflow/model';
  import { message } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      schemaId: string;
    }>(),
    {
      schemaId: '',
    },
  );
  let emits = defineEmits(['close']);
  const configColumns = [
    {
      title: t('序号'),
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 80,
    },
    {
      title: t('状态'),
      dataIndex: 'activityFlag',
      sorter: {
        multiple: 4,
      },
    },
    {
      title: t('版本'),
      dataIndex: 'version',
    },
    {
      title: t('创建人'),
      dataIndex: 'createUserName',
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
    },
    {
      title: t('操作'),
      dataIndex: 'operation',
      width: 120,
      align: 'center',
    },
  ];

  const reulstColumns = [
    {
      title: t('序号'),
      align: 'id',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 80,
    },
    {
      title: t('流程名'),
      dataIndex: 'name',
      width: 200,
    },
    {
      title: t('流水号'),
      dataIndex: 'serailNumber',
    },
    {
      title: t('变更状态'),
      dataIndex: 'status',
      customRender: ({ record }) => {
        if (record.status === 1) {
          return '成功';
        } else {
          return '失败';
        }
      }, // 显示每一行的序号
    },
    {
      title: t('变更详情'),
      dataIndex: 'detail',
    },
  ];
  const data: {
    visible: boolean;
    previewVisible: boolean;
    xml: string;
    dataSource: Array<HistoryModel>;
    reusltVisible: boolean;
    resultDataSource: ChangeResultModel[];
  } = reactive({
    visible: true,
    previewVisible: false,
    xml: '',
    dataSource: [],
    reusltVisible: false,
    resultDataSource: [],
  });

  onMounted(async () => {
    if (props.schemaId) {
      try {
        let res = await getHistory(props.schemaId);
        data.dataSource = res;
      } catch (error) {}
    }
  });

  function close() {
    emits('close');
  }
  function preview(xml: string) {
    if (xml) {
      data.xml = xml;
      data.previewVisible = true;
    }
  }
  async function update(id: string) {
    try {
      let res = await updateHistorySetCurrent(props.schemaId, id);
      if (res) {
        let result = await getHistory(props.schemaId);
        data.reusltVisible = true;
        data.resultDataSource = res;
        data.dataSource = result;
        message.success(t('更新成功！'));
      } else {
        message.error(t('更新失败！'));
      }
    } catch (error) {}
  }
</script>
<style lang="less" scoped>
  .full-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }

    .ant-modal-body {
      flex: 1;
    }
  }
</style>
