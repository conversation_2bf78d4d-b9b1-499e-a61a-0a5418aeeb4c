<template>
  <a-checkbox-group :value="props.modelValue" style="width: 100%" @change="change">
    <a-checkbox v-for="(item, index) in options" :key="index" :value="item.value">{{
      item.label
    }}</a-checkbox>
  </a-checkbox-group>
</template>

<script setup lang="ts">
  import { NoticePolicyType } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const emit = defineEmits(['update:modelValue']);
  const props = defineProps(['modelValue']);
  const options = [
    { label: t('系统消息'), value: NoticePolicyType.SYSTEM_MESSAGES },
    { label: t('短信'), value: NoticePolicyType.SHORT_MESSAGE },
    { label: t('企业微信'), value: NoticePolicyType.ENTERPRISE_WECHAT },
    { label: t('钉钉'), value: NoticePolicyType.DING_TALK },
    { label: t('邮箱'), value: NoticePolicyType.MAILBOX },
  ];
  function change(value) {
    emit('update:modelValue', value);
  }
</script>

<style scoped></style>
