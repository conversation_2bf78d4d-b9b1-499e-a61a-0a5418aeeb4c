<template>
  <div class="list-item">
    <div class="item-box">
      <div class="item-left">
        <div class="icon-box"> <IconFontSymbol :icon="FormImg" fill-color="#5e95ff" /></div
      ></div>
      <div class="item-right">
        <div class="template-box">
          <!-- <span class="item-title">{{ t('编码') }}</span> -->
          <span class="item-form-title">{{ t('功能名称') }}</span>
        </div>
        <div class="template-box">
          <!-- <span class="item-title">{{ t('名称') }}</span> -->
          <a-tooltip v-if="props.item.formName.length > 12" :title="props.item.formName">
            <div class="item-form-name"> {{ `${props.item.formName.slice(0, 9)}...` }}</div>
          </a-tooltip>
          <div class="item-form-name" v-else> {{ props.item.formName }}</div>
        </div>
      </div>
      <div class="fixed-checked">
        <slot name="check"></slot>
      </div>
      <div class="fixed-circle"><PreviewForm :item="props.item" /></div>
      <div class="fixed-icon">
        <IconFontSymbol icon="biaodan2-copy" fill-color="#f8f8f8" />
      </div>
    </div>
  </div>

  <!-- <div class="list-item">
    <div class="item-box">
      <div class="item-left">
        <div class="icon-box"> <IconFontSymbol :icon="FormImg" fill-color="#5e95ff" /></div>
      </div>
      <div class="item-right">
        <div class="item-title">{{ t('功能名称') }}</div>
        <a-tooltip v-if="props.item.formName.length > 12" :title="props.item.formName">
          <div class="item-form-name"> {{ `${props.item.formName.slice(0, 9)}...` }}</div>
        </a-tooltip>
        <div class="item-form-name" v-else> {{ props.item.formName }}</div>
      </div>
      <div class="fixed-checked">
        <slot name="check"></slot>
      </div>
      <div class="fixed-circle"><PreviewForm :item="props.item" /></div>
      <div class="form-icon">
        <IconFontSymbol icon="form" :fill-color="fillColor" />
      </div>
      <div class="fixed-title"> {{ bgTitle }} </div>
    </div>
  </div> -->
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  // import customFormImg from '/@/assets/workflow/custom-form.png';
  // import systemFormImg from '/@/assets/workflow/system-form.png';
  import PreviewForm from '/@bpmn/components/formSettings/PreviewForm.vue';
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  import { FormType } from '/@/enums/workflowEnum';
  import { FormSettingItem } from '/@/model/workflow/formSetting';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  defineEmits(['preview']);

  let props = withDefaults(defineProps<{ item: FormSettingItem }>(), {
    item: () => {
      return { formName: '', key: '', formType: FormType.CUSTOM, formId: '' };
    },
  });

  const FormImg = computed(() => {
    return props.item.formType == FormType.CUSTOM ? 'zidingyimoban-copy' : 'biaodan1-copy';
  });

  // const bgTitle = computed(() => {
  //   return props.item.formType == FormType.CUSTOM ? 'CUSTOM' : 'SYSTEM';
  // });
  // const fontcolor = computed(() => {
  //   return props.item.formType == FormType.CUSTOM ? '@custom-color' : '#18a1f8';
  // });
  // const fillColor = computed(() => {
  //   return props.item.formType == FormType.CUSTOM ? '#eceaff' : '#e8f6ff';
  // });
  // const bgcolor = computed(() => {
  //   return props.item.formType == FormType.CUSTOM ? '#f4f3ff' : '#f4fafe';
  // });
</script>

<style lang="less" scoped>
  @custom-color: #5e95ff;
  @bg-color: #ffffff;

  .list-item {
    width: 261px;
    height: 100px;
    background: @bg-color;
    border-color: transparent;
    border: 1px solid #f4f4f4;
    border-radius: 8px;
    margin-left: 20px;
    margin-bottom: 20px;
    overflow: hidden;

    &:hover {
      border: 1px solid @custom-color;
    }

    .item-box {
      display: flex;
      margin: 14px;
      position: relative;

      .item-left {
        margin-right: 20px;

        .icon-box {
          width: 53px;
          height: 53px;
          font-size: 32px;
          border: 1px solid @custom-color;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .fixed-checked {
        position: absolute;
        bottom: -20px;
        z-index: 1;
        right: -6px;
      }

      .fixed-icon {
        position: absolute;
        right: -24px;
        font-size: 70px;
        transform: rotate(-30deg);
        top: -24px;
      }
    }
  }

  .template-box {
    display: flex;
    margin: 4px 0;
    width: 100%;
  }

  .img-box {
    width: 100%;
    height: 100%;
  }

  .fixed-circle {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
    border-radius: 8px;
    border: 1px solid @custom-color;
    padding: 2px 10px;
    color: @custom-color;
    font-size: 12px;
    cursor: pointer;
  }

  .fixed-circle:hover {
    background-color: #d8e5ff;
    font-size: 12px;
    color: #5e95ff;
    border-color: transparent;
  }

  :deep(.ant-checkbox-inner) {
    border-radius: 50%;
    border-color: @custom-color;
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    border-radius: 50%;
    background-color: @custom-color;
  }

  :deep(.ant-checkbox-checked::after),
  :deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover),
  :deep(.ant-checkbox-inner),
  :deep(.ant-checkbox:hover),
  :deep(.ant-checkbox-input:focus + .ant-checkbox-inner) {
    border-color: @custom-color;
  }

  .item-form-name {
    font-weight: 500;
    font-size: 14px;
    color: #636e80;
  }

  .item-form-title {
    font-weight: bold;
    font-size: 16px;
    color: #262626;
  }

  .picked {
    border-width: 1px;
    border-style: solid;
    border-color: @custom-color;
  }

  .not-picked {
    border-width: 1px;
    border-style: solid;
    border-color: #f3f3f3;
  }
</style>
