<template>
  <div class="list-box">
    <div class="opr-box">
      <NodeHead :nodeName="t('表单操作列表')" />
      <div class="button-box">
        <a-button type="primary" @click="addItem">{{ t('添加') }}</a-button>
      </div>
    </div>
    <div class="list">
      <div class="row head">
        <span class="common">{{ t('赋值来源') }}</span>
        <span class="common">{{ t('目标表单字段') }}</span>
        <span class="small">{{ t('操作') }}</span>
      </div>
      <div class="body" v-if="formInfo.assignmentConfig.formAssignmentConfigs.length > 0">
        <div
          class="row item"
          v-for="(item, index) in formInfo.assignmentConfig.formAssignmentConfigs"
          :key="index"
        >
          <span class="common">
            <a-tree-select
              v-model:value="item.source"
              autoExpandParent
              treeDefaultExpandAll
              :tree-data="props.processParameter"
              style="width: 100%"
              :field-names="{
                children: 'children',
                label: 'name',
                value: 'id',
              }"
            />
          </span>
          <span class="common">
            <FormTargetItem v-model:target="item.target" :formSettingTree="props.formSettingTree" />
          </span>
          <span @click="deleteItem(index)" class="small">
            <Icon icon="ant-design:delete-outlined" class="delete-icon" />
          </span>
        </div>
      </div>
      <EmptyBox v-else :has-icon="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { NodeHead, EmptyBox } from '/@/components/ModalPanel/index';
  import Icon from '/@/components/Icon/index';
  import FormTargetItem from './FormTargetItem.vue';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import { storeToRefs } from 'pinia';
  import { ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    formSettingTree: Array,
    processParameter: Array,
  });

  const store = useBpmnStore();
  const { infoId } = store;
  const { info } = storeToRefs(store);
  const formInfo = ref();

  formInfo.value = info.value.get(infoId);

  function addItem() {
    formInfo.value.assignmentConfig.formAssignmentConfigs.push({
      source: '',
      target: { key: '', formId: '', formField: '' },
    });
  }
  function deleteItem(index: number) {
    formInfo.value.assignmentConfig.formAssignmentConfigs.splice(index, 1);
  }
</script>

<style lang="less" scoped>
  .list-box {
    .opr-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }

  .list {
    .row {
      height: 40px;
      line-height: 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      span {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .common {
        flex-basis: 40%;
        margin-right: 4px;
      }

      .small {
        flex-basis: 10%;
      }
    }

    .head {
      background-color: #f9f9f9;
    }

    .item {
      border-bottom: 1px solid #f9f9f9;
    }

    .delete-icon {
      color: @clear-color;
    }
  }
</style>
