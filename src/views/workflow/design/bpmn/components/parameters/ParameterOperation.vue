<template>
  <a-tabs>
    <a-tab-pane key="1" :tab="t('表单赋值')">
      <FormAssignmentConfig :formSettingTree="targetTree" :processParameter="processParameter" />
    </a-tab-pane>
    <a-tab-pane key="2" :tab="t('参数赋值')">
      <ParamAssignmentConfig
        :needFromData="true"
        :processParameter="processParameter"
        :formSettingTree="sourceTree"
      />
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { getFormSettingTree, getProcessParamConfigs } from '/@bpmn/config/info';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import FormAssignmentConfig from './FormAssignmentConfig.vue';
  import ParamAssignmentConfig from './ParamAssignmentConfig.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { cloneDeep } from 'lodash-es';
  const { t } = useI18n();
  const bpmnStore = useBpmnStore();
  const targetTree = computed(() => {
    const { info, infoId } = bpmnStore;
    if (infoId && info.has(infoId)) {
      let value = info.get(infoId);
      if (value?.formConfigs) {
        let formConfigsData = cloneDeep(value?.formConfigs);
        for (let index = 0; index < formConfigsData.length; index++) {
          const element = formConfigsData[index];
          if (element.children && element.children.length > 0) {
            for (let index = 0; index < element.children.length; index++) {
              const element2 = element.children[index];
              if (element2.type == 'iframe') {
                if (
                  element2.options &&
                  element2.options.list &&
                  Array.isArray(element2.options.list)
                ) {
                  for (let index = 0; index < element2.options.list.length; index++) {
                    const element3 = element2.options.list[index];
                    element.children.push({
                      fieldId: element2.key + '---iframe---' + element3.name,
                      type: 'iframe-name',
                      fieldName: element2.fieldName + '-' + element3.name,
                      required: false,
                      view: false,
                      edit: false,
                      disabled: false,
                      tableName: '',
                      key: '',
                      isSubTable: false,
                      showChildren: false,
                      children: [],
                    });
                  }
                }
              }
            }
          }
        }
        return getFormSettingTree(formConfigsData);
      } else {
        return [];
      }
    }
    return [];
  });
  const sourceTree = computed(() => {
    const { info, infoId } = bpmnStore;
    if (infoId && info.has(infoId)) {
      let value = info.get(infoId);
      if (value?.formConfigs) {
        return getFormSettingTree(value.formConfigs, infoId, value.name, true);
      } else {
        return [];
      }
    }
    return [];
  });
  const processParameter = computed(() => {
    return [
      {
        name: t('流程参数'),
        id: 'processParameter',
        disabled: true,
        children: getProcessParamConfigs(),
      },
    ];
  });
</script>

<style scoped></style>
