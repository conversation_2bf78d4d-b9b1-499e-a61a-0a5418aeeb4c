<template>
  <a-tree-select
    :value="props.target.key"
    autoExpandParent
    treeDefaultExpandAll
    :tree-data="props.formSettingTree"
    style="width: 100%"
    :field-names="{
      children: 'children',
      label: 'title',
      value: 'key',
    }"
    @select="changeTarget"
  />
</template>

<script setup lang="ts">
  const emit = defineEmits(['update:target']);
  const props = defineProps({
    target: {
      type: Object, //类型
      default: null, //默认值
    },
    formSettingTree: {
      type: Array, //类型
      default: null, //默认值
    },
  });

  function changeTarget(value, node) {
    let target = props.target;
    target.formId = node.formId;
    target.key = value;
    target.formField = node.formField;
    emit('update:target', target);
  }
</script>

<style scoped></style>
