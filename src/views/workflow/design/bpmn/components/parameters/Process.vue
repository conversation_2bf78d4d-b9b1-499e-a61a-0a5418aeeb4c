<template>
  <div class="list-box">
    <div class="opr-box">
      <NodeHead :nodeName="t('流程参数')" />
      <div class="button-box">
        <a-button type="primary" @click="add">{{ t('添加参数') }}</a-button>
      </div>
    </div>
    <a-table
      :dataSource="processInfo.processParamConfigs"
      :columns="configColumns"
      :pagination="false"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'name'">
          <a-input v-model:value="record.name" :placeholder="t('参数名称')" />
        </template>
        <template v-if="column.key === 'type'">
          <a-select
            v-model:value="record.type"
            :placeholder="t('请选择类型')"
            style="width: 80px"
            @change="
              () => {
                record.value = null;
              }
            "
          >
            <a-select-option v-for="item in typeOptions" :key="item.id" :value="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </template>
        <template v-if="column.key === 'value'">
          <a-input
            v-if="record.type == OperationType.VALUE"
            v-model:value="record.value"
            :placeholder="t('请填写参数值')"
          />
          <a-tree-select
            v-if="record.type == OperationType.VARIABLE"
            v-model:value="record.value"
            autoExpandParent
            treeDefaultExpandAll
            :tree-data="variableTree"
            style="width: 100%"
            :placeholder="t('请选择参数值')"
            :field-names="{
              children: 'children',
              label: 'title',
              value: 'key',
            }"
          />
          <ScriptApiSelect
            style="width: 100%"
            :isGlobalParams="true"
            v-if="record.type == OperationType.API"
            v-model="record.apiConfig"
            :need-hide-components="true"
          />
        </template>
        <template v-if="column.key === 'operation'">
          <a-popconfirm @confirm="deleteItem(index)">
            <template #title>
              <p>{{ t('删除参数') }}</p>
              <p>{{ t('删除流程参数会清空已引用该参数的所有配置，请确认是否继续？') }}</p>
              <p class="pop-desc">{{
                t('如果引用该流程参数的配置较多，清空时间会相应变长，请耐心等待。')
              }}</p>
            </template>
            <Icon icon="ant-design:delete-outlined" class="delete-icon" />
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
  import { NodeHead } from '/@/components/ModalPanel/index';
  import Icon from '/@/components/Icon/index';
  //import ScriptApiSelect from '/@bpmn/components/arguments/ApiSelect.vue';  //之前是调的这个，现在换成ScriptApiSelect.vue
  import ScriptApiSelect from '/@bpmn/components/arguments/ScriptApiSelect.vue';
  import { ProcessArgumentTreeData } from '/@bpmn/config/rules';
  import { OperationType } from '/@/enums/workflowEnum';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import { storeToRefs } from 'pinia';
  import { randomNum } from '/@bpmn/util/random';
  import { updateProcessParameterRelevance } from '/@bpmn/config/useUpdateAllFormInfo';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const configColumns = [
    {
      title: t('参数名称'),
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: t('类型'),
      dataIndex: 'type',
      key: 'type',
      width: 80,
    },
    {
      title: t('参数值'),
      dataIndex: 'value',
      key: 'value',
    },
    {
      title: t('操作'),
      dataIndex: 'operation',
      key: 'operation',
      width: 60,
    },
  ];
  const store = useBpmnStore();
  const { processInfo } = storeToRefs(store);
  let typeOptions = [
    {
      id: OperationType.VALUE,
      name: t('值'),
    },
    {
      id: OperationType.VARIABLE,
      name: t('变量'),
    },
    {
      id: OperationType.API,
      name: 'API',
    },
  ];
  let variableTree = ProcessArgumentTreeData;
  // 新增
  function add() {
    processInfo.value.processParamConfigs.push({
      id: randomNum(),
      name: '',
      type: OperationType.VALUE,
      value: '',
      apiConfig: {
        id: '',
        name: '',
        method: '',
        requestParamsConfigs: [], //Query Params
        requestHeaderConfigs: [], //Header
        requestBodyConfigs: [], //Body
      },
    });
  }
  //删除
  function deleteItem(index: number) {
    updateProcessParameterRelevance(processInfo.value.processParamConfigs[index].id);
    processInfo.value.processParamConfigs.splice(index, 1);
  }
</script>

<style lang="less" scoped>
  .list-box {
    .opr-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }

  .delete-icon {
    color: @clear-color;
  }

  .pop-desc {
    font-size: 12px;
    color: rgb(0 0 0 / 40%);
  }
</style>
