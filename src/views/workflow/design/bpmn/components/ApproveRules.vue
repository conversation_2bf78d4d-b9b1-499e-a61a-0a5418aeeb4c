<template>
  <FormItem :label="t('自动同意：')">
    <a-tree-select
      :value="props.autoAgreeRule"
      style="width: 100%"
      :tree-data="autoAgreeRuleOptions"
      tree-checkable
      allow-clear
      :placeholder="t('请选择自动同意规则')"
      @change="changeAutoAgreeRule"
    />
  </FormItem>
  <FormItem :label="t('无处理人：')">
    <a-select
      :value="props.noHandler"
      style="width: 100%"
      :options="noHandlerOptions"
      @change="changeNoHandler"
    />
  </FormItem>
  <FormItem :label="t('指定审批人：')">
    <a-select
      :value="props.isPrevChooseNext"
      style="width: 100%"
      :options="designatedApproverOptions"
      @change="changeDesignatedApprover"
    />
  </FormItem>
  <FormItem
    :tip="
      t(
        '临时审批人是指由上一节点审批人指定下一节点审批人过程中，是否允许在审批人基础上添加组织架构人员。',
      )
    "
    :label="t('临时审批人：')"
    v-if="props.provisionalApprover == true || props.provisionalApprover == false"
  >
    <a-switch :checked="props.provisionalApprover" @change="changeProvisionalApprover" />
  </FormItem>
</template>

<script setup lang="ts" name="ProcessBasic">
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import { AutoAgreeRule, DesignatedApprover, NoHandler } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const emits = defineEmits([
    'update:autoAgreeRule',
    'update:noHandler',
    'update:isPrevChooseNext',
    'update:provisionalApprover',
  ]);
  const props = defineProps({
    autoAgreeRule: Array,
    noHandler: Number || String || Boolean,
    isPrevChooseNext: Number || String || Boolean,
    provisionalApprover: {
      type: Boolean || undefined,
      default: undefined,
    },
  });

  // 自动同意规则
  const autoAgreeRuleOptions = [
    {
      value: AutoAgreeRule.ORIGINATOR,
      label: t('候选审批人包含流程任务发起人'),
    },
    {
      value: AutoAgreeRule.PREVIOUS_NODE,
      label: t('候选审批人包含上一节点审批人'),
    },
    {
      value: AutoAgreeRule.APPROVED,
      label: t('候选审批人在此流程中审批过'),
    },
  ];
  function changeAutoAgreeRule(val: Array<AutoAgreeRule>) {
    let autoAgreeRule = val;
    if (val.length > 0) {
      // 选择了自动同意规则后，无处理人 只能由 超级管理员处理 且 指定审批人 只能 不指定审批人
      emits('update:noHandler', NoHandler.ADMIN);
      emits('update:isPrevChooseNext', DesignatedApprover.NOT_SPECIFIED);
    }
    emits('update:autoAgreeRule', autoAgreeRule);
  }
  // 无处理人
  const noHandlerOptions = [
    {
      value: NoHandler.ADMIN,
      label: t('由超级管理员处理'),
    },
    {
      value: NoHandler.PREVIOUS_NODE,
      label: t('由上一节点审批人指定审批人'),
    },
  ];
  function changeNoHandler(val: NoHandler) {
    emits('update:noHandler', val);
    if (val == NoHandler.PREVIOUS_NODE) {
      // 无处理人 选择了 由上一节点审批人指定审批人；那么自动同意规则必须为空
      emits('update:autoAgreeRule', []);
    }
  }
  // 指定审批人
  const designatedApproverOptions = [
    {
      value: DesignatedApprover.NOT_SPECIFIED,
      label: t('不指定审批人'),
    },
    {
      value: DesignatedApprover.PREVIOUS_NODE,
      label: t('由上一节点审批人指定'),
    },
  ];
  function changeDesignatedApprover(val: DesignatedApprover) {
    emits('update:isPrevChooseNext', val);
    if (val == DesignatedApprover.PREVIOUS_NODE) {
      // 指定审批人 选择了 由上一节点审批人指定；那么自动同意规则必须为空
      emits('update:autoAgreeRule', []);
    }
  }
  // 临时审批人
  function changeProvisionalApprover(val: Boolean) {
    emits('update:provisionalApprover', val);
  }
</script>

<style lang="less" scoped></style>
