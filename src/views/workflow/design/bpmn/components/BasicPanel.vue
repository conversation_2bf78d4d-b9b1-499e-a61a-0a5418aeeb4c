<template>
  <div v-if="showPanel">
    <NodeHead class="mb-3" :nodeName="nodeName" />
    <a-tabs>
      <a-tab-pane key="1" :tab="t('基础配置')">
        <FormItem :required="formInfo.type !== 'bpmn:SequenceFlow'" :label="t('节点名称：')">
          <a-input
            v-model:value="formInfo.name"
            :placeholder="t('请填写节点名称')"
            @change="updateElementName"
          />
        </FormItem>
        <slot name="basic"></slot>
        <FormItem :label="t('节点备注：')">
          <a-textarea v-model:value="formInfo.remark" :rows="8" />
        </FormItem>
        <slot name="noticePolicy"></slot>
      </a-tab-pane>
      <slot></slot>

      <a-tab-pane key="99" :tab="t('事件监听')">
        <a-tabs>
          <a-tab-pane v-for="(item, index) in event" :key="index" :tab="t(item)">
            <EventConfig :eventIndex="index" :event="item" v-model:formInfo="formInfo" />
          </a-tab-pane>
        </a-tabs>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import useStateFormInfo from '/@bpmn/hooks/useStateFormInfo';
  import { NodeHead } from '/@/components/ModalPanel/index';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import { inject, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import EventConfig from '/@bpmn/components/arguments/EventConfig.vue';
  const { t } = useI18n();
  const { showPanel, formInfo, nodeName } = useStateFormInfo();
  const updateElementName = inject('updateElementName') as any;
  const event = computed(() => {
    return formInfo.value.type === 'bpmn:UserTask'
      ? ['开始事件', '结束事件', '同意事件', '驳回事件', '撤回事件', '超时事件']
      : ['开始事件', '结束事件'];
  });
  // const showEventTab = computed(() => {
  //   let val = false;
  //   if (formInfo.value && formInfo.value.type === 'bpmn:SequenceFlow') {
  //     if (formInfo.value.sourceConfig && formInfo.value.sourceConfig.type) {
  //       val = false;
  //       if (formInfo.value.sourceConfig.type.includes('Gateway')) {
  //         val = true;
  //       }
  //     }
  //   } else {
  //     val = true;
  //   }

  //   return val;
  // });
</script>

<style lang="less" scoped>
  .node-box {
    position: absolute;
    top: 0;
    right: 0;
    box-shadow: -7px -1px 7px #dadcde;
    padding: 20px 30px 20px 20px;
    height: 100%;
    width: 410px;

    .node-title {
      line-height: 20px;
      margin-bottom: 10px;
      padding-left: 6px;
      border-left: 6px solid #5e95ff;
    }

    :deep(.ant-form-item) {
      margin-bottom: 10px;
    }

    :deep(.ant-select) {
      width: 100%;
    }
  }

  :deep(.ant-table-cell) {
    padding: 10px !important;
  }
</style>
