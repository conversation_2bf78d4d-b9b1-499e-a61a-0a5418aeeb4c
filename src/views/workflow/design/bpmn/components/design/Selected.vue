<template>
  <div class="title">
    <NodeHead :node-name="t('流程模板列表')" />
    <a-button class="selected-btn" @click="open">{{ t('已选流程模板列表') }}</a-button>
  </div>
  <a-drawer
    v-if="data.visible"
    placement="right"
    :visible="data.visible"
    :get-container="false"
    :closable="false"
    :mask="false"
    :zIndex="1000"
  >
    <div class="selected-head title">
      <NodeHead :node-name="t('已选流程模板列表')" />
      <div class="close-icon" @click="close">+</div>
    </div>
    <SearchBox @search="search" />
    <div class="list-box" v-if="data.list && data.list.length > 0">
      <TemplateCard
        class="picked"
        v-for="(item, index) in data.list"
        :key="index"
        :item="item"
        @click="listChecked(item)"
      >
        <template #pick> <a-checkbox size="small" :checked="true" /> </template
      ></TemplateCard>
    </div>
    <EmptyBox v-else />
  </a-drawer>
</template>

<script setup lang="ts">
  import { reactive, watch } from 'vue';
  import { NodeHead, EmptyBox, SearchBox } from '/@/components/ModalPanel/index';
  import TemplateCard from '/@bpmn/components/card/TemplateCard.vue';
  import { cloneDeep } from 'lodash-es';
  import { RelationProcessConfig } from '/@/model/workflow/workflowConfig';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let emits = defineEmits(['change']);
  const props = withDefaults(
    defineProps<{
      list: Array<RelationProcessConfig>;
    }>(),
    {
      list: () => {
        return [];
      },
    },
  );

  let data: {
    visible: Boolean;
    list: Array<RelationProcessConfig>;
    keyword: string;
  } = reactive({
    visible: false,
    list: [],
    keyword: '',
  });

  watch(props, (val) => {
    data.list = cloneDeep(val.list);
  });

  function open() {
    data.list = cloneDeep(props.list);
    data.visible = true;
  }
  function close() {
    data.visible = false;
  }
  function search(keyword = '') {
    data.keyword = keyword;
    data.list = cloneDeep(
      props.list.filter((ele) => {
        return ele.name.includes(data.keyword);
      }),
    );
  }
  function listChecked(item) {
    emits('change', item);
  }
</script>

<style scoped>
  .title {
    display: flex;
    justify-content: space-between;
    height: 40px;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-drawer-content-wrapper) {
    width: 100% !important;
    box-shadow: 0 2px 2px 2px rgb(0 0 0 / 4%);
  }

  :deep(.ant-drawer-open) {
    position: absolute;
    width: calc(100% - 244px) !important;
    top: 50px;
    left: 240px;
    box-shadow: -5px 5px 4px 1px rgb(0 0 0 / 6%);
    height: calc(100% - 110px);
    z-index: 2;
  }

  .list-box {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    padding: 10px 0;
  }

  .selected-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .selected-btn {
    position: absolute;
    right: 30px;
  }

  .close-icon {
    font-size: 24px;
    transform: rotate(45deg);
  }

  .picked {
    border-width: 1px;
    border-style: solid;
    border-color: #5e95ff;
  }
</style>
