<template>
  <div @click.stop="open" :style="buttonStyle">{{ t(buttonTit || '表单预览') }}</div>
  <a-modal v-model:visible="visible" :title="t('预览')" :width="1100" :footer="null">
    <PreviewSystemForm
      v-if="visible && props.item.formType == FormType.SYSTEM"
      :systemComponent="getFormProps.systemComponent"
    />
    <SimpleForm
      v-else
      ref="formRef"
      class="form"
      :formProps="getFormProps"
      :formModel="formData"
      :isWorkFlow="true"
    />
  </a-modal>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import { getSchemasList } from '/@bpmn/config/formPermission';
  import { FormSchema } from '/@/components/Form';
  import SimpleForm from '/@/components/SimpleForm/src/SimpleForm.vue';
  import { FormSettingItem } from '/@/model/workflow/formSetting';
  import { FormType } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { PreviewSystemForm } from '/@/components/SystemForm/index';
  import { SystemComponentConfig } from '/@/model/workflow/bpmnConfig';
  const { t } = useI18n();
  let props = withDefaults(
    defineProps<{ item: FormSettingItem; buttonStyle?: any; buttonTit?: string }>(),
    {
      item: () => {
        return { formType: FormType.CUSTOM, formId: '', key: '', formName: '' };
      },
    },
  );

  const visible = ref(false);
  let getFormProps: {
    schemas: FormSchema[];
    showResetButton: boolean;
    showSubmitButton: boolean;
    hiddenComponent: any;
    systemComponent: SystemComponentConfig;
  } = reactive({
    schemas: [],
    showResetButton: false,
    showSubmitButton: false,
    hiddenComponent: [],
    systemComponent: {
      functionalModule: '',
      functionName: '',
      functionFormName: '',
    },
  });
  const formData = reactive<Recordable>({});
  async function open() {
    if (props.item.formType == FormType.CUSTOM) {
      getFormProps.schemas = await getSchemasList(props.item.formId, props.item.formType);
    } else {
      let systemJson = await getSchemasList(props.item.formId, props.item.formType);
      getFormProps.systemComponent = systemJson.systemComponent;
    }
    visible.value = true;
  }
</script>

<style lang="less" scoped>
  .form {
    min-height: 400px;
    padding: 20px;
  }
</style>
