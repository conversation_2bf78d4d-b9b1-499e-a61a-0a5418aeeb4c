<template>
  <SettingModal
    :list="[
      {
        formId: config.formId,
        formName: config.formName,
        formType: config.formType,
        key: config.key,
      },
    ]"
    :isSingle="true"
    @submit="submit"
  >
    <InputModel :value="config.formName" :placeholder="t('请选择功能表单')" />
  </SettingModal>
</template>

<script setup lang="ts">
  import SettingModal from '/@bpmn/components/formSettings/SettingModal.vue';
  import InputModel from '/@bpmn/components/InputModel.vue';
  import { FormType } from '/@/enums/workflowEnum';
  import { FormSettingItem } from '/@/model/workflow/formSetting';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let emits = defineEmits(['update:config']);
  withDefaults(defineProps<{ config: FormSettingItem }>(), {
    config: () => {
      return { formType: FormType.CUSTOM, formId: '', formName: '', key: '' };
    },
  });
  // FormSettingItem
  function submit(list: Array<FormSettingItem>) {
    let submitJson = {
      enabled: true,
      formId: '',
      formName: '',
      formType: FormType.CUSTOM,
    };
    if (list.length > 0) {
      submitJson.formId = list[0].formId;
      submitJson.formName = list[0].formName;
      submitJson.formType = list[0].formType;
    }
    emits('update:config', submitJson);
  }
</script>

<style lang="less" scoped>
  :deep(.slot-item) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .open-icon {
    text-align: center;
    height: 30px;
  }
</style>
