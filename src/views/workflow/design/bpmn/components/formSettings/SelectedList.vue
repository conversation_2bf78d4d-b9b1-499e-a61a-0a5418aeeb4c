<template>
  <div class="title">
    <NodeHead :node-name="t('表单列表')" />
    <a-button class="selected-btn" @click="open">{{ t('已选表单') }}</a-button>
  </div>
  <a-drawer
    v-if="visible"
    placement="right"
    :visible="true"
    :get-container="false"
    :closable="false"
    :mask="false"
    :zIndex="1000"
  >
    <div class="selected-head title">
      <NodeHead :node-name="t('已选表单')" />
      <div class="close-icon" @click="close">+</div>
    </div>
    <div class="list-box" v-if="props.list.length > 0">
      <FormCard
        v-for="(item, index) in props.list"
        :key="index"
        :item="item"
        class="picked"
        @click="abolish(item)"
      >
        <template #check>
          <a-checkbox size="small" :checked="true" />
        </template>
      </FormCard>
    </div>
    <EmptyBox v-else />
  </a-drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { NodeHead, EmptyBox } from '/@/components/ModalPanel/index';
  import { FormSettingItem } from '/@/model/workflow/formSetting';
  import FormCard from '/@bpmn/components/card/FormCard.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let emits = defineEmits(['abolish']);
  let props = withDefaults(defineProps<{ type: string; list: Array<FormSettingItem> }>(), {
    type: '',
    list: () => {
      return [];
    },
  });

  let visible = ref(false);

  function open() {
    visible.value = true;
  }
  function close() {
    visible.value = false;
  }
  function abolish(item) {
    emits('abolish', item);
  }
</script>

<style scoped>
  .title {
    display: flex;
    justify-content: space-between;
    height: 40px;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-drawer-content-wrapper) {
    width: 100% !important;
    box-shadow: 0 2px 2px 2px rgb(0 0 0 / 4%);
  }

  :deep(.ant-drawer-open) {
    position: absolute;
    width: calc(100% - 244px) !important;
    top: 50px;
    left: 240px;
    box-shadow: -5px 5px 4px 1px rgb(0 0 0 / 6%);
    height: calc(100% - 110px);
    z-index: 2;
  }

  .list-box {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    padding: 10px 0;
  }

  .selected-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .selected-btn {
    position: absolute;
    right: 30px;
  }

  .close-icon {
    font-size: 24px;
    transform: rotate(45deg);
  }

  .picked {
    border-width: 1px;
    border-style: dotted;
  }
</style>
