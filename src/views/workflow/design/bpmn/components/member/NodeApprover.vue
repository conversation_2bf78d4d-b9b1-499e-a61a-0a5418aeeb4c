<template>
  <div>
    <div @click="show"><slot></slot></div>
    <ModalPanel
      :visible="visible"
      :width="400"
      :title="t('指定节点审批人')"
      @submit="submit"
      @close="close"
    >
      <div class="title">
        <NodeHead :node-name="t('节点列表')" />
      </div>
      <div class="list-box">
        <a-checkbox
          v-for="(item, index) in nodes.list"
          :key="index"
          :value="item.id"
          size="small"
          v-model:checked="item.checked"
          >{{ item.name }}</a-checkbox
        >
      </div>
    </ModalPanel>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import { MemberType } from '/@/enums/workflowEnum';
  import { MemberConfig, NodesConfig } from '/@/model/workflow/memberSetting';
  import { NodeHead, ModalPanel } from '/@/components/ModalPanel/index';
  import { InfoId } from '/@/model/workflow/bpmnConfig';
  import { getNodeList } from '/@bpmn/config/info';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const emits = defineEmits(['change']);
  const props = withDefaults(
    defineProps<{
      memberList: Array<MemberConfig>;
    }>(),
    {
      memberList: () => {
        return [];
      },
    },
  );
  const visible = ref(false);
  const memberType = MemberType.SPECIFY_NODE_APPROVER;
  let nodes: { list: Array<NodesConfig> } = reactive({
    list: [],
  });
  function show() {
    nodes.list = [];
    let selectIds: Array<InfoId> = [];
    if (props.memberList.length > 0) {
      selectIds = props.memberList
        .filter((ele: MemberConfig) => {
          if (ele.memberType && ele.memberType === memberType) return ele;
        })
        .map((ele: MemberConfig) => {
          return ele.id;
        });
    }
    getNodeList().map((item) => {
      nodes.list.push({ id: item.id, name: item.name, checked: selectIds.includes(item.id) });
    });
    visible.value = true;
  }
  function submit() {
    let list: Array<MemberConfig> = [];
    if (props.memberList.length > 0) {
      list = props.memberList.filter((ele: MemberConfig) => {
        if (ele.memberType != memberType) return ele;
      });
    }
    nodes.list.forEach((item: NodesConfig) => {
      if (item.checked) {
        list.push({
          memberType: memberType,
          name: item.name,
          id: item.id,
        });
      }
    });
    emits('change', [...list]);
    close();
  }
  function close() {
    visible.value = false;
  }
</script>

<style scoped>
  .title {
    display: flex;
    justify-content: space-between;
    height: 40px;
    font-size: 16px;
    color: #333333;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 10px;
  }

  .list-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    margin: 0 20px;
  }

  :deep(.ant-checkbox-wrapper) {
    margin-left: 8px;
    margin-bottom: 10px;
  }

  :deep(.ant-checkbox-inner) {
    border-radius: unset;
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    border-radius: unset;
  }
</style>
