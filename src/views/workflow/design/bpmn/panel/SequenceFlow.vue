<template>
  <BasicPanel>
    <a-tab-pane key="2" :tab="t('流转条件')" v-if="sourceIsGateway">
      <FormItem required :label="t('变量数据：')">
        <a-tree-select
          v-model:value="sequenceFlow.dataOfVariables"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="Please select"
          allow-clear
          tree-default-expand-all
          :tree-data="sequenceFlow.dataOfVariablesTree"
          :field-names="{
            children: 'children',
            label: 'label',
            value: 'key',
          }"
          @select="selectDataOfVariables"
        />
      </FormItem>
      <FormItem required :label="t('运算符：')">
        <a-select
          v-model:value="sequenceFlow.operator"
          style="width: 100%"
          @change="changeOperator"
        >
          <a-select-option
            v-for="item in operators"
            :title="item.label"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </FormItem>

      <FormItem required :label="t('运算值：')">
        <a-input v-model:value="sequenceFlow.value" style="width: 100%" @blur="changeValue" />
      </FormItem>
      <!-- <FormItem required :label="t('特殊值：')">
        <a-select
          v-model:value="sequenceFlow.special"
          style="width: 100%"
          @change="changeSpecialOperation"
        >
          <a-select-option
            v-for="item in specialValues"
            :title="item.label"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </FormItem> -->
      <FormItem required :label="t('按钮值：')">
        <a-select
          v-model:value="sequenceFlow.button"
          style="width: 100%"
          @change="changeButtonOfOperation"
        >
          <a-select-option
            v-for="item in sequenceFlow.buttons"
            :title="item.title"
            :key="item.key"
            :value="item.key"
          >
            {{ item.title }}
          </a-select-option>
        </a-select>
      </FormItem>
      <FormItem required :label="t('会签结果值：')">
        <a-select
          v-model:value="sequenceFlow.multipleInstances"
          style="width: 100%"
          @change="changeMultipleInstances"
        >
          <a-select-option
            v-for="item in multipleInstances"
            :title="item.label"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </FormItem>
      <FormItem required :label="t('与或非：')">
        <a-select
          v-model:value="sequenceFlow.andOrNot"
          style="width: 100%"
          @change="changeAndOrNot"
        >
          <a-select-option
            v-for="item in andOrNotList"
            :title="item.label"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </FormItem>

      <FormItem required :label="t('条件公式：')" :key="sequenceFlow.rendKey">
        <div class="rule-tip-box tag-box" v-if="formInfo.conditionConfigs.length > 0">
          <draggable v-model="formInfo.conditionConfigs" group="people" @end="end" item-key="key">
            <template #item="{ element, index }">
              <a-tag closable @close="handleClose(index)">{{ element.title }}</a-tag>
            </template>
          </draggable>
        </div>
        <div v-else class="rule-tip-box">
          <EmptyBox />
        </div>
      </FormItem>
    </a-tab-pane>
  </BasicPanel>
</template>

<script setup lang="ts">
  import { inject, computed, onMounted, reactive } from 'vue';
  import Draggable from 'vuedraggable';
  import useStateFormInfo from '/@bpmn/hooks/useStateFormInfo';
  import BasicPanel from '/@bpmn/components/BasicPanel.vue';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import { EmptyBox } from '/@/components/ModalPanel/index';
  import { TreeProps } from 'ant-design-vue';
  import {
    operators,
    buttons,
    andOrNotList,
    getDataOfVariablesTree,
    multipleInstances,
  } from '/@bpmn/config/sequenceConfig';
  import { getProcessParamConfigs, separator } from '/@bpmn/config/info';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { FormFiledConfig } from '/@/model/workflow/memberSetting';
  import { stringIsNumber } from '/@/utils/is';
  const { t } = useI18n();
  const { formInfo } = useStateFormInfo();
  const updateConditionExpression = inject('updateConditionExpression') as any;
  let sequenceFlow: {
    rendKey: number;
    dataOfVariablesTree: TreeProps['treeData'];
    dataOfVariables: string;
    operator: string;
    value: string;
    button: string;
    andOrNot: string;
    special: string;
    multipleInstances: string;
    buttons: Array<{
      key: string;
      title: string;
    }>;
  } = reactive({
    rendKey: 0,
    dataOfVariablesTree: [],
    dataOfVariables: '',
    operator: '',
    value: '',
    button: '',
    andOrNot: '',
    special: '',
    multipleInstances: '',
    buttons: [],
  });
  // 是否有流程线源头线为网关节点
  const sourceIsGateway = computed(() => {
    let val = false;
    if (
      formInfo.value &&
      formInfo.value.sourceConfig &&
      formInfo.value.sourceConfig.type &&
      formInfo.value.sourceConfig.type.includes('Gateway')
    ) {
      val = true;
    }
    return val;
  });
  onMounted(() => {
    sequenceFlow.dataOfVariablesTree = getDataOfVariablesTree();

    let params = getProcessParamConfigs();
    let paramtree: FormFiledConfig[] = [];
    params.forEach((o) => {
      paramtree.push({
        key: separator + 'params' + o.id,
        title: o.name,
        disabled: false,
        label: o.name,
        children: [],
      });
    });
    sequenceFlow.dataOfVariablesTree.unshift({
      children: paramtree,
      disabled: true,
      key: 'paramsData',
      label: t('流程参数'),
    });
    sequenceFlow.buttons = buttons;
  });
  function end(e) {
    let oldJson = formInfo.value.conditionConfigs[e.oldDraggableIndex];
    let newJson = formInfo.value.conditionConfigs[e.newDraggableIndex];
    formInfo.value.conditionConfigs[e.oldDraggableIndex] = newJson;
    formInfo.value.conditionConfigs[e.newDraggableIndex] = oldJson;
  }
  function handleClose(index: number) {
    formInfo.value.conditionConfigs.splice(index, 1);
    sequenceFlow.rendKey++;
    changeConditionExpression();
  }
  //变量数据
  function selectDataOfVariables(_value, option) {
    console.log('selectDataOfVariables', _value, option);
    if (option.key.includes(separator + 'params')) {
      formInfo.value.conditionConfigs.push({
        title: option.label,
        value: 'processParamKey["' + option.key.replace(separator + 'params', '') + '"]',
      });

      changeConditionExpression();
    } else if (option.key.includes(separator + 'button')) {
      // 按钮组
      formInfo.value.conditionConfigs.push({
        title: option.label,
        value: option.key,
      });
      sequenceFlow.buttons = option.buttons;
      changeConditionExpression();
    } else {
      let arr = option.key.split(separator);
      if (arr.length == 3) {
        // let nodeId = arr[0];
        let formId = arr[1];
        let fieldId = arr[2];
        formInfo.value.conditionConfigs.push({
          title: option.label,
          value: formId + '["' + fieldId + '"]',
        });
        changeConditionExpression();
      }
    }

    sequenceFlow.dataOfVariables = '';
  }
  // 运算符
  function changeOperator(_value, option) {
    formInfo.value.conditionConfigs.push({ title: option.title, value: option.value });
    sequenceFlow.operator = '';
    changeConditionExpression();
  }
  // 运算值
  function changeValue(_value) {
    // console.log(stringIsNumber(sequenceFlow.value), _value);
    if (stringIsNumber(sequenceFlow.value)) {
      formInfo.value.conditionConfigs.push({
        title: sequenceFlow.value,
        value: sequenceFlow.value,
      });
    } else {
      formInfo.value.conditionConfigs.push({
        title: sequenceFlow.value,
        value: "'" + sequenceFlow.value + "'",
      });
    }

    sequenceFlow.value = '';
    changeConditionExpression();
  }
  // 按钮值
  function changeButtonOfOperation(_value, option) {
    formInfo.value.conditionConfigs.push({ title: option.title, value: "'" + option.key + "'" });
    sequenceFlow.button = '';
    changeConditionExpression();
  }
  // 与或非
  function changeAndOrNot(_value, option) {
    formInfo.value.conditionConfigs.push({ title: option.title, value: option.value });
    sequenceFlow.andOrNot = '';
    changeConditionExpression();
  }
  // 更新xml
  function changeConditionExpression() {
    let expression = formInfo.value.conditionConfigs.map((ele) => ele.value).join(' ');
    console.log(expression, 'changeConditionExpression', formInfo.value.conditionConfigs);
    updateConditionExpression(expression);
  }
  // 特殊值
  // function changeSpecialOperation(_value, option) {
  //   formInfo.value.conditionConfigs.push({ title: option.title, value: option.key });
  //   sequenceFlow.special = '';
  //   changeConditionExpression();
  // }

  //会签结果值
  function changeMultipleInstances(_value, option) {
    formInfo.value.conditionConfigs.push({
      title: t('会签结果') + option.title,
      value: option.key,
    });
    sequenceFlow.multipleInstances = '';
    changeConditionExpression();
  }
</script>

<style lang="less" scoped>
  .rule-tip-box {
    display: flex;
    height: 100%;
    min-height: 200px;
    flex-direction: column;
    border: 1px solid #ddd;

    .tip-title {
      font-size: 48px;
    }

    .tip-content {
      font-size: 14px;
      color: #9e9d9d;
      padding: 10px 40px;
    }
  }

  .tag-box {
    padding: 10px;
  }

  :deep(.ant-tag) {
    margin: 4px;
    padding: 4px;
    border: none;
    background-color: #eef4ff;
    color: #676767;
  }

  :deep(.ant-tag-close-icon) {
    margin-left: 10px;
  }
</style>
