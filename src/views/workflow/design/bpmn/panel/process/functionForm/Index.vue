<template>
  <div>
    <FormItem :label="t('是否启用：')">
      <a-switch v-model:checked="processInfo.formInitConfig.enabled" @change="changeEnabled" />
    </FormItem>

    <div v-show="showConfig">
      <FormItem required :label="t('功能表单：')">
        <PickFunctionForm v-model:config="processInfo.formInitConfig" />
      </FormItem>
      <FormItem :label="t('注意事项：')" />
      <FormItem>
        <div class="remark">
          <p>{{ t('1.功能表单必须是已经发布为菜单功能的表单，并且启用了“表单发起审批”按钮。') }}</p>
          <p>{{
            t('2.“表单发起审批”功能会针对“系统表单”、“自定义表单”的列表页生成“发起审批”按钮。')
          }}</p>
          <p>{{
            t(
              '3.点击“发起审批”按钮会自动使用选择的列表数据填充相应表单，如果流程不存在选择的表单，则不进行任何处理。',
            )
          }}</p>
          <p>{{
            t(
              '4.流程进入审批状态后，隐藏“发起审批”按钮，并使用流程状态覆盖进行显示，状态包括“审批中”、“审批完成”。',
            )
          }}</p>
          <p>{{ t('5.功能表单仅允许关联一个流程模板，流程模板也仅允许关联一个功能表单。') }}</p>
        </div>
      </FormItem>
    </div>
  </div>
</template>

<script setup lang="ts" name="AssociatedFunctions">
  import { storeToRefs } from 'pinia';
  import { computed } from 'vue';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import PickFunctionForm from '/@bpmn/components/formSettings/PickFunctionForm.vue';
  import { FormType } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const store = useBpmnStore();
  const { processInfo } = storeToRefs(store);
  const showConfig = computed(() => {
    return processInfo.value.formInitConfig.enabled;
  });
  function changeEnabled(val: boolean) {
    if (val == false) {
      processInfo.value.formInitConfig = {
        enabled: false,
        formType: FormType.CUSTOM, //表单类型
        formId: '', //表单ID
        formName: '', //表单名称
      };
    }
  }
</script>

<style scoped>
  .remark {
    font-size: 12px;
    margin-left: 30px;
  }
</style>
