<template>
  <div>
    <FormItem required :label="t('模板编号：')">
      <a-input v-model:value="processInfo.code" :placeholder="t('请填写模板编号')" />
    </FormItem>
    <FormItem required :label="t('模板名称：')">
      <a-input v-model:value="processInfo.name" :placeholder="t('请填写模板名称')" />
    </FormItem>
    <FormItem required :label="t('模板分类：')">
      <a-select
        v-model:value="processInfo.category"
        :placeholder="t('请选择模板分类')"
        style="width: 100%"
      >
        <a-select-option v-for="item in categoryOptions" :key="item.id" :value="item.id">
          {{ item.name }}
        </a-select-option>
      </a-select>
    </FormItem>
    <FormItem :label="t('命名规则：')">
      <NameRuleModel
        v-model="processInfo.nameRule"
        v-model:nameRuleConfigs="processInfo.nameRuleConfigs"
      />
    </FormItem>

    <ApproveRules
      v-model:autoAgreeRule="processInfo.autoAgreeRule"
      v-model:noHandler="processInfo.noHandler"
      v-model:isPrevChooseNext="processInfo.isPrevChooseNext"
    />
    <FormItem :tip="t('配置默认表单后，用户任务节点均会包含该表单')" :label="t('默认表单：')">
      <SettingDefaultForm />
    </FormItem>
    <FormItem :tip="abstractTip" :label="t('流程摘要：')">
      <ProcessAbstract
        @success="handleAbstract"
        @info="handleInfo"
        :summaryInfo="processInfo.summaryInfo"
      >
        <InputModel :placeholder="t('请选择需要展示的摘要信息')" :value="summary" />
      </ProcessAbstract>
    </FormItem>
    <!-- <FormItem :label="t('移动端：')">
      <a-switch v-model:checked="processInfo.appShow" />
    </FormItem> -->
    <FormItem :label="t('备注：')">
      <a-textarea v-model:value="processInfo.remark" :rows="8" />
    </FormItem>
  </div>
</template>

<script setup lang="ts" name="ProcessBasic">
  import { storeToRefs } from 'pinia';
  import { ref } from 'vue';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import ApproveRules from '/@bpmn/components/ApproveRules.vue';
  import NameRuleModel from './NameRule.vue';
  import SettingDefaultForm from './SettingDefaultForm.vue';
  import ProcessAbstract from './ProcessAbstract.vue';
  import InputModel from '/@bpmn/components/InputModel.vue';
  import { TreeItem } from '/@/components/Tree';
  import { getDicDetailList } from '/@/api/system/dic';
  import { FlowCategory } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();

  const abstractTip = `
    1.流程摘要是指可以讲流程基本信息以及流程表单信息显示在流程任务列表当中的功能。
    2.如果需要添加表单信息，建议在流程设计完成之后再进行流程摘要配置。
    3.流程摘要默认会自带发起人名称以及发起人部门信息，用户可以根据需要自行调整。`;

  const store = useBpmnStore();
  const { processInfo } = storeToRefs(store);
  const summary = ref('');
  // 模板分类
  const categoryOptions = ref<TreeItem[]>([]);
  getCategoryList();
  async function getCategoryList() {
    categoryOptions.value = (await getDicDetailList({
      itemId: FlowCategory.ID,
    })) as unknown as TreeItem[];
  }

  const handleAbstract = (data) => {
    processInfo.value.summaryInfo = data;
  };

  const handleInfo = (info) => {
    summary.value = info;
  };
</script>

<style lang="less" scoped></style>
