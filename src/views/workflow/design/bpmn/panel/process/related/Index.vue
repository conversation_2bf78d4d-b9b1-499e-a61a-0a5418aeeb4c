<template>
  <div class="list-box">
    <div class="opr-box">
      <NodeHead :nodeName="t('模板列表')" />
      <div class="button-box">
        <SelectDesign :single="false" :list="processInfo.relationProcessConfigs" @change="add">
          <a-button type="primary">{{ t('添加流程') }}</a-button>
        </SelectDesign>
      </div>
    </div>
    <div class="list">
      <div class="row head">
        <span>{{ t('流程模板名称') }}</span>
        <span>{{ t('任务状态') }}</span>
        <span>{{ t('任务权限') }}</span>
        <span class="small">{{ t('操作') }}</span>
      </div>
      <div class="body" v-if="processInfo.relationProcessConfigs.length > 0">
        <div
          class="row item"
          v-for="(item, index) in processInfo.relationProcessConfigs"
          :key="index"
        >
          <span class="name">
            <a-tooltip>
              <template #title>{{ item.name }}</template>
              {{ item.name }}
            </a-tooltip>
          </span>
          <span>
            <a-select
              v-model:value="item.processStatus"
              style="width: 100px"
              :options="processStatusOptions"
            />
          </span>
          <span>
            <a-select
              v-model:value="item.processAuth"
              style="width: 140px"
              :options="processAuthOptions"
          /></span>
          <span @click="deleteItem(index)" class="small">
            <Icon icon="ant-design:delete-outlined" class="delete-icon" />
          </span>
        </div>
      </div>
      <EmptyBox v-else :has-icon="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { NodeHead, EmptyBox } from '/@/components/ModalPanel/index';
  import SelectDesign from '/@bpmn/components/design/SelectDesign.vue';
  import Icon from '/@/components/Icon/index';
  import { ProcessStatus, TaskPermissions } from '/@/enums/workflowEnum';
  import { RelationProcessConfig } from '/@/model/workflow/workflowConfig';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import { storeToRefs } from 'pinia';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const store = useBpmnStore();
  const { processInfo } = storeToRefs(store);
  //任务状态
  const processStatusOptions = [
    {
      value: ProcessStatus.APPROVAL_INPROGRESS,
      label: t('审批中'),
    },
    {
      value: ProcessStatus.APPROVAL_COMPLETED,
      label: t('审批通过'),
    },
  ];
  //任务权限
  const processAuthOptions = [
    {
      value: TaskPermissions.LIMITED_TO_PROMOTER_INITIATED,
      label: t('限发起人发起'),
    },
    {
      value: TaskPermissions.EVERYONE_LAUNCHES,
      label: t('所有人发起'),
    },
  ];
  //添加流程模板
  function add(tempList: Array<RelationProcessConfig>) {
    processInfo.value.relationProcessConfigs = tempList;
  }
  //删除流程模板
  function deleteItem(index: number) {
    processInfo.value.relationProcessConfigs.splice(index, 1);
  }
</script>

<style lang="less" scoped>
  .list-box {
    .opr-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }

  .list {
    .row {
      height: 40px;
      line-height: 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      span {
        flex-basis: 25%;
        display: flex;
        justify-content: center;
        align-items: center;

        &.name {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 100%;
          display: block;
          padding: 0 10px;
        }
      }

      .small {
        flex-basis: 50px;
      }
    }

    .head {
      background-color: #f9f9f9;
    }

    .item {
      border-bottom: 1px solid #f9f9f9;
    }

    .delete-icon {
      color: @clear-color;
    }
  }
</style>
