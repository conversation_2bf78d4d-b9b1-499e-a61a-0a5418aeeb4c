<template>
  <BasicPanel>
    <template #noticePolicy v-if="showPanel">
      <FormItem :label="t('通知策略：')">
        <NoticePolicyConfig v-model="formInfo.noticePolicyConfigs" />
      </FormItem>
    </template>
  </BasicPanel>
</template>

<script setup lang="ts">
  import useStateFormInfo from '/@bpmn/hooks/useStateFormInfo';
  import BasicPanel from '/@bpmn/components/BasicPanel.vue';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import NoticePolicyConfig from '/@bpmn/components/NoticePolicyConfig.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const { showPanel, formInfo } = useStateFormInfo();
</script>

<style scoped></style>
