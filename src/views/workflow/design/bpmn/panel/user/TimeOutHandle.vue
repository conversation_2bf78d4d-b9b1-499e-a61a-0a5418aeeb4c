<template>
  <FormItem :label="t('超时处理：')" required>
    <a-select v-model:value="formInfo.timeOutHandle.isHandle" style="width: 100%">
      <a-select-option :value="1">{{ t('不启用超时处理') }}</a-select-option>
      <a-select-option :value="2">{{
        t('启用超时处理（需开启超时提醒，否则无效）')
      }}</a-select-option>
    </a-select>
  </FormItem>
  <template v-if="formInfo.timeOutHandle.isHandle == 2">
    <FormItem :label="t('超时机制：')" required>
      <a-select v-model:value="formInfo.timeOutHandle.rule" style="width: 100%">
        <a-select-option :value="1">{{ t('超过最大推送次数则即时处理') }}</a-select-option>
        <a-select-option :value="2">{{ t('首次超时即处理') }}</a-select-option>
      </a-select>
    </FormItem>
    <FormItem :label="t('处理方式：')" required>
      <a-select v-model:value="formInfo.timeOutHandle.type" style="width: 100%">
        <a-select-option :value="1">{{ t('自动驳回至上一节点') }}</a-select-option>
        <a-select-option :value="2">{{ t('自动同意并向下流转') }}</a-select-option>
      </a-select>
    </FormItem>
    <FormItem :label="t('指定人员：')" required>
      <a-select v-model:value="formInfo.timeOutHandle.user" style="width: 100%">
        <a-select-option :value="1">{{
          t('如果需要指定下一节点审批人，系统按默认处理')
        }}</a-select-option>
      </a-select>
    </FormItem>
    <FormItem :label="t('无处理人：')" required>
      <a-select v-model:value="formInfo.timeOutHandle.auto" style="width: 100%">
        <a-select-option :value="1">{{ t('自动指派给管理员') }}</a-select-option>
      </a-select>
    </FormItem>
  </template>
</template>
<script setup lang="ts">
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import { storeToRefs } from 'pinia';
  import { ref } from 'vue';
  import FormItem from '/@bpmn/layout/FormItem.vue';

  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const store = useBpmnStore();
  const { infoId } = store;
  const { info } = storeToRefs(store);
  const formInfo = ref();

  formInfo.value = info.value.get(infoId);
</script>
