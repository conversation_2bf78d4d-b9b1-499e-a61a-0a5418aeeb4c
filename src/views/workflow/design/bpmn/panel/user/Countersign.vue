<template>
  <FormItem :label="t('会签类型：')">
    <a-radio-group
      v-model:value="formInfo.countersignConfig.multipleInstancesType"
      @change="changeMultipleInstancesType"
    >
      <a-radio-button
        v-for="(item, index) in multipleInstancesTypeList"
        :key="index"
        :value="item.value"
        >{{ item.label }}</a-radio-button
      >
    </a-radio-group>
  </FormItem>

  <template v-if="formInfo.countersignConfig.multipleInstancesType != MultipleInstancesType.NONE">
    <FormItem :label="t('加签减签：')">
      <a-select
        :value="formInfo.countersignConfig.addOrRemove"
        style="width: 100%"
        @change="changeAddOrRemove"
      >
        <a-select-option :title="t('允许')" :value="AddOrRemoveType.ALLOW">
          {{ t('允许') }}
        </a-select-option>
        <a-select-option :title="t('禁止')" :value="AddOrRemoveType.FORBID">
          {{ t('禁止') }}
        </a-select-option>
      </a-select>
    </FormItem>
    <FormItem :label="t('完成条件：')">
      <a-select
        :value="formInfo.countersignConfig.finishType"
        style="width: 100%"
        @change="changeFinishType"
      >
        <a-select-option :title="t('全部')" :value="InstanceCompletionConditions.ALL">
          {{ t('全部') }}
        </a-select-option>
        <a-select-option :title="t('单个')" :value="InstanceCompletionConditions.SINGLE">
          {{ t('单个') }}
        </a-select-option>
        <a-select-option :title="t('百分比')" :value="InstanceCompletionConditions.PERCENTAGE">
          {{ t('百分比') }}
        </a-select-option>
      </a-select>
    </FormItem>
    <FormItem
      :label="t('百分比：')"
      v-if="formInfo.countersignConfig.finishType == InstanceCompletionConditions.PERCENTAGE"
    >
      <a-input-number
        :value="formInfo.countersignConfig.percentage"
        :placeholder="t('请填写百分比数值')"
        @change="changePercentage"
        style="width: 100%"
      />
    </FormItem>
    <NodeHead class="member-box" :node-name="t('人员列表')">
      <div class="remark">{{
        t('备注：会签人员名单与审批人名单一致，如需修改请调整审批人名单')
      }}</div>
    </NodeHead>

    <a-table
      :dataSource="formInfo.countersignConfig.countersignList"
      :columns="configColumns"
      :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'checked'">
          <a-checkbox v-model:checked="record.checked" />
        </template>
        <template v-if="column.key === 'memberType'">
          {{ getMemberType(record.memberType) }}
        </template>
        <template v-if="column.key === 'name'">
          {{ record.name }}
        </template>
      </template>
    </a-table>
  </template>
</template>

<script setup lang="ts">
  import { NodeHead } from '/@/components/ModalPanel/index';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import {
    AddOrRemoveType,
    MultipleInstancesType,
    InstanceCompletionConditions,
  } from '/@/enums/workflowEnum';
  import { inject, ref } from 'vue';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import { storeToRefs } from 'pinia';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getMemberType } from '../../config/info';

  const { t } = useI18n();
  const configColumns = [
    {
      title: t('选择'),
      dataIndex: 'checked',
      key: 'checked',
      align: 'center',
    },
    {
      title: t('类型'),
      dataIndex: 'memberType',
      key: 'memberType',
      align: 'center',
    },
    {
      title: t('名称'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
  ];
  const updateCountersign = inject('updateCountersign') as any;
  const store = useBpmnStore();
  const { infoId } = store;
  const { info } = storeToRefs(store);
  const formInfo = ref();

  formInfo.value = info.value.get(infoId);

  // 会签类型
  const multipleInstancesTypeList = [
    {
      value: MultipleInstancesType.NONE,
      label: t('不启用会签'),
    },
    {
      value: MultipleInstancesType.SYNC,
      label: t('同步会签'),
    },
    {
      value: MultipleInstancesType.ASYNC,
      label: t('顺序会签'),
    },
  ];
  function changeMultipleInstancesType() {
    if (formInfo.value.countersignConfig.multipleInstancesType == MultipleInstancesType.NONE) {
      formInfo.value.countersignConfig.countersignList = [];
    }
    changeCountersign(); //更新会签节点
  }
  function changeAddOrRemove(val: AddOrRemoveType) {
    formInfo.value.countersignConfig.addOrRemove = val;
  }
  function changeFinishType(val: InstanceCompletionConditions) {
    formInfo.value.countersignConfig.finishType = val;
    changeCountersign(); //更新会签节点
  }
  function changePercentage(val: number) {
    formInfo.value.countersignConfig.percentage = val;
    changeCountersign(); //更新会签节点
  }

  function changeCountersign() {
    updateCountersign(
      formInfo.value.countersignConfig.multipleInstancesType,
      formInfo.value.countersignConfig.finishType,
      formInfo.value.countersignConfig.percentage,
    ); //更新会签节点
  }
</script>

<style lang="less" scoped>
  .remark {
    flex-basis: 74%;
    margin-left: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
  }

  .member-box {
    margin-bottom: 10px;
  }
</style>
