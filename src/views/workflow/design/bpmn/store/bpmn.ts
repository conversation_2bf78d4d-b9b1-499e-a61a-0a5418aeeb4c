import { defineStore } from 'pinia';
import { getProcessConfig, getStartNodeId } from '../config/property';
import { cloneDeep } from 'lodash-es';
import { BpmnState, InfoId, InfoItem } from '/@/model/workflow/bpmnConfig';
import { FormSettingItem } from '/@/model/workflow/formSetting';
import { ProcessConfig } from '/@/model/workflow/workflowConfig';

export const useBpmnStore = defineStore({
  id: 'bpmn-store',
  state: (): BpmnState => ({
    modelId: '',
    info: new Map(),
    infoId: '',
    processInfo: cloneDeep(getProcessConfig),
    defaultFormList: [],
  }),
  getters: {
    isMainStartNode: (state) => state.infoId === getStartNodeId,
  },
  actions: {
    setProcessInfo(info: ProcessConfig) {
      this.processInfo = info;
    },
    setProperties(key: InfoId, value: InfoItem) {
      this.info.set(key, value);
    },
    hasProperties(key: InfoId) {
      return this.info.has(key);
    },
    getProperties(key: InfoId) {
      return this.info.has(key) ? this.info.get(key) : null;
    },
    deleteProperties(key: InfoId) {
      this.infoId = '';
      if (this.info.has(key)) {
        this.info.delete(key);
      }
    },
    setInfoId(infoId: InfoId) {
      this.infoId = infoId;
    },
    setDefaultFormList(list: Array<FormSettingItem>) {
      this.defaultFormList = list;
    },
  },
});
