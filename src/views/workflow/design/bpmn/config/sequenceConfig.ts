import { ApproveCode } from '/@/enums/workflowEnum';
import { getVariablesTree } from '/@bpmn/config/info';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
// 运算变量
export const getDataOfVariablesTree = () => {
  const res = getVariablesTree({ needUserNodeButton: true, needHideComponents: true });
  return res ? res : [];
};
// 运算符
export const operators = [
  {
    value: '==',
    label: '==',
  },
  {
    value: '+',
    label: '+',
  },
  {
    value: '-',
    label: '-',
  },
  {
    value: '*',
    label: '*',
  },
  {
    value: '/',
    label: '/',
  },
  {
    value: '(',
    label: '(',
  },
  {
    value: ')',
    label: ')',
  },
  {
    value: '!=',
    label: '!=',
  },
  {
    value: '>',
    label: '>',
  },
  {
    value: '>=',
    label: '>=',
  },
  {
    value: '<',
    label: '<',
  },
  {
    value: '<=',
    label: '<=',
  },
  {
    value: '.contains',
    label: '⊇',
  },
];

export const buttons = [
  {
    key: ApproveCode.AGREE,
    title: t('同意'),
  },
  {
    key: ApproveCode.REJECT,
    title: t('拒绝'),
  },
];
// 与或非
export const andOrNotList = [
  {
    value: '&&',
    label: t('与'),
  },
  {
    value: '||',
    label: t('或'),
  },
  {
    value: '!=',
    label: t('非'),
  },
];

//特殊值
export const specialValues = [
  {
    value: 'null',
    label: 'null',
  },
];

//会签结果值
export const multipleInstances = [
  {
    value: 'true',
    label: '通过',
  },
  {
    value: 'false',
    label: '不通过',
  },
];
