import {
  AddOrRemoveType,
  ApprovalOpinionDisplayType,
  AuthType,
  BpmnNodeKey,
  ButtonType,
  MultipleInstancesType,
  DesignatedApprover,
  ElectronicSignatureVerification,
  FormType,
  InstanceCompletionConditions,
  NoHandler,
  RecordType,
  RejectType,
  CallActivityType,
  FinishType,
  ExecutionType,
  MemberType,
  ApproveType,
  ApproveCode,
  TimeOutHandle,
  TimeOutRule,
  TimeOutType,
} from '/@/enums/workflowEnum';
import { InfoType } from '/@/model/workflow/bpmnConfig';
import {
  CallActivityConfig,
  EndEventConfig,
  GatewayConfig,
  ProcessConfig,
  ScriptTaskConfig,
  SequenceFlowConfig,
  StartEventConfig,
  UserTaskConfig,
} from '/@/model/workflow/workflowConfig';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
// 流程模板默认属性
export const processConfig: ProcessConfig = {
  processId: '', //节点id
  type: BpmnNodeKey.PROCESS, //节点类型
  code: '', //模板编码
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  category: undefined, //分类
  nameRule: '', //命名规则
  nameRuleConfigs: [], // 命名规则列表
  autoAgreeRule: [], //自动同意规则
  isPrevChooseNext: DesignatedApprover.NOT_SPECIFIED, //是否上一节点审批人指定下一节点审批人
  noHandler: NoHandler.ADMIN, //无对应处理人
  appShow: false, //移动端是否显示
  defaultFormList: [], //默认表单
  remark: '', //节点描述
  content: '', //xml
  authConfig: {
    //权限设置
    authType: AuthType.ALL,
    authMemberConfigs: [],
  },
  menuConfig: {
    //是否启用菜单
    enabled: false,
    code: '',
    name: '',
    system: undefined,
    parentId: undefined,
    icon: '',
    order: undefined,
    remark: '',
  },
  formInitConfig: {
    //表单发起流程
    enabled: false,
    formType: FormType.CUSTOM,
    formId: '',
    formName: '',
  },
  timeoutRemidConfig: {
    //超时提醒配置
    enabled: false,
    hour: undefined, //超时时间
    interval: undefined, //消息间隔
    pushHits: undefined, //推送次数
    pushMemberConfigs: [], //推送人
  },
  relationProcessConfigs: [], //关联任务
  processParamConfigs: [], //流程参数
  xmlContent: '',
  summaryInfo: [], //摘要信息
};
// 默认属性
export const defaultProperties = {
  id: '', //节点id
  type: BpmnNodeKey.EVENT, //节点类型
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
};
// 开始节点默认属性
const StartProperties: StartEventConfig = {
  id: '', //节点id
  type: BpmnNodeKey.START, //节点类型
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  formConfigs: [], //表单配置
  subProcessInitiator: '', //子流程开始节点发起人
  assignmentConfig: { formAssignmentConfigs: [], paramAssignmentConfigs: [] }, //参数操作
  startEventConfigs: [],
  endEventConfigs: [],
};
// 结束节点默认属性
const EndProperties: EndEventConfig = {
  id: '', //节点id
  type: BpmnNodeKey.END, //节点类型
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  noticePolicyConfigs: [], //通知策略
  startEventConfigs: [],
  endEventConfigs: [],
};
// 用户节点默认属性
const UserProperties: UserTaskConfig = {
  id: '', //节点id
  type: BpmnNodeKey.USER, //节点类型
  currentProgress: undefined, //进度
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  formConfigs: [], //表单配置
  noticePolicyConfigs: [], //通知策略
  approverConfigs: [], //审批人
  circulateConfigs: [], //传阅人
  autoAgreeRule: [], //自动同意规则
  isPrevChooseNext: DesignatedApprover.NOT_SPECIFIED, //是否上一节点审批人指定下一节点审批人
  provisionalApprover: false, //临时审批人
  noHandler: NoHandler.ADMIN, //无对应处理人
  countersignConfig: {
    multipleInstancesType: MultipleInstancesType.NONE, //多实例类型
    addOrRemove: AddOrRemoveType.ALLOW, //加签 或者 减签
    finishType: InstanceCompletionConditions.ALL, //完成条件  多实例
    percentage: 0,
    countersignList: [],
  }, //会签
  buttonConfigs: [
    {
      buttonType: ButtonType.DEFAULT, //按钮类型
      buttonName: t('同意'), //按钮名称
      buttonCode: ApproveCode.AGREE, //按钮编码
      approveType: ApproveType.AGREE, //
      checked: true, //选中
    },
    {
      buttonType: ButtonType.DEFAULT, //按钮类型
      buttonName: t('拒绝'), //按钮名称
      buttonCode: ApproveCode.DISAGREE, //按钮编码
      approveType: ApproveType.DISAGREE, //
      checked: true, //选中
    },
    {
      buttonType: ButtonType.DEFAULT, //按钮类型
      buttonName: t('驳回'), //按钮名称
      buttonCode: ApproveCode.REJECT, //按钮编码
      approveType: ApproveType.REJECT, //
      checked: true, //选中
      buttonOpera: RejectType.ALL,
    },
    {
      buttonType: ButtonType.DEFAULT, //按钮类型
      buttonName: t('结束'), //按钮名称
      buttonCode: ApproveCode.FINISH, //按钮编码
      approveType: ApproveType.FINISH, //
      checked: true, //选中
    },
    {
      buttonType: ButtonType.DEFAULT, //按钮类型
      buttonName: t('暂存'), //按钮名称
      buttonCode: ApproveCode.STAGING, //按钮编码
      approveType: ApproveType.STAGING, //
      checked: true, //选中
    },
  ], //按钮配置
  opinionConfig: {
    enabled: false, //是否关联表单
    showType: ApprovalOpinionDisplayType.ALL, //审批意见展示类型 0 显示所有 1 显示最终结果
    signature: ElectronicSignatureVerification.NO_PASSWORD_REQUIRED, //电子签章验证
    component: [], //关联意见框组件
  },
  assignmentConfig: {
    formAssignmentConfigs: [],
    paramAssignmentConfigs: [],
  }, //参数操作
  timeOutHandle: {
    isHandle: TimeOutHandle.NO,
    rule: TimeOutRule.MAX,
    type: TimeOutType.AGREE,
    user: 1,
    auto: 1,
  }, //超时处理
  startEventConfigs: [],
  endEventConfigs: [],
  agreeEventConfigs: [], //同意事件
  rejectEventConfigs: [], //驳回事件
  withdrawEventConfigs: [], //撤回事件
  timeoutEventConfigs: [], //超时事件
};
// 脚本节点默认属性
const ScriptProperties: ScriptTaskConfig = {
  id: '', //节点id
  type: BpmnNodeKey.SCRIPT, //节点类型
  // Recorded
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  recordInfo: RecordType.RECORD,
  noticePolicyConfigs: [], //通知策略
  script: {
    enabled: false,
    scriptFormat: 'JavaScript', //脚本格式
    scriptContent: ' var a = 1', //脚本内容
  },
  api: {
    enabled: false,
    apiConfig: {
      id: '',
      name: '',
      method: '',
      requestParamsConfigs: [], //Query Params
      requestHeaderConfigs: [], //Header
      requestBodyConfigs: [], //Body
    },
  },
  assignmentConfig: { formAssignmentConfigs: [], paramAssignmentConfigs: [] }, //参数操作
  startEventConfigs: [],
  endEventConfigs: [],
};
// 连接线
const SequenceFlowProperties: SequenceFlowConfig = {
  id: '', //节点id
  type: BpmnNodeKey.SEQUENCEFLOW, //节点类型
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  conditionConfigs: [], //流转条件
  startEventConfigs: [],
  endEventConfigs: [],
};
// 子流程
const SubProcessProperties = {
  id: '', //节点id
  type: BpmnNodeKey.SUB_PROCESS, //节点类型
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  startEventConfigs: [],
  endEventConfigs: [],
};
// 外部流程
const CallActivityProperties: CallActivityConfig = {
  id: '', //节点id
  type: BpmnNodeKey.CALLACTIVITY, //节点类型
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  schemaId: '', //被调用元素（外部流程schemaId）
  schemaName: '', //被调用元素（外部流程名称）
  callActivityType: CallActivityType.SINGLE, // 调用类型
  finishType: FinishType.ALL, //完成条件
  percentOf: undefined, //百分比数值
  executionType: ExecutionType.SEQUENCE, //执行类型
  originatorNode: '', //如果调用类型为单实例 子流程发起人
  originatorType: MemberType.FORM_FIELD, //发起人类型
  originatorConfig: '', //表单数据
  inParams: [], //输入参数
  outParams: [], //输出参数
  startEventConfigs: [],
  endEventConfigs: [],
};
// 互斥网关
const ExclusiveGatewayProperties: GatewayConfig = {
  id: '', //节点id
  type: BpmnNodeKey.EXCLUSIVE, //节点类型
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  startEventConfigs: [],
  endEventConfigs: [],
};
// 相容网关
const InclusiveGatewayProperties: GatewayConfig = {
  id: '', //节点id
  type: BpmnNodeKey.INCLUSIVE, //节点类型
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  startEventConfigs: [],
  endEventConfigs: [],
};
// 并行网关
const ParallelGatewayProperties: GatewayConfig = {
  id: '', //节点id
  type: BpmnNodeKey.PARALLEL, //节点类型
  name: '', //节点名称
  parentId: '', //父节点（流程id）
  remark: '', //节点描述
  startEventConfigs: [],
  endEventConfigs: [],
};
// 获取节点默认属性
export const propertiesByType: Map<InfoType, any> = new Map([
  [BpmnNodeKey.START, StartProperties],
  [BpmnNodeKey.END, EndProperties],
  [BpmnNodeKey.USER, UserProperties],
  [BpmnNodeKey.SCRIPT, ScriptProperties],
  [BpmnNodeKey.EXCLUSIVE, ExclusiveGatewayProperties],
  [BpmnNodeKey.INCLUSIVE, InclusiveGatewayProperties],
  [BpmnNodeKey.PARALLEL, ParallelGatewayProperties],
  [BpmnNodeKey.SUB_PROCESS, SubProcessProperties],
  [BpmnNodeKey.CALLACTIVITY, CallActivityProperties],
  [BpmnNodeKey.SEQUENCEFLOW, SequenceFlowProperties],
]);
// 根据节点类型获取节点名称
export const nodeNameByType: Map<InfoType, any> = new Map([
  [BpmnNodeKey.START, t('开始节点')],
  [BpmnNodeKey.END, t('结束节点')],
  [BpmnNodeKey.USER, t('用户节点')],
  [BpmnNodeKey.SCRIPT, t('脚本节点')],
  [BpmnNodeKey.EXCLUSIVE, t('互斥网关')],
  [BpmnNodeKey.INCLUSIVE, t('相容网关')],
  [BpmnNodeKey.PARALLEL, t('并行网关')],
  [BpmnNodeKey.SUB_PROCESS, t('子流程')],
  [BpmnNodeKey.CALLACTIVITY, t('外部流程')],
  [BpmnNodeKey.SEQUENCEFLOW, t('流程线')],
  [BpmnNodeKey.PROCESS, t('流程模板')],
]);
