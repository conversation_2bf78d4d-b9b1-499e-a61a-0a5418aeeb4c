import { storeToRefs } from 'pinia';
import { separator } from './info';
import { BpmnNodeKey, ParamType } from '/@/enums/workflowEnum';
import { InfoItem } from '/@/model/workflow/bpmnConfig';
import { useBpmnStore } from '/@bpmn/store/bpmn';

// 删除参数：删除流程参数会清空已引用该参数的所有配置
export function updateProcessParameterRelevance(formKey: string) {
  const bpmnStore = useBpmnStore();
  const { info } = storeToRefs(bpmnStore);
  for (const [key, item] of info.value) {
    if (
      item.type == BpmnNodeKey.START ||
      item.type == BpmnNodeKey.USER ||
      item.type == BpmnNodeKey.SCRIPT
    ) {
      const element: null | InfoItem = changeProcessParamAssignmentConfig(formKey, item);
      if (element) {
        bpmnStore.setProperties(key, element);
      }
    } else if (item.type == BpmnNodeKey.CALLACTIVITY) {
      const element: null | InfoItem = callactivityInfoFromSearchKey(formKey, item);
      if (element) {
        bpmnStore.setProperties(key, element);
      }
    }
  }
}
// 删除表单：删除表单会清空已引用该表单数据的所有配置
export function updateFormDataInfo(formKey: string) {
  const bpmnStore = useBpmnStore();
  const { info } = storeToRefs(bpmnStore);
  for (const [key, item] of info.value) {
    if (item.type == BpmnNodeKey.START || item.type == BpmnNodeKey.USER) {
      const element: null | InfoItem = changeFormInfoAssignmentConfig(formKey, item);
      if (element) {
        bpmnStore.setProperties(key, element);
      }
    } else if (item.type == BpmnNodeKey.SEQUENCEFLOW) {
      const element: null | InfoItem = sequenceInfoFromSearchKey(formKey, item);
      if (element) {
        bpmnStore.setProperties(key, element);
      }
    } else if (item.type == BpmnNodeKey.CALLACTIVITY) {
      const element: null | InfoItem = callactivityInfoFromSearchKey(formKey, item);
      if (element) {
        bpmnStore.setProperties(key, element);
      }
    }
  }
}
// 更新表单：表单字段在新表单中被删除了需要清空已引用该表单字段数据的所有配置
export function updateFormFieldIdRelevance(formKey: string, field: string) {
  const bpmnStore = useBpmnStore();
  const { info } = storeToRefs(bpmnStore);

  for (const [key, item] of info.value) {
    const filedKey = item.id + separator + formKey + separator + field;
    if (item.type == BpmnNodeKey.START || item.type == BpmnNodeKey.USER) {
      const element: null | InfoItem = changeFormInfoAssignmentConfig(filedKey, item);
      if (element) {
        bpmnStore.setProperties(key, element);
      }
    } else if (item.type == BpmnNodeKey.SEQUENCEFLOW) {
      const element: null | InfoItem = sequenceInfoFromSearchKey(filedKey, item);
      if (element) {
        bpmnStore.setProperties(key, element);
      }
    } else if (item.type == BpmnNodeKey.CALLACTIVITY) {
      const element: null | InfoItem = callactivityInfoFromSearchKey(filedKey, item);
      if (element) {
        bpmnStore.setProperties(key, element);
      }
    }
  }
}
// 节点，剔除已经配置了此流程参数的数据【参数操作】
function changeProcessParamAssignmentConfig(formKey: string, element: InfoItem): null | InfoItem {
  let needChange = false;
  if (
    element.assignmentConfig &&
    element.assignmentConfig.formAssignmentConfigs &&
    element.assignmentConfig.formAssignmentConfigs.length > 0
  ) {
    element.assignmentConfig.formAssignmentConfigs =
      element.assignmentConfig.formAssignmentConfigs.filter((item) => {
        if (item.source === formKey) {
          needChange = true; // 删除
          return false;
        } else {
          return true;
        }
      });
  }
  if (
    element.assignmentConfig &&
    element.assignmentConfig.paramAssignmentConfigs &&
    element.assignmentConfig.paramAssignmentConfigs.length > 0
  ) {
    element.assignmentConfig.paramAssignmentConfigs =
      element.assignmentConfig.paramAssignmentConfigs.filter((item) => {
        if (item.target === formKey) {
          needChange = true; // 删除
          return false;
        } else {
          return true;
        }
      });
  }
  return needChange ? element : null;
}
// 节点，剔除已经配置了此表单的的表单数据【参数操作】
function changeFormInfoAssignmentConfig(formKey: string, element: InfoItem): null | InfoItem {
  let needChange = false;
  if (
    element.assignmentConfig &&
    element.assignmentConfig.formAssignmentConfigs &&
    element.assignmentConfig.formAssignmentConfigs.length > 0
  ) {
    element.assignmentConfig.formAssignmentConfigs =
      element.assignmentConfig.formAssignmentConfigs.filter((item) => {
        if (item.target['key'].includes(formKey)) {
          needChange = true; // 删除
          return false;
        } else {
          return true;
        }
      });
  }
  if (
    element.assignmentConfig &&
    element.assignmentConfig.paramAssignmentConfigs &&
    element.assignmentConfig.paramAssignmentConfigs.length > 0
  ) {
    element.assignmentConfig.paramAssignmentConfigs =
      element.assignmentConfig.paramAssignmentConfigs.filter((item) => {
        if (item.type === ParamType.FORM_DATA && item.formConfig['key'].includes(formKey)) {
          needChange = true; // 删除
          return false;
        } else {
          return true;
        }
      });
  }
  return needChange ? element : null;
}

// 流程线，剔除已经配置了此表单的的表单数据【流转条件】
function sequenceInfoFromSearchKey(formKey: string, element: InfoItem): null | InfoItem {
  let needChange = false;
  if (element.conditionConfigs && element.conditionConfigs.length > 0) {
    element.conditionConfigs = element.conditionConfigs.filter((item) => {
      if (item['value'].includes(formKey)) {
        needChange = true; // 删除
        return false;
      } else {
        return true;
      }
    });
  }
  if (needChange) {
    element.conditionConfigs = [];
  }
  return needChange ? element : null;
}
// 外部流程，剔除已经配置了此表单的的表单数据【输入参数，输出参数】
function callactivityInfoFromSearchKey(formKey: string, element: InfoItem) {
  let needChange = false;
  if (element.inParams && element.inParams.length > 0) {
    element.inParams = element.inParams.filter((item) => {
      if (
        (item.source && item.source.includes(formKey)) ||
        (item.target && item.target.includes(formKey))
      ) {
        needChange = true; // 删除
        return false;
      } else {
        return true;
      }
    });
  }
  if (element.outParams && element.outParams.length > 0) {
    element.outParams = element.outParams.filter((item) => {
      if (
        (item.source && item.source.includes(formKey)) ||
        (item.target && item.target.includes(formKey))
      ) {
        needChange = true; // 删除
        return false;
      } else {
        return true;
      }
    });
  }
  return needChange ? element : null;
}
