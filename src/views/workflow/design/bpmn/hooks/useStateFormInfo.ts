import { computed, onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useBpmnStore } from '/@bpmn/store/bpmn';
import { getNodeName } from '/@bpmn/config/property';

export default function () {
  const store = useBpmnStore();
  const { infoId, isMainStartNode } = store;
  const { info } = storeToRefs(store);
  const showPanel = ref(false);
  const formInfo = ref();

  onMounted(() => {
    if (infoId) {
      formInfo.value = info.value.get(infoId);
      showPanel.value = true;
    }
  });
  const nodeName = computed(() => {
    if (formInfo.value && formInfo.value.type) {
      const labelName = getNodeName(formInfo.value.type);
      return labelName;
    }
    return '节点';
  });
  return {
    formInfo,
    nodeName,
    showPanel,
    isMainStartNode,
  };
}
