<template>
  <div class="form-item">
    <div class="form-item-label" v-if="props.label">
      <a-tooltip v-if="props.tip" :title="props.tip" color="#545454">
        <Icon icon="ant-design:question-circle-outlined" />
      </a-tooltip>
      <em class="text-red-600" v-if="props.required">*</em>{{ props.label }}</div
    >
    <div class="form-item-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Icon } from '/@/components/Icon';
  const props = defineProps({
    required: <PERSON><PERSON><PERSON>,
    label: String,
    tip: String,
  });
</script>

<style lang="less" scoped>
  /* 表单 */
  .form-item {
    display: flex;
    margin: 10px 0;
  }

  .form-item-content {
    flex: 1;
  }

  .form-item-label {
    flex-basis: 100px;
    line-height: 2;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  :deep(.ant-select),
  :deep(.ant-select-item) {
    font-size: 14px;
  }
</style>
