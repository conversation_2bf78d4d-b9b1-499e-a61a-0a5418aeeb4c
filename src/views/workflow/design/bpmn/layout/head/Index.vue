<template>
  <div class="header-box box-shadow bg-white dark:bg-dark-900">
    <div class="left">
      <DesignLogo />
      <div class="header-title">{{ t(title || '新增流程设计') }}</div>
    </div>

    <div class="button-box">
      <UpdateFormData />
      <a-button type="primary" class="bpmn-button" @click="$emit('save')">{{
        t('保存模板')
      }}</a-button>
      <a-button type="primary" class="clean-icon" @click="$emit('close')">{{ t('关闭') }}</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { DesignLogo } from '/@/components/ModalPanel/index';
  import UpdateFormData from '/@bpmn/components/UpdateFormData.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  defineEmits(['save', 'close', 'updateFormData']);
  defineProps({
    title: String,
  });
</script>

<style lang="less" scoped>
  .header-box {
    height: 70px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .box-shadow {
    box-shadow: 5px 5px 5px rgb(0 0 0 / 10%);
  }

  .left {
    display: flex;
    align-items: center;
  }

  .header-title {
    font-size: 16px;
    color: #333;
    margin-right: 10px;
  }

  [data-theme='dark'] .header-title {
    color: #fff;
  }

  .button-box {
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-right: 20px;
  }

  .inline-box {
    font-size: 12px;
  }

  .bpmn-button {
    margin: 0 4px;
  }

  .clean-icon {
    background-color: @clear-color;
    border-color: @clear-color;
  }
</style>
