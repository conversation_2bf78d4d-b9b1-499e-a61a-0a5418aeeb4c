import inherits from 'inherits';

import Modeler from 'bpmn-js/lib/Modeler';

import CustomModule from './custom';

function CustomModeler(this: any, options: unknown) {
  Modeler.call(this, options);

  this._customElements = [];
}

inherits(CustomModeler, Modeler);

const arr: any = [CustomModule];

CustomModeler.prototype._modules = [].concat(CustomModeler.prototype._modules, arr);

export default CustomModeler;
