import CustomPalette from './CustomPalette';
import CustomRenderer from './CustomRenderer';
import CustomContextPad from './CustomContextPad';
import BpmnGridSnapping from './BpmnGridSnapping';
export default {
  __init__: ['paletteProvider', 'customRenderer', 'customContextPad', 'bpmnGridSnapping'],
  paletteProvider: ['type', CustomPalette],
  customRenderer: ['type', CustomRenderer],
  customContextPad: ['type', CustomContextPad],
  bpmnGridSnapping: ['type', BpmnGridSnapping],
};
