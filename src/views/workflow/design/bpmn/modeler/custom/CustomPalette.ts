import { assign } from 'min-dash';
import { getTranslate } from './../util/TranslateUtil';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
export default function PaletteProvider(
  this: any,
  palette: { registerProvider: (arg0: any) => void },
  create: any,
  elementFactory: any,
  spaceTool: any,
  lassoTool: any,
  handTool: any,
  globalConnect: any,
  translate: any,
) {
  this._palette = palette;
  this._create = create;
  this._elementFactory = elementFactory;
  this._spaceTool = spaceTool;
  this._lassoTool = lassoTool;
  this._handTool = handTool;
  this._globalConnect = globalConnect;
  this._translate = translate;

  palette.registerProvider(this);
}

PaletteProvider.$inject = [
  'palette',
  'create',
  'elementFactory',
  'spaceTool',
  'lassoTool',
  'handTool',
  'globalConnect',
  'translate',
];

PaletteProvider.prototype.getPaletteEntries = function () {
  const actions = {},
    create = this._create,
    elementFactory = this._elementFactory,
    // spaceTool = this._spaceTool,
    // lassoTool = this._lassoTool,
    handTool = this._handTool,
    globalConnect = this._globalConnect;
  // translate = this._translate;

  function createAction(
    type: any,
    group: any,
    className: any,
    title: any,
    options?: { isExpanded: any } | undefined,
  ) {
    function createListener(event: any) {
      const shape = elementFactory.createShape(assign({ type: type }, options));

      if (options) {
        shape.businessObject.di.isExpanded = options.isExpanded;
      }

      create.start(event, shape);
    }

    const shortType = type.replace(/^bpmn:/, '');

    return {
      group: group,
      className: className,
      title: title || getTranslate('Create {type}', { type: shortType }),
      action: {
        dragstart: createListener,
        click: createListener,
      },
    };
  }

  function createSubprocess(event: any) {
    const subProcess = elementFactory.createShape({
      type: 'bpmn:SubProcess',
      x: 0,
      y: 0,
      isExpanded: true,
    });

    const startEvent = elementFactory.createShape({
      type: 'bpmn:StartEvent',
      x: 40,
      y: 82,
      parent: subProcess,
    });

    create.start(event, [subProcess, startEvent], {
      hints: {
        autoSelect: [startEvent],
      },
    });
  }

  // function createParticipant(event) {
  //   create.start(event, elementFactory.createParticipantShape());
  // }

  assign(actions, {
    'hand-tool': {
      group: 'tools',
      className: 'bpmn-icon-hand-tool',
      title: getTranslate(t('激活手动工具')),
      action: {
        click: function (event: any) {
          handTool.activateHand(event);
        },
      },
    },
    // 'lasso-tool': {
    //   group: 'tools',
    //   className: 'bpmn-icon-lasso-tool',
    //   title: getTranslate('激活套索工具'),
    //   action: {
    //     click: function (event: any) {
    //       lassoTool.activateSelection(event);
    //     },
    //   },
    // },
    // 'space-tool': {
    //   group: 'tools',
    //   className: 'bpmn-icon-space-tool',
    //   title: getTranslate('激活创建/删除空间工具'),
    //   action: {
    //     click: function (event: any) {
    //       spaceTool.activateSelection(event);
    //     },
    //   },
    // },
    'global-connect-tool': {
      group: 'tools',
      className: 'bpmn-icon-connection-multi',
      title: getTranslate(t('激活全局连接工具')),
      action: {
        click: function (event: any) {
          globalConnect.toggle(event);
        },
      },
    },

    // 'create.start-event': createAction(
    //   'bpmn:StartEvent',
    //   'event',
    //   'bpmn-icon-start-event-none',
    //   getTranslate('创建开始节点'),
    // ),
    'create.end-event': createAction(
      'bpmn:EndEvent',
      'event',
      'bpmn-icon-end-event-none',
      getTranslate(t('创建结束节点')),
    ),
    'create.exclusive-gateway': createAction(
      'bpmn:ExclusiveGateway',
      'event',
      'bpmn-icon-gateway-xor',
      getTranslate(t('创建互斥网关')),
    ),
    'create.inclusive-gateway': createAction(
      'bpmn:InclusiveGateway',
      'event',
      'bpmn-icon-gateway-or',
      getTranslate(t('创建相容网关')),
    ),
    'create.parallel-gateway': createAction(
      'bpmn:ParallelGateway',
      'event',
      'bpmn-icon-gateway-parallel',
      getTranslate(t('创建并行网关')),
    ),
    'create.user-task': createAction(
      'bpmn:UserTask',
      'event',
      'bpmn-icon-user',
      getTranslate(t('创建用户节点')),
    ),
    'create.script-task': createAction(
      'bpmn:ScriptTask',
      'event',
      'bpmn-icon-script',
      getTranslate(t('创建脚本节点')),
    ),
    // "create.intermediate-event": createAction(
    //   "bpmn:IntermediateThrowEvent",
    //   "event",
    //   "bpmn-icon-intermediate-event-none",
    //   getTranslate("Create Intermediate/Boundary Event")
    // ),

    // "create.task": createAction(
    //   "bpmn:Task",
    //   "activity",
    //   "bpmn-icon-task",
    //   getTranslate("Create Task")
    // ),
    // "create.data-object": createAction(
    //   "bpmn:DataObjectReference",
    //   "data-object",
    //   "bpmn-icon-data-object",
    //   getTranslate("Create DataObjectReference")
    // ),
    // "create.data-store": createAction(
    //   "bpmn:DataStoreReference",
    //   "data-store",
    //   "bpmn-icon-data-store",
    //   getTranslate("Create DataStoreReference")
    // ),
    'create.CallActivity': createAction(
      'bpmn:CallActivity',
      'event',
      'bpmn-icon-call-activity',
      getTranslate(t('创建外部流程')),
    ),
    'create.subprocess-expanded': {
      group: 'event',
      className: 'bpmn-icon-subprocess-expanded',
      title: getTranslate(t('创建子流程')),
      action: {
        dragstart: createSubprocess,
        click: createSubprocess,
      },
    },

    // "create.participant-expanded": {
    //   group: "collaboration",
    //   className: "bpmn-icon-participant",
    //   title: getTranslate("Create Pool/Participant"),
    //   action: {
    //     dragstart: createParticipant,
    //     click: createParticipant
    //   }
    // },
    // 'create.group': createAction(
    //   'bpmn:Group', 'artifact', 'bpmn-icon-group',
    //   getTranslate('Create Group')
    // ),
  });

  return actions;
};
