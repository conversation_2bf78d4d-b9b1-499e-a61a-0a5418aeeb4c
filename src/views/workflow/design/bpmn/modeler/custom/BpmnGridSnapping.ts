import { isAny } from './../util/ModelingUtil';

export default function BpmnGridSnapping(eventBus: {
  on: (arg0: string[], arg1: { (event: any): void; (event: any): void }) => void;
}) {
  eventBus.on(['create.init'], function (_event: { shape: any }) {
    // var shape = event.shape;
  });
  eventBus.on(['create.init', 'shape.move.init'], function (event: { context: any; shape: any }) {
    const context = event.context,
      shape = event.shape;
    if (isAny(shape, ['bpmn:Participant', 'bpmn:SubProcess', 'bpmn:TextAnnotation'])) {
      if (!context.gridSnappingContext) {
        context.gridSnappingContext = {};
      }

      context.gridSnappingContext.snapLocation = 'top-left';
    }
  });
}

BpmnGridSnapping.$inject = ['eventBus'];
