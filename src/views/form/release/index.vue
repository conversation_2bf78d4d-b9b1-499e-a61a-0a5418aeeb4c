<template>
  <PageWrapper dense fixed-height contentFullHeight>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'form-release:add'">{{
          t('新增')
        }}</a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              auth: 'form-release:edit',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              auth: 'form-release:delete',
              color: 'error',
              custom: true,
              onClick: handleDel.bind(null, record),
              popConfirm: {
                visible: curID == record.id,
                confirm: handleDelete.bind(null, record),
                deleteMenu: handleDeleteMenu.bind(null, record),
                cancel: () => {
                  curID = '';
                },
              },
            },
          ]"
        />
      </template>
    </BasicTable>

    <ReleaseModal
      v-if="modalClose"
      @register="registerModal"
      @success="handleSuccess"
      @close="handleClose"
    />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';
  import { getFormReleasePage, deleteFormRelease, deleteFormAndMenu } from '/@/api/form/release';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useModal } from '/@/components/Modal';
  import ReleaseModal from './components/ReleaseModal.vue';
  import { usePermission } from '/@/hooks/web/usePermission';

  const { t } = useI18n();
  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入关键字'),
      },
    },
  ];

  const columns: BasicColumn[] = [
    {
      dataIndex: 'formName',
      title: t('表单名称'),
      align: 'left',
      resizable: true,
    },
    {
      dataIndex: 'menuName',
      title: t('菜单名称'),
      align: 'left',
      resizable: true,
    },
    {
      dataIndex: 'remark',
      title: t('备注'),
      align: 'left',
      resizable: true,
    },
  ];
  const { notification } = useMessage();
  const { hasPermission } = usePermission();
  const curID = ref('');
  const modalClose = ref(true);
  defineEmits(['register']);
  const [registerModal, { openModal }] = useModal();
  const [registerTable, { reload }] = useTable({
    title: t('表单发布管理'),
    api: getFormReleasePage,
    rowKey: 'id',
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('form-release:edit')) {
            handleEdit(record);
          }
        },
      };
    },
  });

  function handleCreate() {
    openModal(true, {
      title: t('新增表单发布'),
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      id: record.id,
      isUpdate: true,
      title: t('编辑表单发布'),
    });
  }

  function handleDelete(record: Recordable) {
    deleteFormRelease([record.id]).then((_) => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功'),
      }); //提示消息
    });
  }
  function handleDeleteMenu(record: Recordable) {
    deleteFormAndMenu([record.id]).then((_) => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功'),
      }); //提示消息
    });
  }
  function handleSuccess(isEdit) {
    notification.success({
      message: t('提示'),
      description: isEdit ? t('修改成功！') : t('发布成功！'),
    }); //提示消息
    reload();
  }

  function handleClose() {
    modalClose.value = !modalClose.value;
    setTimeout(() => {
      modalClose.value = !modalClose.value;
    }, 100);
  }
  function handleDel(record: Recordable) {
    curID.value = record.id;
  }
</script>
