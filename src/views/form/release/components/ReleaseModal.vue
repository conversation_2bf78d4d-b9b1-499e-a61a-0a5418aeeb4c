<template>
  <BasicModal @register="registerModal" v-bind="$attrs" wrapClassName="form-modal">
    <template #title>
      <div class="step-form-form">
        <a href="http://www.zilueit.com/" target="_blank">
          <DesignLogo />
        </a>
        <span>•</span>
        <span class="title">{{ title }}</span>
        <a-steps :current="current">
          <a-step :title="t('基础信息')" />
          <a-step :title="t('界面设计')" />
        </a-steps>
        <div class="btn-box">
          <a-button type="primary" @click="handleStepPrev" v-show="current === 1">{{
            t('上一步')
          }}</a-button>
          <a-button type="primary" @click="handleStepNext" v-show="current === 0">{{
            t('下一步')
          }}</a-button>
          <a-button type="primary" @click="handleSave" v-show="current === 1">{{
            t('完成')
          }}</a-button>
          <a-button type="primary" danger @click="handleClose">{{ t('关闭') }}</a-button>
        </div>
      </div>
    </template>
    <div class="step-container">
      <BasicConfigStep ref="basicConfigStepRef" v-show="current === 0" />
      <ViewDesignStep ref="viewDesignStepRef" v-show="current === 1" :isUpdate="isUpdate" />
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, reactive, provide, Ref, toRaw, defineAsyncComponent } from 'vue';
  import BasicConfigStep from './BasicConfigStep.vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { FormReleaseConfig, GeneratorConfig } from '/@/model/generator/generatorConfig';
  import { FieldInfo, TableInfo } from '/@/components/Designer';
  import { getFormTemplate } from '/@/api/form/design';
  import { getDatabaselinkMultiTableColumns } from '/@/api/system/databaselink';
  import { addFormRelease, getFormRelease, updateFormRelease } from '/@/api/form/release';
  import { JavaTypeConvertTsType } from '/@/utils/helper/designHelper';
  import { FormDesignTypeEnum } from '/@/enums/formtypeEnum';
  import { cloneDeep } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  import DesignLogo from '/@/components/ModalPanel/src/DesignLogo.vue';
  import { TreeStructureType } from '/@/enums/treeStructure';
  import { LoadingBox } from '/@/components/ModalPanel/index';
  const ViewDesignStep = defineAsyncComponent({
    loader: () => import('/@/components/CreateCodeStep/src/ViewDesignStep.vue'),
    loadingComponent: LoadingBox,
  });
  const { t } = useI18n();
  const current = ref(0);
  const isFormEdit = ref<boolean>(false);

  const emit = defineEmits(['success', 'register', 'close']);

  const formIdCache = ref(``);

  const basicConfigStepRef = ref();
  const viewDesignStepRef = ref();

  const tableInfo = ref<TableInfo[]>([]);

  const isUpdate = ref<boolean>(false);
  const releaseId = ref<string>('');
  const menuId = ref<string>('');
  const designType = ref<string>('');
  const title = ref<string>('');

  let generatorConfig = reactive<GeneratorConfig>({
    listConfig: {
      listTitle: '',
      isLeftMenu: false,
      queryConfigs: [],
      leftMenuConfig: {
        datasourceType: 'tree',
        listFieldName: undefined,
        apiConfig: {},
        dictionaryItemId: undefined,
        menuName: '',
        parentIcon: '',
        childIcon: '',
        staticData: [],
        treeConfig: {
          id: '',
          name: '',
          type: TreeStructureType.STATIC,
          configTip: '',
          config: [],
          isMultiple: false,
        },
      },
      columnConfigs: [],
      buttonConfigs: [],
      defaultOrder: true,
      listStyle: 'default',
      orderBy: '',
      isPage: true,
    },
    menuConfig: {},
  });

  let formReleaseConfig = reactive<FormReleaseConfig>({
    formId: '',
    listConfig: {},
    menuConfig: generatorConfig.menuConfig,
  });

  provide<GeneratorConfig>('generatorConfig', generatorConfig);
  provide<FormReleaseConfig>('formReleaseConfig', formReleaseConfig);
  provide<Ref<TableInfo[]>>('tableInfo', tableInfo);
  provide<Ref<number>>('current', current); //当前步骤
  provide<Ref<string>>('designType', designType);
  provide<boolean>('isCustomForm', true); //是自定义表单

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    isFormEdit.value = !!data.isUpdate;
    isUpdate.value = !!data.isUpdate;
    releaseId.value = data.id;
    title.value = data.title;
    setModalProps({
      confirmLoading: false,
      canFullscreen: false,
      defaultFullscreen: true,
      draggable: false,
      destroyOnClose: true,
      maskClosable: false,
      footer: null,
      closable: false,
    });
    if (isUpdate.value) {
      const res = await getFormRelease(releaseId.value);
      const configJson = JSON.parse(res.configJson);
      const menuConfig = {
        ...configJson.menuConfig,
        formId: res.formId,
        systemId: configJson.menuConfig.systemId.toString(),
      };
      menuId.value = res.menuId;
      generatorConfig.listConfig = cloneDeep(configJson.listConfig);
      //基础信息赋值
      basicConfigStepRef.value.setFieldsValue(menuConfig);
    }
  });

  async function getReleseInfo() {
    if (formReleaseConfig.formId && formIdCache.value !== formReleaseConfig.formId) {
      formIdCache.value = formReleaseConfig.formId;
      const res = await getFormTemplate(formReleaseConfig.formId);
      const formJson = JSON.parse(res.formJson);
      const selectTableName = formJson.tableConfigs?.map((item) => item.tableName);

      switch (res.formDesignType) {
        case FormDesignTypeEnum.DATA_FIRST:
          designType.value = 'data';
          break;
        case FormDesignTypeEnum.CODE_FIRST:
          designType.value = 'code';
          break;
        case FormDesignTypeEnum.SIMPLE_TEMPLATE:
          designType.value = 'template';
          break;
      }

      generatorConfig.formJson = formJson.formJson;
      generatorConfig.tableStructureConfigs = formJson.tableStructureConfigs;
      if (designType.value !== 'data') return;
      try {
        const result = await getDatabaselinkMultiTableColumns({
          id: formJson.databaseId,
          tableNames: selectTableName.join(','),
        });
        for (const key in result) {
          const columnInfo = result[key];
          //如果已经写入过的表格 不再添加
          if (!tableInfo?.value.find((x) => x.name === key)) {
            const fields = columnInfo.map((field) => {
              const filedInfo: FieldInfo = {
                name: field.column,
                length: field.dataLength,
                type: JavaTypeConvertTsType(field.dataType),
                isPk: field.primaryKey,
                isNullable: field.nullable,
              };
              return filedInfo;
            });

            tableInfo?.value.push({
              name: key,
              isMain: formJson!.tableConfigs!.find((x) => x.tableName === key)?.isMain as boolean,
              fields: fields,
            });
          }
        }
      } catch (error) {}
    }
  }

  function handleClose() {
    emit('close');
  }

  //上一步
  function handleStepPrev() {
    current.value--;
  }
  //下一步
  async function handleStepNext() {
    const isOk = await stepValidate[current.value]();
    if (!isOk) {
      return;
    }
    if (current.value === 0) {
      await getReleseInfo();
    }
    current.value++;
  }

  async function handleSave() {
    const isOk = await stepValidate[1]();
    if (!isOk) {
      return;
    }
    formReleaseConfig.listConfig = generatorConfig.listConfig;
    const params = {
      ...toRaw(formReleaseConfig),
      id: releaseId.value,
      menuId: menuId.value,
    };
    if (isUpdate.value) {
      await updateFormRelease(params);
    } else {
      await addFormRelease(toRaw(formReleaseConfig));
    }
    closeModal();
    emit('success', isFormEdit.value);
    emit('close');
  }

  const stepValidate = {
    //数据表配置 验证
    0: () => basicConfigStepRef.value.validateStep(),
    1: () => viewDesignStepRef.value.validateStep(),
  };
</script>
<style lang="less" scoped>
  .step-form-content {
    padding: 24px;
    background-color: @component-background;
  }

  @keyframes rotation {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
  @keyframes rotationReverse {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(-360deg);
    }
  }

  :deep(.ant-steps-item-process) {
    .ant-steps-item-icon {
      border: 2px solid #fff;
      outline: 2px dashed #1890ff !important;
      line-height: 30px;
      animation: rotation 4s linear infinite;

      .ant-steps-icon {
        display: inline-block;
        animation: rotationReverse 4s linear infinite;
      }
    }
  }

  .step-form-form {
    display: flex;
    align-items: center;
    font-weight: 400;

    a {
      margin-left: -16px;
    }

    span {
      font-size: 16px;
      margin: 0 20px 0 -5px;
      white-space: nowrap;
    }

    .title {
      margin-right: 10%;
    }

    :deep(.ant-steps) {
      width: calc(100% - 900px);
    }

    :deep(.ant-steps-item-container) {
      padding: 2px 0 2px 2px;
    }

    .btn-box {
      position: absolute;
      right: 10px;

      :deep(.ant-btn) {
        margin-right: 10px;
      }
    }
  }

  .step-container {
    height: calc(100% - 50px);
  }

  .step1 {
    padding: 14px;
    box-sizing: border-box;
  }
</style>
