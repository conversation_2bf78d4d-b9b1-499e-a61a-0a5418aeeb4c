<template>
  <ResizePageWrapper :hasLeft="listConfig.isLeftMenu ? true : false">
    <template #resizeLeft v-if="listConfig.isLeftMenu">
      <TreeStructure
        v-if="listConfig.leftMenuConfig?.datasourceType == 'tree'"
        :treeConfig="listConfig.leftMenuConfig?.treeConfig"
        @select="handleSelectTree"
      />
      <BasicTree
        v-else
        :title="listConfig.leftMenuConfig?.menuName"
        toolbar
        search
        :clickRowToExpand="true"
        :treeData="treeData"
        :fieldNames="fieldNames"
        @select="handleSelect"
      >
        <template #title="item">
          <template v-if="item.renderIcon === 'parentIcon'">
            <Icon :icon="listConfig.leftMenuConfig?.parentIcon" />
          </template>
          <template v-if="item.renderIcon === 'childIcon'">
            <Icon :icon="listConfig.leftMenuConfig?.childIcon" />
          </template>
          {{ treeTitle(item) }}
        </template>
      </BasicTree>
    </template>

    <template #resizeRight>
      <div :class="isCardList ? 'card-box' : ''" style="height: 100%">
        <BasicTable
          @register="registerTable"
          ref="tableRef"
          @expand="expandedRowsChange"
          isMenuTable
        >
          <template #customFilterIcon="{ filtered, column }">
            <Icon
              icon="ant-design:filter-filled"
              @click="handleFilterClick(column)"
              :class="filterClass(column, filtered)"
              :style="{ color: filtered ? '#108ee9' : undefined }"
            />
          </template>
          <template #toolbar>
            <template v-for="button in tableButtonConfig" :key="button.code">
              <a-button
                v-if="button.isDefault"
                type="primary"
                v-auth="`${menuCode}:${button.code}`"
                @click="buttonClick(button.code)"
              >
                <template #icon><Icon :icon="button.icon" /></template>
                {{ button.name }}
              </a-button>
              <a-button v-else type="primary" v-auth="`${menuCode}:${button.code}`">
                <template #icon><Icon :icon="button.icon" /></template>
                {{ button.name }}
              </a-button>
            </template>
          </template>
          <template #bodyCell="{ column, record }" v-if="!isCardList">
            <template v-if="column.componentType === 'switch'">
              <a-switch
                v-model:checked="record[column.dataIndex]"
                :unCheckedValue="0"
                :checkedValue="1"
                :disabled="true"
              />
            </template>
            <template v-if="column.componentType === 'picker-color'">
              <ColorPicker v-model:value="record[column.dataIndex]" :disabled="true" />
            </template>
            <template v-else-if="column.componentType === 'labelComponent'">
              <XjrLabelComponentInfo
                :value="record[column.dataIndex]"
                :styleConfig="column.styleConfig"
              />
            </template>
            <template v-if="column.dataIndex === 'action'">
              <div class="btn-box" ref="pushBtnRef" v-if="isShowBtn && pushFormId === record.id">
                <a-button
                  type="primary"
                  v-for="item in pushorderInfo!.setting.formInfo"
                  :key="item.formId"
                  @click="openPushorder(item)"
                >
                  {{ item.formType === 0 ? item.functionName : item.formName }}
                </a-button>
              </div>
              <TableAction :actions="getActions(record)" />
            </template>
            <template v-else-if="column.componentType === 'money-chinese'">
              <span :style="executeListStyle(record, column?.listStyle)">
                {{ moneyChineseData(record[column.dataIndex]) }}
              </span>
            </template>

            <template v-else-if="column.dataIndex && column?.listStyle">
              <span :style="executeListStyle(record, column?.listStyle)">{{
                record[column.dataIndex]
              }}</span>
            </template>
          </template>
          <template #expandedRowRender="{ record }" v-if="hasSubformList">
            <a-table
              v-for="item in subFormList"
              :key="item.key"
              :columns="innerColumns[item.field]"
              :data-source="getInnerDataSource(record.id, item.field)"
              :pagination="false"
              :scroll="{ x: 'max-content' }"
            >
              <template #bodyCell="{ column, record: innerRecord }">
                <template v-if="column.componentType === 'picker-color'">
                  <ColorPicker v-model:value="innerRecord[column.dataIndex]" :disabled="true" />
                </template>
                <template v-if="column.componentType === 'labelComponent'">
                  <XjrLabelComponentInfo
                    :value="innerRecord[column.dataIndex]"
                    :styleConfig="column.styleConfig"
                  />
                </template>
                <template v-else-if="column.componentType === 'money-chinese'">
                  {{ moneyChineseData(innerRecord[column.dataIndex]) }}
                </template>
              </template>
            </a-table>
          </template>
          <template #emptyText v-if="isCardList">
            <a-checkbox-group
              v-model:value="selectedKeys"
              class="data-list"
              v-if="datasource.length"
            >
              <div
                v-for="(data, idx) in datasource"
                :key="data.id"
                :class="[
                  'box-container',
                  selectedKeys.includes(data.id) ? 'selected-box' : 'unselected-box',
                ]"
              >
                <div class="box-title">
                  <div>
                    <a-checkbox :value="data.id" class="!mr-1" v-if="hasListCheckbox" />
                    <span class="text-black">#{{ idx + 1 }}</span>
                  </div>
                  <div>
                    <a-tooltip v-for="(item, index) in getActions(data)" :key="index">
                      <template #title>{{ item.tooltip }} </template>
                      <Icon
                        :icon="item.icon"
                        v-auth="item.auth"
                        :class="['icon-box', item.color === 'error' ? 'icon-delete' : '']"
                        @click="item.onClick()"
                      />
                    </a-tooltip>
                    <div
                      class="btn-box"
                      ref="pushBtnRef"
                      v-if="isShowBtn && pushFormId === data.id"
                    >
                      <a-button
                        type="primary"
                        v-for="info in pushorderInfo!.setting.formInfo"
                        :key="info.formId"
                        @click="openPushorder(info)"
                      >
                        {{ info.formType === 0 ? info.functionName : info.formName }}
                      </a-button>
                    </div>
                  </div>
                </div>
                <div class="flex-box">
                  <div
                    v-for="(key, value, index) in getData(data)"
                    :key="index"
                    :class="`flex-item text-${key.align || 'left'} ${
                      key.aRow ? '!basis-full' : ''
                    }`"
                  >
                    <div :class="['title', key.textBold ? 'font-bold' : '']">{{ value }}</div>
                    <div class="value">{{ key.value }}</div>
                  </div>
                </div>
              </div>
            </a-checkbox-group>
            <div v-else>
              <a-empty />
            </div>
          </template>
        </BasicTable>
      </div>
    </template>

    <FormModal
      v-if="formType === 'modal'"
      @register="registerModal"
      @success="handleSuccess"
      :formType="formType"
      :formWidth="formWidth"
      :form-id="formIdComputedRef"
    />
    <FormModal
      v-else
      @register="registerDrawer"
      @success="handleSuccess"
      :formType="formType"
      :formWidth="formWidth"
      :form-id="formIdComputedRef"
    />
    <ImportModal
      @register="registerImportModal"
      importUrl="/form/execute/import"
      :data="{ releaseId: menuId }"
      @success="handleImportSuccess"
    />
    <LookProcess
      v-if="visibleLookProcessRef"
      :taskId="taskIdRef"
      :processId="processIdRef"
      @close="visibleLookProcessRef = false"
      :visible="visibleLookProcessRef"
    />
    <LaunchProcess
      v-if="visibleLaunchProcessRef"
      :schemaId="schemaIdRef"
      :form-data="formDataRef"
      :form-id="formIdComputedRef"
      :rowKeyData="rowKeyData"
      :draftsId="draftsId"
      @close="handleCloseLaunch"
    />
    <ApprovalProcess
      v-if="visibleApproveProcessRef"
      :taskId="taskIdRef"
      :processId="processIdRef"
      :schemaId="schemaIdRef"
      @close="handleCloseApproval"
      :visible="visibleApproveProcessRef"
    />
    <SetRuleUserModal @register="registerRuleUserModal" @success="handleSuccess" />
    <PrintPreview
      v-if="visiblePrintPreview"
      :id="printData.id"
      :request-params-configs="printData.requestParamsConfigs"
      :request-body-configs="printData.requestBodyConfigs"
      :request-header-configs="printData.requestHeaderConfigs"
      @close="visiblePrintPreview = false"
    />
    <ExportModal
      v-if="visibleExport"
      :columns="columns"
      @close="visibleExport = false"
      @success="handleExportSuccess"
    />
    <BasicModal
      v-if="visibleFlowRecordModal"
      :visible="visibleFlowRecordModal"
      :title="t('查看流转记录')"
      :paddingRight="15"
      :showOkBtn="false"
      :width="1200"
      @visible-change="
        (v) => {
          visibleFlowRecordModal = v;
        }
      "
      :bodyStyle="{ minHeight: '400px !important' }"
    >
      <FlowRecord :processId="processIdRef" />
    </BasicModal>
    <PushOrderModal @register="registerPushModal" />
  </ResizePageWrapper>
</template>
<script lang="ts" setup>
  import {
    BasicColumn,
    BasicTable,
    FormProps,
    FormSchema,
    TableAction,
    useTable,
    ActionItem,
  } from '/@/components/Table';
  import { useDrawer } from '/@/components/Drawer';
  // import { PageWrapper } from '/@/components/Page';
  import { ResizePageWrapper } from '/@/components/Page';
  import FormModal from './components/FormModal.vue';
  import { ColorPicker } from '/@/components/ColorPicker';
  import { XjrLabelComponentInfo } from '/@/components/LabelComponent';
  import { ImportModal } from '/@/components/Import';
  import SetRuleUserModal from '/@/components/Designer/src/components/generateComponent/SetRuleUserModal.vue';
  import Icon from '/@/components/Icon/index';
  import LookProcess from '/@/views/workflow/task/components/LookProcess.vue';
  import LaunchProcess from '/@/views/workflow/task/components/LaunchProcess.vue';
  import ApprovalProcess from '/@/views/workflow/task/components/ApprovalProcess.vue';
  import { downloadByData } from '/@/utils/file/download';
  import { onMounted, ref, unref, computed, createVNode, provide, reactive, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { getFormTemplate } from '/@/api/form/design';
  import { getFormRelease } from '/@/api/form/release';
  import { useModal, BasicModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { FormReleaseConfig, GeneratorConfig } from '/@/model/generator/generatorConfig';
  import { ColumnConfig, ListConfig, QueryConfig } from '/@/model/generator/listConfig';
  import { FormEventColumnConfig } from '/@/model/generator/formEventConfig';
  import { buildOption } from '/@/utils/helper/designHelper';
  import { whetherNeedToTransform, findSchema } from '/@/utils/helper/generatorHelper';
  import { ComponentOptionModel } from '/@/model/generator/codeGenerator';
  import {
    deleteFormExecute,
    getFormExecutePage,
    getFormExecuteWorkflow,
    exportForm,
    setDataAuth,
    getFormExecute,
    getSubData,
  } from '/@/api/form/execute';
  import { handleSearchForm } from '/@/utils/helper/exportHelper';
  import { useTabs } from '/@/hooks/web/useTabs';
  import { executeListStyle } from '/@/hooks/web/useListStyle';
  import { useTitle } from '@vueuse/core';
  import { useGlobSetting } from '/@/hooks/setting';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { TreeItem, BasicTree } from '/@/components/Tree';
  import { getDicDetailList } from '/@/api/system/dic';
  import {
    apiConfigFunc,
    generateTableJson,
    isValidJSON,
    moneyChineseData,
  } from '/@/utils/event/design';
  import { Modal } from 'ant-design-vue';
  import { CloseCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import printJS from 'print-js';
  import { getPrintConfigInfo } from '/@/api/system/generator/print';
  import PrintPreview from '/@/views/generator/print/Preview.vue';
  import { PrintButton } from '/@/enums/printEnum';
  import { InputParamItem } from '/@/components/ApiConfig/src/interface';
  import { getDraftInfo } from '/@/api/workflow/process';
  import FlowRecord from '/@/views/workflow/task/components/flow/FlowRecord.vue';
  import { TreeStructure } from '/@/components/TreeStructure';
  import PushOrderModal from './components/PushOrderModal.vue';
  import ExportModal from './components/ExportModal.vue';

  interface SearchDate {
    fieldName: string;
    format: string;
  }
  const { setTitle } = useTabs();
  const { title } = useGlobSetting();
  const { currentRoute } = useRouter();
  const { path } = unref(currentRoute);
  const printMenuId = computed(() => currentRoute.value.meta.menuId as string);
  const { filterColumnAuth, filterButtonAuth, hasPermission } = usePermission();

  let columns: BasicColumn[] = [], //列表配置
    searchFormSchema: FormSchema[] = [], //搜索框配置
    searchDate: SearchDate[] = [], //搜索栏时间组件配置
    formProps: FormProps = {}, //表单配置
    listConfig = ref<ListConfig>({
      isLeftMenu: false,
      isPage: false,
      queryConfigs: [],
      columnConfigs: [],
      buttonConfigs: [],
      listTitle: '',
      isAdvancedQuery: false,
      querySelectOption: '',
      objectId: '',
    }), //列表配置
    formEventConfig = ref<FormEventColumnConfig>(), //表单事件配置
    treeData = ref<any[]>([]), //树结构数据
    selectId = ref<string>(''), // 左侧菜单所选;
    formType = ref<string>(''), //弹窗类型：modal、drawer
    formWidth = ref<number>(800), // 弹窗、抽屉宽度
    menuId = ref<string>(''),
    pkField = ref<string>(''), //主表主键
    tableRef = ref(),
    innerColumns = ref({}),
    innerDataSource = ref({}),
    subFormList = ref<any[]>([]);

  // 模板打印 入参参数
  let printData: {
    id: string;
    requestParamsConfigs: Array<InputParamItem>;
    requestHeaderConfigs: Array<InputParamItem>;
    requestBodyConfigs: Array<InputParamItem>;
  } = reactive({
    id: '',
    requestParamsConfigs: [],
    requestHeaderConfigs: [],
    requestBodyConfigs: [],
  });
  //展示在列表内的按钮
  const actionButtons = ref<string[]>([
    'view',
    'edit',
    'copyData',
    'delete',
    'startwork',
    'flowRecord',
    'pushorder',
  ]);
  const { notification } = useMessage();
  const { t } = useI18n();
  defineEmits(['register']);
  const selectedKeys = ref<string[]>([]);
  const selectedRowsData = ref<any[]>([]);

  const isDataAuth = ref<boolean>(false);

  const listTitle = ref<string>('');
  const pageParamsInfo = ref<any>({});
  const isShowFilter = ref(false);
  const showColumnIndex = ref<string>('');
  const clickColumnIndex = ref<string>('');
  const visibleFlowRecordModal = ref(false);
  const menuName = ref('');
  const menuCode = ref('');
  const pushorderInfo = ref();
  const datasource = ref<any>([]);

  provide<boolean>('isCustomForm', true);
  const paths = path.split('/');
  menuId.value = paths.length > 0 ? paths[paths.length - 1] : '';
  const filterColumns = ref<BasicColumn[]>([]);
  const buttonConfigs = computed(() => {
    if (!listConfig.value.buttonConfigs) return;
    listConfig.value.buttonConfigs.map((item, index) => {
      if (item.code === 'delete') {
        //删除按钮放在最后
        listConfig.value.buttonConfigs.splice(index, 1);
        listConfig.value.buttonConfigs.push(item);
        return;
      }
      if (item.code === 'pushorder') {
        //推单按钮放在最前面
        listConfig.value.buttonConfigs.splice(index, 1);
        listConfig.value.buttonConfigs.unshift(item);
        return;
      }
    });
    return filterButtonAuth(listConfig.value.buttonConfigs.filter((x) => x.isUse));
  });

  const tableButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => !actionButtons.value.includes(x.code));
  });

  const actionButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => actionButtons.value.includes(x.code));
  });

  const filterClass = (column, filtered) => {
    return (isShowFilter.value && column.dataIndex === showColumnIndex.value) ||
      column.dataIndex === clickColumnIndex.value ||
      filtered
      ? 'show'
      : 'hide';
  };

  const visibleLookProcessRef = ref(false);
  const processIdRef = ref('');

  const visibleLaunchProcessRef = ref(false);
  const schemaIdRef = ref('');
  const formDataRef = ref();
  const rowKeyData = ref();
  const draftsId = ref();
  const pushFormId = ref();
  const pushBtnRef = ref();
  const pushbtnleft = ref();
  const isShowBtn = ref(false);
  const hasSubformList = ref(false);
  const hasListCheckbox = ref(false);

  const visibleApproveProcessRef = ref(false);
  const taskIdRef = ref('');

  const formIdComputedRef = ref('');

  const visiblePrintPreview = ref(false);
  const visibleExport = ref(false);

  const btnEvent = {
    view: handleView,
    add: handleAdd,
    edit: handleEdit,
    delete: handleDelete,
    batchdelete: handleBatchdelete,
    batchSetUserId: handleBatchSetUserId,
    import: handleImport,
    export: handleExport,
    print: handlePrint,
    copyData: handleCopyData,
    pushorder: handlePushorder,
  };

  const fieldNames = computed(() => {
    return {
      key: listConfig.value.leftMenuConfig?.datasourceType === 'static' ? 'key' : 'value',
      title:
        listConfig.value.leftMenuConfig?.datasourceType === 'dic'
          ? 'name'
          : listConfig.value.leftMenuConfig?.datasourceType === 'api'
          ? 'label'
          : listConfig.value.leftMenuConfig?.showFieldName || 'title',
    };
  });

  const isCardList = computed(() => {
    return listConfig.value?.listStyle === 'card';
  });

  const treeTitle = (item) => {
    switch (listConfig.value.leftMenuConfig?.datasourceType) {
      case 'dic':
        return item.name;
      case 'api':
        return item.label;
      default:
        return item[listConfig.value.leftMenuConfig?.showFieldName || 'title'];
    }
  };
  const [registerModal, { openModal }] = useModal();
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerRuleUserModal, { openModal: openRuleUserModal }] = useModal();
  const [registerPushModal, { openModal: openPushModal }] = useModal();

  const [registerTable, { reload, setProps, setSelectedRowKeys, getRawDataSource, setColumns }] =
    useTable({
      title: '',
      columns,
      formConfig: {
        rowProps: {
          gutter: 16,
        },
        schemas: searchFormSchema,
        showResetButton: false,
      },
      beforeFetch: (pageParams) => {
        //发送请求默认新增  左边树结构所选机构id
        const newParams = {
          releaseId: menuId.value,
          params: pageParams,
          order: pageParams.order,
          field: pageParams.field,
        };
        pageParamsInfo.value = {
          releaseId: menuId.value,
          params: pageParams,
        };
        //如果有左侧菜单 需要加上左侧树 所选项  作为参数
        if (listConfig.value.isLeftMenu) {
          pageParams[listConfig.value.leftMenuConfig?.listFieldName as string] = selectId.value;
        }
        newParams['headers'] = {
          FormId: formIdComputedRef.value,
          PK: pkField.value,
        };
        return newParams;
      },
      afterFetch: (res) => {
        filterColumns.value.map((column: any) => {
          if (column.onFilter) {
            const info = res.map((item) => item[column.dataIndex!]);
            column.filters = [...new Set(info)].map((item) => {
              return {
                text: item,
                value: item,
              };
            });
            column.customHeaderCell = () => {
              return {
                onmouseenter: () => {
                  isShowFilter.value = true;
                  showColumnIndex.value = column.dataIndex;
                  clickColumnIndex.value = '';
                },
                onmouseleave: () => {
                  isShowFilter.value = false;
                  showColumnIndex.value = '';
                },
              };
            };
          }
        });
        selectedKeys.value = [];
        selectedRowsData.value = [];
        setSelectedRowKeys([]);
        setColumns(filterColumns.value);
        tableRef.value.setToolBarWidth();
        if (isCardList.value) {
          getDataList(res);
          return [];
        } else {
          setColumns(filterColumns.value);
        }
        return res;
      },
      useSearchForm: true,
      showTableSetting: true,
      striped: false,
      isFilterByDataSoure: true,
      actionColumn: {
        width: 160,
        title: t('操作'),
        dataIndex: 'action',
        slots: { customRender: 'action' },
      },
      customRow,
      tableSetting: {
        size: false,
        setting: !isCardList.value,
      },
      isAdvancedQuery: false,
      querySelectOption: '',
      objectId: '',
    });

  function handleFilterClick(column) {
    clickColumnIndex.value = column.dataIndex;
  }

  function customRow(record: Recordable) {
    return {
      onClick: () => {
        let selectedRowKeys = [...selectedKeys.value];
        if (selectedRowKeys.indexOf(record[pkField.value]) >= 0) {
          let index = selectedRowKeys.indexOf(record[pkField.value]);
          selectedRowKeys.splice(index, 1);
        } else {
          selectedRowKeys.push(record[pkField.value]);
        }
        selectedKeys.value = selectedRowKeys;
        setSelectedRowKeys(selectedKeys.value);
      },
      ondblclick: () => {
        if (record.isCanEdit && hasPermission(`${menuCode.value}:edit`)) {
          handleEdit(record);
        }
      },
    };
  }
  function buttonClick(code) {
    if (code.includes(PrintButton.CODE)) {
      printTemplate(code);
    } else {
      btnEvent[code]();
    }
  }
  // 模板打印
  async function printTemplate(code) {
    if (!selectedKeys.value.length) {
      notification.warning({
        message: t('提示'),
        description: t('请选择数据'),
      });
      return;
    }
    if (selectedKeys.value.length > 1) {
      notification.warning({
        message: t('提示'),
        description: t('只能选择一条数据进行操作'),
      });
      return;
    }
    let id = selectedKeys.value[0];
    try {
      const record = await getFormExecute({ releaseId: menuId.value, id: id });
      let res = await getPrintConfigInfo(code, printMenuId.value);
      if (res.enabledMark == null) {
        notification.warning({
          message: t('提示'),
          description: t('当前功能未绑定打印模板，请绑定后再进行模板打印。'),
        });
        return;
      }
      if (res.enabledMark == 0) {
        notification.warning({
          message: t('提示'),
          description: t('找不到打印模板，请联系管理员。'),
        });
        return;
      }
      printData.id = res.schemaId;
      if (res.id && res.apiConfig) {
        let json = JSON.parse(res.apiConfig);
        if (json.requestParamsConfigs && json.requestParamsConfigs.length > 0) {
          printData.requestParamsConfigs = json.requestParamsConfigs.map((ele) => {
            if (ele.config && record[ele.config] != undefined) {
              ele.value = record[ele.config];
            }
            return ele;
          });
        }
        if (json.requestHeaderConfigs && json.requestHeaderConfigs.length > 0) {
          printData.requestHeaderConfigs = json.requestHeaderConfigs.map((ele) => {
            if (ele.config && record[ele.config] != undefined) {
              ele.value = record[ele.config];
            }
            return ele;
          });
        }
        if (json.requestBodyConfigs && json.requestBodyConfigs.length > 0) {
          printData.requestBodyConfigs = json.requestBodyConfigs.map((ele) => {
            if (ele.config && record[ele.config] != undefined) {
              ele.value = record[ele.config];
            }
            return ele;
          });
        }
        visiblePrintPreview.value = true;
      } else {
        notification.warning({
          message: t('提示'),
          description: t('当前功能未绑定打印模板，请绑定后再进行模板打印。'),
        });
      }
    } catch (error) {}
  }
  function onSelectChange(rowKeys: [], selectedRows) {
    selectedKeys.value = rowKeys;
    selectedRowsData.value = selectedRows;

    setSelectedRowKeys(selectedKeys.value);
  }

  function getDataList(data) {
    datasource.value = [];
    if (data?.length) {
      data.forEach((item) => {
        let listData = {
          id: item.id,
          workflowData: item.workflowData,
        };
        filterColumns.value.forEach((col) => {
          for (let key in item) {
            if (col.dataIndex === key) {
              let value = item[key];
              if (col.componentType === 'money-chinese') {
                value = moneyChineseData(value);
              }
              listData[col.title as string] = {
                value,
                align: col.align,
                aRow: col.aRow,
                textBold: col.textBold,
              };
            }
          }
        });

        datasource.value.push(listData);
      });
    }
  }
  function getData(data) {
    let dataObj: any = {};
    for (let key in data) {
      if (key !== 'id' && key !== 'workflowData' && key !== 'isCanEdit') {
        dataObj[key] = data[key];
      }
    }
    return dataObj;
  }

  function handleAdd() {
    formProps = buildOption(formJson.value.formJson, false);

    const info = {
      isUpdate: false,
      releaseId: menuId.value,
      pkField: pkField.value,
      formEventConfig,
      formProps,
      menuName,
    };
    formType.value === 'modal' ? openModal(true, info) : openDrawer(true, info);
  }

  async function handleEdit(record: Recordable) {
    const info = {
      id: record[pkField.value],
      releaseId: menuId.value,
      pkField: pkField.value,
      isUpdate: true,
      formEventConfig,
      formProps,
      menuName,
    };
    formType.value === 'modal' ? openModal(true, info) : openDrawer(true, info);
  }

  function handleView(record: Recordable) {
    const info = {
      id: record[pkField.value],
      releaseId: menuId.value,
      pkField: pkField.value,
      isView: true,
      formEventConfig,
      formProps,
      menuName,
    };
    formType.value === 'modal' ? openModal(true, info) : openDrawer(true, info);
  }

  function handleCopyData(record: Recordable) {
    const info = {
      id: record[pkField.value],
      releaseId: menuId.value,
      pkField: pkField.value,
      isCopy: true,
      formEventConfig,
      formProps,
      menuName,
    };
    formType.value === 'modal' ? openModal(true, info) : openDrawer(true, info);
  }

  async function handleDelete(record: Recordable) {
    deleteList([record[pkField.value]]);
  }

  async function handlePrint() {
    const dataSource = Array.isArray(getRawDataSource())
      ? getRawDataSource()
      : getRawDataSource().list;
    const json = generateTableJson(filterColumns.value, dataSource);
    const properties = filterColumns.value.map((item) => item.title);
    printJS({
      printable: json,
      properties: properties,
      type: 'json',
    });
  }

  async function handlePushorder(record) {
    if (pushorderInfo.value?.setting.type === 'form') {
      pushFormId.value = record.id;
      isShowBtn.value = !isShowBtn.value;
      if (isShowBtn.value) {
        nextTick(() => {
          pushbtnleft.value = `-${pushBtnRef.value?.offsetWidth}px`;
        });
      }
    } else {
      Modal.confirm({
        title: '提示信息',
        icon: createVNode(ExclamationCircleOutlined),
        content: '确定需要推单吗？',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          try {
            apiConfigFunc(pushorderInfo.value?.setting.apiConfig, false, record).then(() => {
              notification.success({
                message: 'Tip',
                description: '执行成功',
              });
            });
          } catch (error) {}
        },
      });
    }
  }

  async function openPushorder(record) {
    isShowBtn.value = false;
    const rowInfo = await getFormExecute({ releaseId: menuId.value, id: pushFormId.value });
    openPushModal(true, {
      info: record,
      rowInfo,
      isCustom: true,
    });
  }

  function handleBatchdelete() {
    if (!selectedKeys.value.length) {
      notification.warning({
        message: t('提示'),
        description: t('请选择需要删除的数据'),
      });
      return;
    }
    if (isCardList.value) {
      selectedRowsData.value = datasource.value.filter((x) => selectedKeys.value.includes(x.id));
    }
    //与工作流相关的数据不能进行批量删除
    const cantDelete = selectedRowsData.value.filter((x) => {
      return (x.workflowData?.enabled && x.workflowData?.status) || !!x.workflowData?.processId;
    });
    if (cantDelete.length) {
      notification.warning({
        message: 'Tip',
        description: t('含有不能删除的数据'),
      });
      return;
    }
    deleteList(selectedKeys.value);
  }

  function deleteList(ids) {
    Modal.confirm({
      title: t('提示信息'),
      icon: createVNode(ExclamationCircleOutlined),
      content: t('是否确认删除？'),
      okText: t('确认'),
      cancelText: t('取消'),
      onOk() {
        deleteFormExecute({ releaseId: menuId.value, ids }).then((_) => {
          handleSuccess();
          notification.success({
            message: t('提示'),
            description: t('删除成功！'),
          });
        });
      },
      onCancel() {},
    });
  }

  function handleBatchSetUserId() {
    if (!isDataAuth.value) {
      Modal.confirm({
        title: t('操作失败'),
        icon: createVNode(CloseCircleOutlined, { style: 'color: #ed6f6f' }),
        content: createVNode(
          'div',
          { style: 'color: #999' },
          t('当前功能未配置数据权限，请配置后再进行操作。'),
        ),
        centered: true,
        okText: t('确定'),
        cancelText: t('取消'),
        onOk() {},
        onCancel() {},
      });
      return;
    }
    if (!selectedKeys.value.length) {
      notification.warning({
        message: t('提示'),
        description: t('请选择需要设置权限的数据'),
      });
      return;
    }
    openRuleUserModal(true, {
      rowKey: pkField.value,
      columns: filterColumns.value,
      dataSource: selectedRowsData.value,
      setDataAuthApi: setDataAuth, //设置权限的接口地址
      params: { releaseId: menuId.value },
      isCustomForm: true,
    });
  }

  function handleExport() {
    visibleExport.value = true;
  }

  async function handleExportSuccess(cols) {
    const fileName = listTitle.value;
    const res = await exportForm({ ...pageParamsInfo.value, columns: cols });
    visibleExport.value = false;

    downloadByData(
      res.data,
      `${fileName}.xlsx`,
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
  }

  function handleImport() {
    openImportModal(true, {
      title: t('快速导入'),
      downLoadUrl: '/form/execute/export',
      type: 'POST',
    });
  }

  function handleImportSuccess() {
    reload();
  }

  function handleSuccess() {
    selectedKeys.value = [];
    selectedRowsData.value = [];
    setSelectedRowKeys([]);
    reload();
  }

  function handleViewWorkflow(record: Recordable) {
    if (record.workflowData) {
      visibleLookProcessRef.value = true;
      processIdRef.value = record.workflowData.processId;
    }
  }
  function handleFlowRecord(record: Recordable) {
    if (record.workflowData) {
      visibleFlowRecordModal.value = true;
      processIdRef.value = record.workflowData?.processId;
    }
  }
  async function handleLaunchProcess(record: Recordable) {
    if (record.workflowData) {
      if (record.workflowData.draftId) {
        let res = await getDraftInfo(record.workflowData.draftId);
        let data = isValidJSON(res.formData);
        if (data) {
          for (let key in data) {
            if (key.includes(formIdComputedRef.value)) {
              formDataRef.value = data[key];
            }
          }
        }
        draftsId.value = record.workflowData.draftId;
      } else {
        const result = await getFormExecuteWorkflow({
          formId: formIdComputedRef.value,
          id: record[pkField.value],
        });
        formDataRef.value = result;
      }
      rowKeyData.value = record[pkField.value];
      visibleLaunchProcessRef.value = true;
      schemaIdRef.value = record.workflowData.schemaId;
    }
  }
  function handleApproveProcess(record: Recordable) {
    visibleApproveProcessRef.value = true;
    schemaIdRef.value = record.workflowData.schemaId;
    processIdRef.value = record.workflowData.processId;
    taskIdRef.value = record.workflowData.taskIds[0];
  }
  function handleCloseLaunch() {
    visibleLaunchProcessRef.value = false;
    reload();
  }
  function handleCloseApproval() {
    visibleApproveProcessRef.value = false;
    reload();
  }

  const formJson = ref();

  onMounted(async () => {
    //获取发布数据
    const releaseResult = await getFormRelease(menuId.value as string);
    const configJson = JSON.parse(releaseResult.configJson) as FormReleaseConfig;
    formIdComputedRef.value = releaseResult.formId;
    menuName.value = configJson.menuConfig.name;
    menuCode.value = configJson.menuConfig.code;
    //设置Tab标题
    setTitle(menuName.value);
    //设置浏览器标题
    useTitle(` ${menuName.value} - ${title} `);
    //设置是否需要左侧菜单
    listConfig.value = configJson.listConfig!;
    pushorderInfo.value = buttonConfigs.value?.find((x) => x.code === 'pushorder');

    listTitle.value = listConfig.value.listTitle || menuName.value;
    let { queryConfigs, columnConfigs } = configJson.listConfig!;

    //如果设置了左侧菜单  需要请求菜单数据
    if (listConfig.value.isLeftMenu) {
      fetchLeftData();
    }
    //获取表单模板数据
    const templateResult = await getFormTemplate(releaseResult.formId);

    formJson.value = JSON.parse(templateResult.formJson) as GeneratorConfig;
    isDataAuth.value = formJson.value.isDataAuth || false;

    const mainTable = formJson.value.tableConfigs?.find((item) => item.isMain);
    pkField.value = mainTable?.pkField || 'id';

    //构建表单Props
    formProps = buildOption(formJson.value.formJson, false);
    formType.value = formJson.value.formJson.config.formType!;
    formWidth.value = formJson.value.formJson.config.formWidth || 800;
    formEventConfig.value = formJson.value.formEventConfig;
    //查询配置转换为schema
    if (listConfig.value.isAdvancedQuery) {
      let tempArr = JSON.parse(listConfig.value.querySelectOption);
      queryConfigs = tempArr.map((tempEle) => {
        return {
          fieldName: tempEle.value,
          isDate: ['time', 'date'].includes(tempEle.type) || !!tempEle.isDate,
        };
      });
    }
    [searchFormSchema, searchDate] = ConvertQueryConfigToFormSchema(
      queryConfigs,
      formProps.schemas!,
      formJson.value.formJson.list,
    );
    hasSubformList.value = formProps.schemas!.some((item) => {
      return item.type === 'form' && (item.componentProps as FormSchema)!.isListView;
    });
    //列表配置 转换为 BasicColumn
    columns = ConvertColumnConfigToBasicColumn(columnConfigs);
    filterColumns.value = filterColumnAuth(columns);
    hasListCheckbox.value = tableButtonConfig.value!.some(
      (x) =>
        ['batchdelete', 'batchSetUserId'].includes(x.code) || x.code.includes(PrintButton.CODE),
    );
    subFormList.value = formProps.schemas!.filter(
      (x) => x.type === 'form' && (x.componentProps as FormSchema)!.isListView,
    );
    subFormList.value?.forEach((x) => {
      innerColumns.value[x.field] = (x.componentProps! as FormSchema)
        .viewList!.filter((sub) => sub.checked)
        .map((sub) => {
          return {
            title: sub.label,
            dataIndex: sub.field,
            componentType: sub.componentType,
          };
        });
    });
    //是否展示多选按钮
    if (hasListCheckbox.value && !isCardList.value) {
      setProps({
        rowSelection: {
          onChange: onSelectChange,
        },
      });
    }
    //重新设定table组件props
    setProps({
      api: getFormExecutePage,
      title: listTitle.value,
      rowKey: pkField.value,
      columns: filterColumns.value,
      formConfig: {
        labelWidth: 100,
        schemas: searchFormSchema,
        fieldMapToTime: searchDate.map((date) => {
          return [
            date.fieldName!,
            [date.fieldName + 'Start', date.fieldName + 'End'],
            date.format,
            true,
          ];
          // [['fieldTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
        }),
        showResetButton: false,
      },
      isAdvancedQuery: listConfig.value.isAdvancedQuery,
      querySelectOption: JSON.stringify(searchFormSchema),
      objectId: menuId.value, ////系统表单formId,自定义表单releaseId的id值
    });
    if (isCardList.value) {
      setProps({
        pagination: {
          pageSizeOptions: ['9', '12', '15', '18'],
          pageSize: 9,
        },
        columns: [],
        actionColumn: undefined,
      });
    }
    reload();
  });

  function getActions(record: Recordable): ActionItem[] {
    record.isCanEdit = false;
    const hasStartWorkButton = buttonConfigs.value?.some((x) => x.code === 'startwork');
    let actionsList: ActionItem[] = [];
    let editAndDelBtn: ActionItem[] = [];
    let pushorderBtn: ActionItem[] = [];
    let hasFlowRecord = false;
    actionButtonConfig.value?.map((button) => {
      if (button.code === 'view') {
        actionsList.push({
          icon: button?.icon,
          auth: `${menuCode.value}:${button.code}`,
          tooltip: button?.name,
          onClick: handleView.bind(null, record),
        });
      }
      if (['edit', 'copyData', 'delete'].includes(button.code)) {
        editAndDelBtn.push({
          icon: button?.icon,
          auth: `${menuCode.value}:${button.code}`,
          tooltip: button?.name,
          color: button.code === 'delete' ? 'error' : undefined,
          onClick: btnEvent[button.code].bind(null, record),
        });
      }
      if (button.code === 'pushorder') {
        pushorderBtn.push({
          icon: button?.icon,
          auth: `${menuCode.value}:${button.code}`,
          tooltip: button?.name,
          onClick: btnEvent[button.code].bind(null, record),
        });
      }
      if (button.code === 'flowRecord') hasFlowRecord = true;
    });
    if (record.workflowData?.enabled) {
      if (!hasStartWorkButton) {
        record.isCanEdit = !record.workflowData.status;
        if (!record.workflowData.status) {
          actionsList.unshift(...pushorderBtn);
          actionsList.concat(editAndDelBtn);
        }
        return actionsList;
      }
      //与工作流有关联的表单
      if (record.workflowData.status) {
        //如果是本人需要审批的数据 就会有taskIds  所以需要修改绑定事件
        const act: ActionItem = {};
        if (record.workflowData.taskIds) {
          act.tooltip = t('查看流程(待审批)');
          act.icon = 'daishenpi|svg';
          act.onClick = handleApproveProcess.bind(null, record);
        } else {
          act.tooltip =
            t('查看流程') +
            (record.workflowData.status === 'ACTIVE' ? t('(审批中)') : t('(已完成)'));
          act.icon =
            record.workflowData.status === 'ACTIVE' ? 'jinshenpi|svg' : 'shenpiwancheng|svg';
          act.onClick = handleViewWorkflow.bind(null, record);
        }
        actionsList.unshift(act);
        if (hasFlowRecord) {
          actionsList.splice(1, 0, {
            tooltip: '查看流转记录',
            icon: 'liuzhuanxinxi|svg',
            onClick: handleFlowRecord.bind(null, record),
          });
        }
      } else {
        actionsList.unshift({
          icon: 'faqishenpi|svg',
          tooltip: record.workflowData.draftId ? t('编辑草稿') : t('发起审批'),
          onClick: handleLaunchProcess.bind(null, record),
        });
        actionsList.unshift(...pushorderBtn);
        record.isCanEdit = true;
        actionsList = actionsList.concat(editAndDelBtn);
      }
    } else {
      if (!record.workflowData?.processId) {
        //与工作流没有关联的表单并且在当前页面新增的数据 如选择编辑、删除按钮则加上
        actionsList.unshift(...pushorderBtn);
        record.isCanEdit = true;
        actionsList = actionsList.concat(editAndDelBtn);
      }
    }
    return actionsList;
  }
  function handleSelectTree(treeConditions) {
    reload({ searchInfo: { treeConditions: treeConditions } });
  }
  function handleSelect(selectIds) {
    selectId.value = selectIds[0];
    reload({
      searchInfo: { [listConfig.value.leftMenuConfig?.listFieldName as string]: selectId.value },
    });
  }

  async function fetchLeftData() {
    //如果是数据字典
    if (listConfig.value.leftMenuConfig?.datasourceType === 'dic') {
      treeData.value = (await getDicDetailList({
        itemId: listConfig.value.leftMenuConfig.dictionaryItemId,
      })) as unknown as TreeItem[];
    } else if (listConfig.value.leftMenuConfig?.datasourceType === 'static') {
      //如果是静态数据
      treeData.value = listConfig.value.leftMenuConfig.staticData!;
    } else if (listConfig.value.leftMenuConfig?.datasourceType === 'api') {
      //如果是api
      treeData.value = (await apiConfigFunc(
        listConfig.value.leftMenuConfig?.apiConfig,
        false,
      )) as unknown as TreeItem[];
    }
    addRenderIcon(treeData.value);
  }

  function addRenderIcon(data) {
    data.map((item) => {
      if (item.children?.length) addRenderIcon(item.children);
      return (item.renderIcon = item.children?.length ? 'parentIcon' : 'childIcon');
    });
  }

  function getInnerDataSource(id, field) {
    if (innerDataSource.value[id]) {
      return innerDataSource.value[id][field];
    }
    return [];
  }

  async function expandedRowsChange(isOpen, record) {
    if (!isOpen) return;
    const tableInfo = await getSubData({
      dataId: record.id,
      formId: formIdComputedRef.value,
    });
    innerDataSource.value[record.id] = tableInfo;
  }

  /**
   * QueryConfig 转换 为搜索栏 FormSchema
   * 根据查询字段 获取到 当前字段查询组件的类型
   * 普通类型：input
   * 远程组件：ApiSelect
   * 时间字段：RangePicker
   */
  function ConvertQueryConfigToFormSchema(
    queryConfigs: QueryConfig[],
    schemas: FormSchema[],
    list: ComponentOptionModel[],
  ): [FormSchema[], SearchDate[]] {
    const searchDate: SearchDate[] = [];
    const searchFormSchema = queryConfigs.map((config) => {
      if (!!config.isDate) {
        searchDate.push({
          fieldName: config.fieldName,
          format: config.format!,
        });
      }
      const schema = findSchema(schemas, config.fieldName);
      const [isNeedTrans, option] = whetherNeedToTransform(config, list);
      return handleSearchForm(option, schema, config, isNeedTrans, false) as FormSchema;
    });
    return [searchFormSchema, searchDate];
  }

  /**
   *
   * columnConfig转 BasicColumn
   * @param columnConfigs
   */
  function ConvertColumnConfigToBasicColumn(columnConfigs: ColumnConfig[]): BasicColumn[] {
    return columnConfigs.map((config) => {
      const column: BasicColumn = {
        dataIndex: config.columnName,
        title: config.label,
        componentType: config.componentType,
        listStyle: config.componentProps?.listStyle,
        styleConfig: config.componentProps?.styleConfig,
        sorter: true,
        resizable: true,
      };
      if (config.alignType) column.align = config.alignType as any;
      if (config.isTotal) column.total = true;
      if (!config.autoWidth && config.columnWidth) column.width = config.columnWidth;
      if (config.componentProps?.datasourceType === 'staticData') {
        column.customRender = ({ record }) => {
          const staticOptions = config.componentProps?.staticOptions;
          if (config.componentType === 'checkbox') {
            const valArr = record[config.columnName]?.split(',');
            return staticOptions
              .filter((x) => valArr.includes(x.value))
              ?.map((x) => x.label)
              .toString();
          }
          return staticOptions.filter((x) => x.value === record[config.columnName])[0]?.label;
        };
      }
      if (config.textBold) column.textBold = true;
      if (config.aRow) column.aRow = true;
      if (config.isFilter) {
        column.onFilter = (value, record) => {
          return record[config.columnName] === value;
        };
      }
      return column;
    });
  }
</script>
<style lang="less" scoped>
  :deep(.ant-table-selection-col) {
    width: 50px;
  }

  .show {
    display: flex;
  }

  .hide {
    display: none !important;
  }

  .btn-box {
    display: grid;
    position: absolute;
    top: 0;
    left: v-bind(pushbtnleft);
    z-index: 999;

    .ant-btn {
      margin-top: 5px;
      padding: 0 10px;
    }
  }

  :deep(.ant-table-expanded-row) {
    .ant-table {
      margin: 0 !important;
      height: 100%;
    }

    .ant-table-wrapper {
      height: 200px;
    }
  }

  .card-box {
    :deep(.ant-table-content) {
      max-height: none !important;
    }

    :deep(.vben-basic-table) {
      .ant-table .ant-table-body {
        height: 100% !important;

        tr td {
          padding: 0 !important;
          border-bottom: none;
        }
      }

      .ant-table-container {
        height: calc(100% - 60px);
      }
    }

    :deep(.ant-table-header) {
      display: none;
    }

    :deep(.ant-table-expanded-row-fixed) {
      margin: 0 !important;
      padding: 0;
    }
  }

  .selected-box {
    border: 1px solid #5e95ff;
    background: linear-gradient(to bottom, #88b1ff, #fff 80%);

    .box-title {
      border-bottom: 1px solid #98b7f1;

      .icon-box {
        background: #5e95ff;
        color: #fff;
      }

      .icon-delete {
        background: #ed6f6f;
      }
    }
  }

  .unselected-box {
    background: linear-gradient(to bottom, #e7efff, #fff 80%);

    .box-title {
      border-bottom: 1px solid #e3edff;

      .icon-box {
        background: #fff;
        color: #5e95ff;
      }

      .icon-delete {
        background: #fff;
        color: #ed6f6f;
      }
    }
  }

  .data-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    padding-left: 10px;

    .box-container {
      margin: 0 10px 10px 0;
      flex: 0 0 calc((100% - 20px) / 3);
      height: 270px;
      overflow: auto;
      padding-bottom: 10px;

      &:nth-child(3n) {
        margin-right: 0;
      }

      .box-title {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 5px;

        .icon-box {
          padding: 4px;
          border-radius: 50%;
          margin-right: 5px;
          cursor: pointer;
        }
      }

      .flex-box {
        display: flex;
        flex-wrap: wrap;
        height: calc(100% - 40px);
        overflow: auto;
        padding: 10px 10px 0;

        .flex-item {
          flex: 0 0 50%;
          height: 50px;
          overflow: hidden;

          > div {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .title {
            color: #67676c;
          }

          .value {
            font-weight: bold;
            color: #4a4b4d;
          }
        }
      }
    }
  }
</style>
