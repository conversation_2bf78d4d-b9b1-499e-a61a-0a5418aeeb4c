<template>
  <BasicModal @register="registerCategoryModal" v-bind="$attrs">
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <a-button type="link" v-if="record.activityFlag === 1" @click="handlePreview(record.id)"
          >{{ t('预览') }}
        </a-button>
        <div v-else class="flex justify-around">
          <a-button type="link" @click="handlePreview(record.id)">{{ t('预览') }} </a-button>
          <a-popconfirm
            :title="t('确认要将当前表单更新到此历史版本吗？')"
            :ok-text="t('确定')"
            :cancel-text="t('取消')"
            @confirm="handleVersion(record)"
          >
            <a-button type="link">{{ t('更新到此版本') }} </a-button>
          </a-popconfirm>
        </div>
      </template>
    </BasicTable>
  </BasicModal>
  <PreviewModal @register="registerPreviewModal" />
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import PreviewModal from './PreviewModal.vue';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { getFormHistoryInfo, setFormHistoryVersion } from '/@/api/form/design';
  import { getFormHistory } from '/@/api/form/design';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const columns: BasicColumn[] = [
    {
      dataIndex: 'activityFlag',
      title: t('版本'),
      customRender: ({ record }) =>
        `${record.activityFlag === 1 ? t('当前版本') : t('非当前版本')}`, //1-当前版本 0-非当前版本
    },
    {
      dataIndex: 'createUserName',
      title: t('创建人'),
    },
    {
      dataIndex: 'createDate',
      title: t('创建时间'),
    },
  ];

  const formId = ref<string>('');
  const { notification } = useMessage();

  const [registerPreviewModal, { openModal }] = useModal();

  const [registerCategoryModal, { setModalProps }] = useModalInner(async (data) => {
    formId.value = data.formId;
    setModalProps({
      confirmLoading: false,
      draggable: false,
      title: data.title,
      showOkBtn: false,
      showCancelBtn: false,
      destroyOnClose: true,
      width: 800,
    });
  });
  const [registerTable, { reload }] = useTable({
    api: getFormHistory,
    beforeFetch: () => {
      return formId.value;
    },
    rowKey: 'id',
    columns,
    showTableSetting: false,
    bordered: true,
    maxHeight: 200,
    actionColumn: {
      width: 180,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  const handlePreview = async (id) => {
    if (!id) {
      notification.warning({
        message: t('提示'),
        description: t('请先选择要预览的数据项'),
      }); //提示消息
      return;
    }
    const templateJson = await getFormHistoryInfo(id);
    openModal(true, { title: t('预览'), formJson: templateJson.formJson });
  };

  const handleVersion = (data) => {
    setFormHistoryVersion({ formId: data.formId, id: data.id }).then((res) => {
      if (res) {
        notification.success({
          message: t('提示'),
          description: t('更新版本成功'),
        });
        reload();
      } else {
        notification.error({
          message: t('提示'),
          description: t('更新版本失败'),
        });
      }
    });
  };
</script>
<style lang="less" scoped>
  :deep(.vben-basic-table-header__toolbar) {
    justify-content: space-between;
  }

  :deep(.current-row) td {
    background: #e6f2ff !important;
  }
</style>
