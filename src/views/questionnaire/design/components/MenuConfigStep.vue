<template>
  <div class="step1-form">
    <BasicForm @register="register" />
  </div>
</template>
<script lang="ts" setup>
  import { inject } from 'vue';
  import { getSystemAllList } from '/@/api/system/subSystem';
  import { getMenuTree } from '/@/api/system/menu';
  import { FormSchema, useForm, BasicForm } from '/@/components/Form';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { QuestionnaireConfig } from '/@/model/generator/generatorConfig';

  const { notification } = useMessage();

  const { t } = useI18n();
  defineEmits(['register']);

  const generatorConfig = inject<QuestionnaireConfig>('generatorConfig');

  const formSchema: FormSchema[] = [
    {
      field: 'code',
      label: t('菜单编号'),
      required: true,
      component: 'Input',
      colProps: { span: 12 },
    },
    {
      field: 'name',
      label: t('菜单名称'),
      required: true,
      component: 'Input',
      colProps: { span: 12 },
    },
    {
      field: 'systemId',
      label: '主子系统',
      component: 'ApiSelect',
      required: true,
      componentProps: ({ formModel, formActionType }) => {
        return {
          api: getSystemAllList,
          labelField: 'name',
          valueField: 'id',
          getPopupContainer: () => document.body,
          showSearch: true,
          onChange: async (val) => {
            formModel.parentId = '';
            const { updateSchema } = formActionType;
            updateSchema({
              field: 'parentId',
              componentProps: {
                api: getMenuTree,
                params: { systemId: val },
                fieldNames: {
                  key: 'id',
                  label: 'title',
                  value: 'id',
                },
              },
            });
          },
        };
      },
      colProps: { span: 12 },
    },
    {
      field: 'parentId',
      label: t('上级菜单'),
      component: 'ApiTreeSelect',
      componentProps: ({ formModel }) => {
        return {
          api: getMenuTree,
          params: { systemId: formModel.systemId },
          fieldNames: {
            key: 'id',
            label: 'title',
            value: 'id',
          },
          getPopupContainer: () => document.body,
        };
      },
      colProps: { span: 12 },
    },
    {
      field: 'sortCode',
      label: t('排序'),
      required: true,
      component: 'InputNumber',
      componentProps: {
        style: { width: '100%' },
      },
      colProps: { span: 12 },
    },
    {
      field: 'icon',
      label: t('图标'),
      required: true,
      component: 'IconPicker',
      colProps: { span: 12 },
    },

    {
      field: 'remark',
      label: t('备注'),
      component: 'InputTextArea',
      colProps: { span: 24 },
    },
  ];
  const [register, { validate, setFieldsValue }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
  });

  //验证当前步骤的数据
  const validateStep = async (): Promise<boolean> => {
    try {
      const formData = await validate();
      if (formData.code.length > 10) {
        notification.error({
          message: t('提示'),
          description: '编号长度不能超过10个字符',
        }); //提示消息
        return false;
      }
      generatorConfig!.menuConfig.code = formData.code;
      generatorConfig!.menuConfig.name = formData.name;
      generatorConfig!.menuConfig.parentId = formData.parentId;
      generatorConfig!.menuConfig.remark = formData.remark;
      generatorConfig!.menuConfig.sortCode = formData.sortCode;
      generatorConfig!.menuConfig.icon = formData.icon;
      generatorConfig!.menuConfig.systemId = formData.systemId;
    } catch (error) {
      return false;
    }
    return true;
  };

  defineExpose({ validateStep, setFieldsValue });
</script>
