<template>
  <a-spin :spinning="spinning" :delay="delayTime">
    <PageLayout v-if="data.visible">
      <template #right>
        <iframe ref="iframe" :src="iframeUrl" class="iframe-box" :style="iframeStyle"></iframe>
      </template>
    </PageLayout>
    <EmptyBox
      v-if="data.emptyBoxVisible"
      class="iframe-empty-box"
      msg="找不到当前打印模板，请联系管理员。"
    />
  </a-spin>
</template>

<script setup lang="ts">
  import { PageLayout } from '/@/components/ModalPanel';
  import { computed, nextTick, onMounted, reactive, ref, unref } from 'vue';
  import { iframeUrl } from './config';
  import { useRouter } from 'vue-router';
  import { getDesignInfo } from '/@/api/system/generator/print';
  import { getPrintData } from './hooks/usePrintData';
  import { EmptyBox } from '/@/components/ModalPanel/index';
  import { getAppEnvConfig } from '/@/utils/env';
  const { currentRoute } = useRouter();
  const spinning = ref<boolean>(true);
  const delayTime = 500;
  let iframe = ref();
  let height = ref(600);
  const data = reactive({
    visible: false,
    emptyBoxVisible: false,
    json: '',
    apiConfig: {
      id: '',
      name: '',
      method: '',
      requestParamsConfigs: [], //Query Params
      requestHeaderConfigs: [], //Header
      requestBodyConfigs: [], //Body
    },
  });

  const iframeStyle = computed(() => {
    return 'height:' + height.value + 'px';
  });
  onMounted(async () => {
    try {
      const { path } = unref(currentRoute);
      data.emptyBoxVisible = false;
      let paths = path.split('/');
      let id = paths.length > 0 ? paths[paths.length - 1] : '';
      if (id) {
        try {
          let res = await getDesignInfo(id);
          if (res && res.enabledMark) {
            data.visible = true;
            data.json = res.content;
            data.apiConfig = JSON.parse(res.apiConfig);
            await init();
          } else {
            data.emptyBoxVisible = true;
          }
        } catch (error) {}
      }
      spinning.value = false;
    } catch (error) {
      spinning.value = false;
    }
  });
  const init = async () => {
    scrollHeight();
    await nextTick();
    iframe.value.onload = function () {
      sendMessage();
    };
  };
  async function sendMessage() {
    try {
      let printData = await getPrintData(data.apiConfig);
      let info = {
        type: 'preview',
        fields: printData.apiFields,
        subTabulation: printData.subTabulation,
        printData: printData.data,
        json: data.json,
      };
      var wn = iframe.value.contentWindow;
      wn.postMessage(info, getAppEnvConfig().VITE_GLOB_PRINT_BASE_URL);
    } catch (error) {}
  }
  function scrollHeight() {
    const conTable = document.getElementsByClassName('full');
    if (conTable && conTable.length > 0 && conTable[0]) {
      height.value = conTable[0].clientHeight - 50;
    }
  }
</script>

<style scoped>
  .iframe-box {
    width: 100%;
    height: 100%;
    box-shadow: 0 0 6px 4px #eee;
    max-width: 800px;
    margin: 0 auto;
    border-radius: 10px;
  }

  .iframe-empty-box {
    max-width: 800px;
    margin: 0 auto;
    margin-top: 20%;
  }
</style>
