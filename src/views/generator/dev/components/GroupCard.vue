<template>
  <div class="box" id="myDiv">
    <div
      v-for="(item, index) in growCardList"
      :key="item.title"
      class="item"
      :class="getClass(index)"
    >
      <img :src="item.image" class="getStyle" />
      <p class="card-name">{{ item.name }}</p>
      <p class="card-content">{{ item.content }}</p>
      <a-button type="primary" @click="item.func">{{ item.btnName }}</a-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
  defineProps({
    growCardList: {
      type: [Array] as PropType<any[]>,
      required: true, //表格配置json
    },
  });

  function getClass(index) {
    let strClass = '';
    if (index == 2 || index == 3) {
      strClass += ' margin-top-20 ';
    }
    if (index == 0 || index == 2) {
      strClass += 'margin-right-20';
    }
    return strClass;
  }
</script>
<style lang="less" scoped>
  :deep(.ant-card:last-child) {
    margin-right: 0 !important;
  }

  .card-name {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    margin-bottom: 10px;
  }

  .card-content {
    color: #999;
    margin-bottom: 20px;
  }

  :deep(.ant-btn) {
    width: 150px;
    height: 45px;
    background: #5e95ff;
    box-shadow: 0 4px 18px 0 rgb(94 149 255 / 50%);
    border-radius: 0 30px 30px;
    border-color: transparent;
  }

  .box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    padding: 8px;

    .item {
      flex: 1;
      flex-basis: calc(50% - 16px);
      height: calc(50vh - 80px);
      margin: 0;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border: 1px solid #f0f0f0;
    }

    .margin-top-20 {
      margin-top: 20px;
    }

    .margin-right-20 {
      margin-right: 20px;
    }
  }

  .getStyle {
    width: calc(50vw - 432px);
  }
</style>
