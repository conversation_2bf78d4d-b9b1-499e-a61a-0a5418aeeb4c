<template>
  <a-spin :spinning="spinning" :delay="delayTime">
    <DesktopHome v-if="data.visible" :list="data.list" :isMenu="true" />
  </a-spin>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, unref } from 'vue';
  import DesktopHome from './components/DesktopHome.vue';
  import { useRouter } from 'vue-router';
  import { getDesktopInfo } from '/@/api/desktop';
  import { replaceJsonParseInfinity } from '/@/utils/json';
  const { currentRoute } = useRouter();
  const spinning = ref<boolean>(true);
  const delayTime = 500;
  const data = reactive({ visible: false, list: [] });
  onMounted(async () => {
    try {
      const { path } = unref(currentRoute);
      let paths = path.split('/');
      let id = paths.length > 0 ? paths[paths.length - 1] : '';

      if (id) {
        let { jsonContent } = await getDesktopInfo(id);
        if (jsonContent) {
          data.list = JSON.parse(jsonContent, replaceJsonParseInfinity);
          data.visible = true;
        }
      }
      spinning.value = false;
    } catch (error) {
      spinning.value = false;
    }
  });
</script>

<style scoped></style>
