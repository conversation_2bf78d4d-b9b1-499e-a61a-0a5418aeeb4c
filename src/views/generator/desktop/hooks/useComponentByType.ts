import { Component, defineAsyncComponent } from 'vue';
import { DesktopComponent } from '/@/enums/desktop';
export default function () {
  const Chart = defineAsyncComponent({
    loader: () => import('./../components/designer/chart/Chart.vue'),
  });
  const ChartBar = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/ChartBar.vue'),
  });
  const MixLineBar = defineAsyncComponent({
    loader: () => import('./../components/designer/chart/MixLineBar.vue'),
  });
  const Default = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Default.vue'),
  });
  const Dashboard = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Dashboard.vue'),
  });
  const Information = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Information.vue'),
  });
  const MyTask = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/MyTask.vue'),
  });
  const TodoList = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/TodoList.vue'),
  });
  const Modules = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Modules.vue'),
  });
  const DefaultProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Default.vue'),
  });
  const ChartlineProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Chartline.vue'),
  });
  const ChartBarProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/ChartBar.vue'),
  });
  const GaugeProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Gauge.vue'),
  });
  const FunnelProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Funnel.vue'),
  });
  const RadarProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Radar.vue'),
  });
  const CategoryStackProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/CategoryStack.vue'),
  });
  const DashboardProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Dashboard.vue'),
  });
  const InformationProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Information.vue'),
  });
  const PieProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Pie.vue'),
  });
  const TodoListProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/TodoList.vue'),
  });
  const ModulesProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Modules.vue'),
  });
  const ButtonDemo = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Button.vue'),
  });
  const ButtonProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Button.vue'),
  });
  const Table = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Table.vue'),
  });
  const TableProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Table.vue'),
  });
  const TabsTable = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/TabsTable.vue'),
  });
  const TabsTableProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/TabsTable.vue'),
  });
  const MyImage = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Image.vue'),
  });
  const MyImageProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Image.vue'),
  });
  const MyMap = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Map.vue'),
  });
  const MyMapProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Map.vue'),
  });
  const componentByType = new Map<DesktopComponent, { legend: Component; properties: Component }>([
    [DesktopComponent.DASHBOARD, { legend: Dashboard, properties: DashboardProperties }],
    [DesktopComponent.INFORMATION, { legend: Information, properties: InformationProperties }],
    [DesktopComponent.CHARTLINE, { legend: MixLineBar, properties: ChartlineProperties }],
    [DesktopComponent.PIE, { legend: Chart, properties: PieProperties }],
    [DesktopComponent.RADAR, { legend: Chart, properties: RadarProperties }],
    [DesktopComponent.GAUGE, { legend: Chart, properties: GaugeProperties }],
    [DesktopComponent.FUNNEL, { legend: Chart, properties: FunnelProperties }],
    [DesktopComponent.CATEGORY_STACK, { legend: Chart, properties: CategoryStackProperties }],
    [DesktopComponent.CHARTBAR, { legend: ChartBar, properties: ChartBarProperties }],
    [DesktopComponent.MYTASK, { legend: MyTask, properties: DefaultProperties }],
    [DesktopComponent.TODOLIST, { legend: TodoList, properties: TodoListProperties }],
    [DesktopComponent.MODULES, { legend: Modules, properties: ModulesProperties }],
    [DesktopComponent.DEFAULT, { legend: Default, properties: DefaultProperties }],
    [DesktopComponent.BUTTON, { legend: ButtonDemo, properties: ButtonProperties }],
    [DesktopComponent.TABLE, { legend: Table, properties: TableProperties }],
    [DesktopComponent.TABSTABLE, { legend: TabsTable, properties: TabsTableProperties }],
    [DesktopComponent.IMAGE, { legend: MyImage, properties: MyImageProperties }],
    [DesktopComponent.MAP, { legend: MyMap, properties: MyMapProperties }],
  ]);
  return {
    componentByType,
  };
}
