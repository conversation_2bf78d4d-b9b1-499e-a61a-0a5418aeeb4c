import { Component, defineAsyncComponent } from 'vue';
import { DesktopComponent } from '/@/enums/desktop';
export default function () {
  const Chart = defineAsyncComponent({
    loader: () => import('./../components/designer/chart/Chart.vue'),
  });
  const ChartBar = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/ChartBar.vue'),
  });
  const MixLineBar = defineAsyncComponent({
    loader: () => import('./../components/designer/chart/MixLineBar.vue'),
  });
  const Default = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Default.vue'),
  });
  const Dashboard = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Dashboard.vue'),
  });
  const Information = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Information.vue'),
  });
  const MyTask = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/MyTask.vue'),
  });
  const TodoList = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/TodoList.vue'),
  });
  const Modules = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Modules.vue'),
  });
  const ButtonDemo = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Button.vue'),
  });
  const Table = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Table.vue'),
  });
  const TabsTable = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/TabsTable.vue'),
  });
  const MyImage = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Image.vue'),
  });
  const MyMap = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Map.vue'),
  });
  const componentByType = new Map<DesktopComponent, Component>([
    [DesktopComponent.DASHBOARD, Dashboard],
    [DesktopComponent.INFORMATION, Information],
    [DesktopComponent.CHARTLINE, MixLineBar],
    [DesktopComponent.PIE, Chart],
    [DesktopComponent.RADAR, Chart],
    [DesktopComponent.GAUGE, Chart],
    [DesktopComponent.FUNNEL, Chart],
    [DesktopComponent.CATEGORY_STACK, Chart],
    [DesktopComponent.CHARTBAR, ChartBar],
    [DesktopComponent.MYTASK, MyTask],
    [DesktopComponent.TODOLIST, TodoList],
    [DesktopComponent.MODULES, Modules],
    [DesktopComponent.DEFAULT, Default],
    [DesktopComponent.BUTTON, ButtonDemo],
    [DesktopComponent.TABLE, Table],
    [DesktopComponent.TABSTABLE, TabsTable],
    [DesktopComponent.IMAGE, MyImage],
    [DesktopComponent.MAP, MyMap],
  ]);
  return {
    componentByType,
  };
}
