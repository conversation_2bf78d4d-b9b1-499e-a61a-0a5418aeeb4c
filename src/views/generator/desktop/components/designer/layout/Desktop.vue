<template>
  <div class="design-containers">
    <div class="head">
      <slot name="head"></slot>
    </div>
    <div class="layout">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style lang="less" scoped>
  .head {
    height: 56px;
    width: 100%;
    box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
  }

  .design-containers {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: #f5f5f5;
  }

  [data-theme='dark'] .flow-containers {
    background-color: #151515;
  }

  .common-button {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .clean-icon {
    background-color: @clear-color;
    border-color: @clear-color;
  }

  .layout {
    position: relative;
    width: 100%;
    height: 100%;
    top: 56px;
  }

  :deep(.ant-steps) {
    width: 280px;
  }

  .design-button {
    display: flex;
    margin: 0 20px;
  }
</style>
