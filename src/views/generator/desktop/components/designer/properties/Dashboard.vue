<template>
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('基础配置')">
        <a-form-item :label="t('标题')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.title" />
        </a-form-item>
        <a-form-item :label="t('风格类型')" :colon="false" labelAlign="left">
          <a-select
            v-model:value="data.info.config.showType"
            style="width: 100%"
            :options="showTypeOptions"
          />
        </a-form-item>
        <!-- 描述只有在风格1 风格3中需要填写 -->
        <a-form-item
          v-if="
            data.info.config.showType == DashboardShowType.ONE ||
            data.info.config.showType == DashboardShowType.THREE
          "
          :label="t('描述')"
          :colon="false"
          labelAlign="left"
        >
          <a-input v-model:value="data.info.config.des" />
        </a-form-item>
        <!-- 标题1 标题2 只有在风格3中需要填写 -->
        <a-form-item
          v-if="data.info.config.showType == DashboardShowType.THREE"
          :label="t('标题1')"
          :colon="false"
          labelAlign="left"
        >
          <a-input v-model:value="data.info.config.title1" />
        </a-form-item>
        <a-form-item
          v-if="data.info.config.showType == DashboardShowType.THREE"
          :label="t('标题2')"
          :colon="false"
          labelAlign="left"
        >
          <a-input v-model:value="data.info.config.title2" />
        </a-form-item>

        <a-form-item :label="t('数据颜色')" :colon="false" labelAlign="left">
          <SelectColor v-model:value="data.info.config.numColor" />
        </a-form-item>
        <!-- 图片类型 图标选择 图标颜色 只有在风格1中需要填写 -->
        <a-form-item
          :label="t('图片类型')"
          v-if="data.info.config.showType == DashboardShowType.ONE"
          :colon="false"
          labelAlign="left"
        >
          <a-select
            v-model:value="data.info.config.imgType"
            style="width: 100%"
            :options="imgTypeOptions"
          />
        </a-form-item>
        <a-form-item
          :label="t('图标选择')"
          v-if="
            data.info.config.showType == DashboardShowType.ONE &&
            data.info.config.imgType == ImgType.ICON
          "
          :colon="false"
          labelAlign="left"
        >
          <IconPicker v-model:value="data.info.config.imgIcon" />
        </a-form-item>
        <a-form-item
          :label="t('图标颜色')"
          v-if="
            data.info.config.showType == DashboardShowType.ONE &&
            data.info.config.imgType == ImgType.ICON
          "
          :colon="false"
          labelAlign="left"
        >
          <SelectColor v-model:value="data.info.config.imgIconColor" />
        </a-form-item>
        <!-- 图片选择 只有在风格2 或者风格1且图片类型为图片时候 中需要填写 -->
        <a-form-item
          :label="t('图片选择')"
          v-if="
            data.info.config.showType == DashboardShowType.TWO ||
            (data.info.config.showType == DashboardShowType.ONE &&
              data.info.config.imgType == ImgType.PICTURE)
          "
          :colon="false"
          labelAlign="left"
        >
          <div class="img-box">
            <a-upload
              name="file"
              accept="image/*"
              :headers="data.headers"
              :max-count="1"
              :showUploadList="false"
              :action="data.action"
              list-type="picture-card"
              @change="photoChange"
            >
              <img v-if="data.info.config.pictureUrl" :src="data.info.config.pictureUrl" />
              <div v-else>{{ t('点击上传') }}</div>
            </a-upload>
          </div>
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('数据配置')">
        <a-form-item :label="t('数据视图')" :colon="false" labelAlign="left">
          <ApiSelect
            v-model:value="data.info.config.apiConfig"
            :exampleStr="exampleStr"
            @save="saveApiConfig"
          />
        </a-form-item>
        <a-form-item :label="t('主指标')" :colon="false" labelAlign="left">
          <a-select
            v-model:value="data.info.config.mainIndex"
            style="width: 100%"
            @change="changeMainIndexValue"
            allowClear
          >
            <a-select-option
              v-for="(item, index) in data.info.config.apiColumns"
              :key="index"
              :value="item"
              >{{ item }}</a-select-option
            >
          </a-select>
        </a-form-item>
        <!-- 主指标2 只有在风格3 中需要填写 -->
        <a-form-item
          v-if="data.info.config.showType == DashboardShowType.THREE"
          :label="t('主指标2')"
          :colon="false"
          labelAlign="left"
        >
          <a-select
            v-model:value="data.info.config.mainIndex2"
            style="width: 100%"
            @change="changeMainIndex2Value"
            allowClear
          >
            <a-select-option
              v-for="(item, index) in data.info.config.apiColumns"
              :key="index"
              :value="item"
              >{{ item }}</a-select-option
            >
          </a-select>
        </a-form-item>
        <!-- 副指标 只有在风格1  风格3 中需要填写 -->
        <a-form-item
          v-if="
            data.info.config.showType == DashboardShowType.ONE ||
            data.info.config.showType == DashboardShowType.THREE
          "
          :label="t('副指标')"
          :colon="false"
          labelAlign="left"
        >
          <a-select
            allowClear
            v-model:value="data.info.config.secondaryIndex"
            style="width: 100%"
            @change="changeSecondaryIndexValue"
          >
            <a-select-option
              v-for="(item, index) in data.info.config.apiColumns"
              :key="index"
              :value="item"
              >{{ item }}</a-select-option
            >
          </a-select>
        </a-form-item>
        <!-- 标记 只有在风格1  风格3 中需要填写 -->
        <a-form-item
          v-if="
            data.info.config.showType == DashboardShowType.ONE ||
            data.info.config.showType == DashboardShowType.THREE
          "
          :label="t('标记')"
          :colon="false"
          labelAlign="left"
        >
          <a-select
            allowClear
            v-model:value="data.info.config.flagKey"
            style="width: 100%"
            @change="changeFlagIsUp"
          >
            <a-select-option
              v-for="(item, index) in data.info.config.apiColumns"
              :key="index"
              :value="item"
              >{{ item }}</a-select-option
            >
          </a-select>
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="3" :header="t('交互设置')">
        <a-form-item :label="t('跳转功能')" :colon="false" labelAlign="left">
          <JumpMenu
            v-model:value="data.info.config.jumpId"
            @path="
              (path) => {
                data.info.config.path = path;
              }
            "
          />
        </a-form-item>
        <!-- <a-form-item>
          <a-button>设置跳转条件</a-button>
        </a-form-item> -->
      </a-collapse-panel>
      <a-collapse-panel key="4" :header="t('大小定位')">
        <Location v-model:info="data.info" />
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import ApiSelect from './ApiSelect.vue';
  import SelectColor from './SelectColor.vue';
  import { IconPicker } from '/@/components/Icon';
  import { JumpMenu } from '/@/components/MenuSelect';
  import { dashboardInfo } from '../config/info';
  import { uploadSrc } from '/@/api/sys/upload';
  import { DashboardShowType, ImgType } from '/@/enums/desktop';
  import { DashboardInfo } from '/@/model/desktop/designer';
  import Location from './collapse/Location.vue';
  import { isNumber } from 'lodash-es';
  import { message } from 'ant-design-vue';
  import type { UploadChangeParam } from 'ant-design-vue';
  import { getAppEnvConfig } from '/@/utils/env';
  import { getToken } from '/@/utils/auth';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: DashboardInfo;
    }>(),
    {
      info: () => {
        return dashboardInfo;
      },
    },
  );

  const data: {
    show: boolean;
    info: DashboardInfo;
    action: string;
    headers: { Authorization: string };
  } = reactive({
    show: false,
    info: dashboardInfo,
    action: getAppEnvConfig().VITE_GLOB_API_URL + uploadSrc,
    headers: { Authorization: `Bearer ${getToken()}` },
  });

  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
      if (val.config.flagKey) changeFlagIsUp(val.config.flagKey);
      if (val.config.mainIndex) changeMainIndexValue(val.config.mainIndex);
      if (val.config.secondaryIndex) changeSecondaryIndexValue(val.config.secondaryIndex);
    },
    {
      deep: true,
      immediate: true,
    },
  );
  const activeKey = ref(['1', '2']);
  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  function saveApiConfig() {
    resetApiData();
    resetDisplay();
  }
  function resetApiData() {
    data.info.config.apiData = {};
    data.info.config.apiColumns = [];
    data.info.config.mainIndex = '';
    data.info.config.mainIndexValue = 0;
    data.info.config.mainIndex2 = '';
    data.info.config.mainIndex2Value = 0;
    data.info.config.secondaryIndex = '';
    data.info.config.secondaryIndexValue = 0;
    data.info.config.flagKey = '';
    data.info.config.flagIsUp = -1;
  }
  function changeMainIndexValue(key) {
    data.info.config.mainIndexValue = isNumber(data.info.config.apiData[key])
      ? data.info.config.apiData[key]
      : 0;
  }
  function changeMainIndex2Value(key) {
    data.info.config.mainIndex2Value = isNumber(data.info.config.apiData[key])
      ? data.info.config.apiData[key]
      : 0;
  }
  function changeSecondaryIndexValue(key) {
    data.info.config.secondaryIndexValue = isNumber(data.info.config.apiData[key])
      ? data.info.config.apiData[key]
      : 0;
  }
  function changeFlagIsUp(key) {
    data.info.config.flagIsUp = data.info.config.apiData[key];
  }

  function photoChange(info: UploadChangeParam) {
    if (info.file.status !== 'uploading') {
    }
    if (info.file.status === 'done') {
      if (info.file && info.file.response && info.file.response.code == 0) {
        message.success(t(`{name}上传成功！`, { name: info.file.name }));
        data.info.config.pictureUrl = info.file.response.data.fileUrl;
      } else {
        message.error(t('上传照片失败'));
      }
    } else if (info.file.status === 'error') {
      message.error(t(`{name}上传失败.`, { name: info.file.name }));
    }
  }
  function resetDisplay() {
    if (data.info.config.renderKey >= 0) {
      data.info.config.renderKey++;
    }
  }
  const exampleStr = ` {
      code: 0,
      msg: 'success',
      data: {
        filed1: 0,
        filed2: 0,
        filed3: 0,
      },
    }`;
  const showTypeOptions = [
    {
      label: t('风格一'),
      value: DashboardShowType.ONE,
    },
    {
      label: t('风格二'),
      value: DashboardShowType.TWO,
    },
    {
      label: t('风格三'),
      value: DashboardShowType.THREE,
    },
  ];
  const imgTypeOptions = [
    {
      label: t('图标'),
      value: ImgType.ICON,
    },
    {
      label: t('图片'),
      value: ImgType.PICTURE,
    },
    {
      label: t('无'),
      value: ImgType.DEFAULT,
    },
  ];
</script>

<style lang="less" scoped>
  .img-box {
    overflow: auto;
  }
</style>
