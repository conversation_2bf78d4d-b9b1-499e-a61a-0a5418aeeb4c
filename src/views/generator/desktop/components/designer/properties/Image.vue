<template>
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('基础配置')">
        <a-form-item :label="t('标题')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.title" />
        </a-form-item>
        <a-form-item :label="t('显示标题')" :colon="false" labelAlign="left">
          <a-switch v-model:checked="data.info.config.showTitle" />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('图片配置')">
        <a-form-item :label="t('默认图片')">
          <a-input v-model:value="imageUrl" :disabled="true" style="margin-bottom: 10px" />
          <a-upload
            action=""
            :beforeUpload="submitUpload"
            accept=""
            :max-count="1"
            :show-upload-list="false"
          >
            <a-button type="primary">
              {{ t('点击上传') }}
            </a-button>
          </a-upload>
        </a-form-item>
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import { imageInfo } from '../config/info';
  import { ImageInfo } from '/@/model/desktop/designer';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getFileList } from '/@/api/system/file';
  import { uploadMultiApi } from '/@/api/sys/upload';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: ImageInfo;
    }>(),
    {
      info: () => {
        return imageInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    show: boolean;
    info: ImageInfo;
  } = reactive({
    show: false,
    info: imageInfo,
  });
  const activeKey = ref(['1', '2']);
  const imageUrl = ref('');
  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  const submitUpload = (file) => {
    let folderId = data.info.config.folderId;
    uploadMultiApi(
      {
        name: 'file',
        file: [file],
      },
      folderId,
    ).then((res) => {
      data.info.config.folderId = res[0].folderId;
      getImage();
    });
  };
  async function getImage() {
    if (data.info.config.folderId) {
      let fileList = await getFileList({ folderId: data.info.config.folderId });
      if (fileList.length) {
        imageUrl.value = fileList[0].fileUrl;
        data.info.config.folderId = fileList[0].folderId;
      }
    } else {
      imageUrl.value = '';
    }
  }
</script>

<style lang="less" scoped></style>
