<template>
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('基础配置')">
        <a-form-item
          :label-col="{ style: { width: '40px' } }"
          :label="t('标题')"
          :colon="false"
          labelAlign="left"
        >
          <a-input v-model:value="data.info.config.title" />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('按钮配置')">
        <a-form-item :colon="false" labelAlign="left">
          <div
            class="card-box"
            v-if="
              data.info.config && data.info.config.buttons && data.info.config.buttons.length > 0
            "
          >
            <div
              class="card-item"
              v-for="(item, topIndex) in data.info.config.buttons"
              :key="topIndex"
            >
              <a-form-item
                :label="t('按钮名称')"
                :label-col="{ style: { width: '68px' } }"
                :colon="false"
                labelAlign="left"
              >
                <a-input v-model:value="item.name" />
              </a-form-item>
              <a-form-item
                :label="t('按钮类型')"
                :label-col="{ style: { width: '68px' } }"
                :colon="false"
                labelAlign="left"
              >
                <a-select
                  v-model:value="item.type"
                  style="width: 100%"
                  @change="changeButton(topIndex)"
                >
                  <a-select-option :value="ButtonType.CREATE">{{
                    t('新增数据按钮')
                  }}</a-select-option>
                  <a-select-option :value="ButtonType.SEARCH">{{
                    t('查询数据按钮')
                  }}</a-select-option>
                  <a-select-option :value="ButtonType.EXECUTE">{{ t('执行按钮') }}</a-select-option>
                </a-select>
              </a-form-item>
              <template v-if="item.type == ButtonType.CREATE">
                <a-form-item
                  :label="t('关联表单')"
                  :label-col="{ style: { width: '68px' } }"
                  :colon="false"
                  labelAlign="left"
                >
                  <SettingForm v-model:formId="item.formId" v-model:formName="item.formName">
                    <a-input v-model:value="item.formName" />
                  </SettingForm>
                </a-form-item>
              </template>
              <template v-if="item.type == ButtonType.SEARCH">
                <a-form-item
                  :label="t('接口配置')"
                  :label-col="{ style: { width: '68px' } }"
                  :colon="false"
                  labelAlign="left"
                >
                  <SelectApi v-model:value="item.apiConfig" :exampleStr="exampleStr" />
                </a-form-item>
                <a-form-item
                  :label="t('表头配置')"
                  :label-col="{ style: { width: '68px' } }"
                  :colon="false"
                  labelAlign="left"
                >
                  <ColumnSelect :apiConfig="item.apiConfig" v-model:api-column="item.apiColumn" />
                </a-form-item>
              </template>
              <template v-if="item.type == ButtonType.EXECUTE">
                <a-form-item
                  :label="t('执行事件')"
                  :label-col="{ style: { width: '68px' } }"
                  :colon="false"
                  labelAlign="left"
                >
                  <ExecuteConfig
                    :topIndex="topIndex"
                    :processEvent="item.processEvent"
                    @submit="submitEvent"
                  />
                </a-form-item>
              </template>
              <div
                class="close-icon"
                v-if="data.info.config.buttons && data.info.config.buttons.length > 1"
                @click="deleteButton(topIndex)"
              >
                <Icon icon="ant-design:close-circle-outlined" color="#ff8080" :size="20" />
              </div>
            </div>
            <a-button type="primary" @click="addButton">{{ t('添加') }}</a-button>
          </div>
        </a-form-item>
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch, nextTick } from 'vue';
  import Box from './Box.vue';
  import { buttonInfo } from '../config/info';
  import { ButtonInfo } from '/@/model/desktop/designer';
  import { ButtonType } from '/@/enums/desktop';
  import Icon from '/@/components/Icon/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  import {
    createButtonConfig,
    executeButtonConfig,
    searchButtonConfig,
  } from '../config/properties';
  import SettingForm from './collapse/form/SettingForm.vue';
  import SelectApi from './collapse/complex/SelectApi.vue';
  import ColumnSelect from './ColumnSelect.vue';
  import ExecuteConfig from './collapse/ExecuteConfig.vue';
  import { cloneDeep } from 'lodash-es';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: ButtonInfo;
    }>(),
    {
      info: () => {
        return buttonInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    show: boolean;
    info: ButtonInfo;
  } = reactive({
    show: false,
    info: buttonInfo,
  });
  const activeKey = ref(['1', '2']);
  onMounted(async () => {
    data.info = props.info;
    data.show = true;
  });
  function addButton() {
    data.info.config.buttons.push({
      ...createButtonConfig,
    });
  }
  function deleteButton(index) {
    data.info.config.buttons.splice(index, 1);
  }
  async function changeButton(index) {
    await nextTick();
    if (data.info.config.buttons[index].type == ButtonType.CREATE) {
      data.info.config.buttons[index] = createButtonConfig;
    } else if (data.info.config.buttons[index].type == ButtonType.SEARCH) {
      data.info.config.buttons[index] = searchButtonConfig;
    } else if (data.info.config.buttons[index].type == ButtonType.EXECUTE) {
      data.info.config.buttons[index] = cloneDeep(executeButtonConfig);
    }
  }
  function submitEvent(obj) {
    let { index, processEvent } = obj;
    data.info.config.buttons[index].processEvent = processEvent;
  }
  const exampleStr = ` {
      "code":0,
      "msg":"提示信息",
      "data":
      [
        {"label":"选项一","value":1},
        {"label":"选项二","value":2},
        {"label":"选项三","value":3}
      ]
    }`;
</script>

<style lang="less" scoped>
  .card-box {
    .card-item {
      position: relative;
      margin: 10px 0;
      padding: 20px 10px;
      background: #f5f5f5;
      border-radius: 4px;
    }

    .close-icon {
      position: absolute;
      top: -10px;
      right: -6px;
    }
  }
</style>
