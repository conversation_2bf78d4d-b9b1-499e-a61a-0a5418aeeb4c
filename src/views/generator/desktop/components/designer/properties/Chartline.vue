<template>
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('基础配置')">
        <a-form-item :label="t('标题')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.title" />
        </a-form-item>
        <a-form-item :label="t('条件颜色')" :colon="false" labelAlign="left">
          <SelectColor
            v-model:value="data.info.config.condition.color"
            @change="changeIndicatorData"
          />
        </a-form-item>
        <a-form-item :label="t('条件选中')" :colon="false" labelAlign="left">
          <SelectColor
            v-model:value="data.info.config.condition.selected"
            @change="changeIndicatorData"
          />
        </a-form-item>
        <a-form-item :label="t('横向')" :colon="false" labelAlign="left">
          <a-switch v-model:checked="data.info.config.isCrosswise" @change="changeIndicatorData" />
        </a-form-item>
        <a-form-item :label="t('显示统计')" :colon="false" labelAlign="left">
          <a-switch v-model:checked="data.info.config.count.show" @change="changeIndicatorData" />
        </a-form-item>
        <a-form-item :label="t('统计单位')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.count.unit" @change="changeIndicatorData" />
        </a-form-item>
        <a-form-item :label="t('统计标题')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.count.title" @change="changeIndicatorData" />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('数据配置')">
        <a-form-item :colon="false" labelAlign="left">
          <div
            class="card-box"
            v-if="
              data.info.config && data.info.config.dataList && data.info.config.dataList.length > 0
            "
          >
            <div
              class="card-item"
              v-for="(item, index) in data.info.config.dataList"
              :key="index"
              @click="changeApiIndex(index)"
            >
              <a-form-item
                :label="t('名称')"
                :label-col="{ style: { width: '40px' } }"
                :colon="false"
                labelAlign="left"
              >
                <a-input v-model:value="item.title" />
              </a-form-item>
              <a-form-item
                :label="t('数据')"
                :label-col="{ style: { width: '40px' } }"
                :colon="false"
                labelAlign="left"
              >
                <ApiSelect
                  v-model:value="item.apiConfig"
                  :exampleStr="exampleStr"
                  @save="
                    (val) => {
                      saveApiConfig(val, index);
                    }
                  "
                />
              </a-form-item>
              <a-form-item
                :label="t('维度')"
                :label-col="{ style: { width: '40px' } }"
                :colon="false"
                labelAlign="left"
              >
                <a-select
                  v-model:value="item.valueKey"
                  style="width: 100%"
                  allowClear
                  @change="changeIndicatorData"
                >
                  <a-select-option
                    v-for="(val, valIndex) in data.info.config.dataList[index].apiColumns"
                    :key="valIndex"
                    :value="val.prop"
                    >{{ val.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>

              <div class="indicator-box">
                <div
                  class="indicator-item"
                  v-for="(val, valIndex) in item.indicator"
                  :key="valIndex"
                >
                  <Icon
                    v-if="val.type == 'line'"
                    color="#fff"
                    :size="20"
                    class="indicator-icon"
                    icon="ant-design:line-chart-outlined"
                    @click="changeIndicatorType(index, valIndex, 'bar')"
                  />
                  <Icon
                    v-if="val.type == 'bar'"
                    color="#fff"
                    :size="20"
                    class="indicator-icon"
                    icon="ant-design:bar-chart-outlined"
                    @click="changeIndicatorType(index, valIndex, 'line')"
                  />
                  <a-input
                    v-model:value="val.name"
                    style="width: 80px; margin: 0 2px"
                    @change="changeIndicatorData"
                  />
                  <a-select
                    v-model:value="val.value"
                    style="width: 100%"
                    allowClear
                    @change="changeIndicatorData"
                  >
                    <a-select-option
                      v-for="(col, coIndex) in data.info.config.dataList[index].apiColumns"
                      :key="coIndex"
                      :value="col.prop"
                      >{{ col.label }}</a-select-option
                    >
                  </a-select>
                  <SelectColor v-model:value="val.color" @change="changeIndicatorData" />
                  <a-popover :title="t('高级设置')">
                    <template #content>
                      <a-form-item :label="t('面积图')" :colon="false" labelAlign="left">
                        <a-switch
                          v-model:checked="val.showAreaStyle"
                          @change="debounceArea(valIndex)"
                        />
                      </a-form-item>
                      <a-form-item
                        :label="t('渐变开始')"
                        :colon="false"
                        labelAlign="left"
                        v-if="val.showAreaStyle"
                      >
                        <SelectColor
                          v-model:value="val.gradualStartColor"
                          @change="debounceArea(valIndex)"
                          style="min-width: 200px"
                        />
                      </a-form-item>
                      <a-form-item
                        :label="t('渐变结束')"
                        :colon="false"
                        labelAlign="left"
                        v-if="val.showAreaStyle"
                      >
                        <SelectColor
                          v-model:value="val.gradualEndColor"
                          @change="debounceArea(valIndex)"
                          style="min-width: 200px"
                        />
                      </a-form-item>
                    </template>
                    <Icon icon="ant-design:setting-outlined" :size="20" class="setting-icon" />
                  </a-popover>

                  <Icon
                    v-if="item.indicator && item.indicator.length > 1"
                    icon="ant-design:delete-outlined"
                    :size="20"
                    class="delete-icon"
                    @click="deleteIndicator(index, valIndex)"
                  />
                </div>
                <a-button class="indicator-btn" @click="addIndicator(index)">{{
                  t('添加指标')
                }}</a-button>
              </div>
              <div
                class="close-icon"
                v-if="data.info.config.dataList && data.info.config.dataList.length > 1"
              >
                <Icon
                  icon="ant-design:close-circle-outlined"
                  color="#ff8080"
                  :size="20"
                  @click="deleteDataList(index)"
                />
              </div>
            </div>
            <a-button type="primary" @click="addDataList">{{ t('添加') }}</a-button>
          </div>
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="3" :header="t('Y轴配置')">
        <div class="card-box">
          <div v-for="(yAxis, index) in data.info.config.yAxis" :key="index" class="card-item">
            <a-form-item :label="t('名称')" :colon="false" labelAlign="left">
              <a-input v-model:value="yAxis.name" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('名称位置')" :colon="false" labelAlign="left">
              <a-select
                v-model:value="yAxis.nameLocation"
                style="width: 100%"
                allowClear
                @change="changeAxisData"
              >
                <a-select-option value="start">{{ t('开头') }}</a-select-option>
                <a-select-option value="center">{{ t('中间') }}</a-select-option>
                <a-select-option value="end">{{ t('末尾') }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item :label="t('名称颜色')" :colon="false" labelAlign="left">
              <SelectColor v-model:value="yAxis.nameTextStyle.color" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('名称大小')" :colon="false" labelAlign="left">
              <a-input-number
                v-model:value="yAxis.nameTextStyle.fontSize"
                @change="changeAxisData"
              />
            </a-form-item>
            <a-form-item :label="t('名称粗细')" :colon="false" labelAlign="left">
              <a-select
                v-model:value="yAxis.nameTextStyle.fontWeight"
                style="width: 100%"
                allowClear
                @change="changeAxisData"
              >
                <a-select-option value="normal">{{ t('正常') }}</a-select-option>
                <a-select-option value="bold">{{ t('加粗') }}</a-select-option>
                <a-select-option value="bolder">{{ t('更粗') }}</a-select-option>
                <a-select-option value="lighter">{{ t('细') }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item :label="t('最小值')" :colon="false" labelAlign="left">
              <a-input-number v-model:value="yAxis.min" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('最大值')" :colon="false" labelAlign="left">
              <a-input-number v-model:value="yAxis.max" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('分割间隔')" :colon="false" labelAlign="left">
              <a-input-number v-model:value="yAxis.interval" @change="changeAxisData" :min="0" />
            </a-form-item>
            <a-form-item :label="t('轴位置')" :colon="false" labelAlign="left">
              <a-select
                v-model:value="yAxis.position"
                style="width: 100%"
                allowClear
                @change="changeAxisData"
              >
                <a-select-option value="left">{{ t('左') }}</a-select-option>
                <a-select-option value="right">{{ t('右') }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item :label="t('数值类型')" :colon="false" labelAlign="left">
              <a-select
                v-model:value="yAxis.type"
                style="width: 100%"
                allowClear
                @change="changeAxisData"
              >
                <a-select-option value="value">{{ t('数值') }}</a-select-option>
                <a-select-option value="category">{{ t('类目') }}</a-select-option>
                <a-select-option value="time">{{ t('时间') }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item :label="t('数值格式')" :colon="false" labelAlign="left">
              <div class="flex">
                <a-input v-model:value="yAxis.axisLabel.formatter" @change="changeAxisData" />
                <a-tooltip :title="formatterText" color="#545454">
                  <Icon
                    icon="ant-design:question-circle-outlined"
                    :size="20"
                    style="display: inline-flex; align-items: center; justify-content: center"
                  />
                </a-tooltip>
              </div>
            </a-form-item>
            <a-form-item :label="t('数值颜色')" :colon="false" labelAlign="left">
              <SelectColor v-model:value="yAxis.axisLabel.color" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('线条显示')" :colon="false" labelAlign="left">
              <a-switch v-model:checked="yAxis.axisLine.show" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('线条颜色')" :colon="false" labelAlign="left">
              <SelectColor
                v-model:value="yAxis.axisLine.lineStyle.color"
                @change="changeAxisData"
              />
            </a-form-item>
            <div
              class="close-icon"
              v-if="data.info.config.yAxis && data.info.config.yAxis.length > 1"
            >
              <Icon
                icon="ant-design:close-circle-outlined"
                color="#ff8080"
                :size="20"
                @click="deleteYAxis"
              />
            </div>
          </div>
        </div>
        <a-button
          type="primary"
          v-if="data.info.config.yAxis && data.info.config.yAxis.length == 1"
          @click="addYAxis"
          >{{ t('添加') }}</a-button
        >
      </a-collapse-panel>
      <a-collapse-panel key="4" :header="t('X轴配置')">
        <div class="card-box">
          <div v-for="(xAxis, index) in data.info.config.xAxis" :key="index" class="card-item">
            <a-form-item :label="t('轴位置')" :colon="false" labelAlign="left">
              <a-select
                v-model:value="xAxis.position"
                style="width: 100%"
                allowClear
                @change="changeAxisData"
              >
                <a-select-option value="top">{{ t('上') }}</a-select-option>
                <a-select-option value="bottom">{{ t('下') }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item :label="t('名称')" :colon="false" labelAlign="left">
              <a-input v-model:value="xAxis.name" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('名称位置')" :colon="false" labelAlign="left">
              <a-select
                v-model:value="xAxis.nameLocation"
                style="width: 100%"
                allowClear
                @change="changeAxisData"
              >
                <a-select-option value="start">{{ t('开头') }}</a-select-option>
                <a-select-option value="center">{{ t('中间') }}</a-select-option>
                <a-select-option value="end">{{ t('末尾') }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item :label="t('名称颜色')" :colon="false" labelAlign="left">
              <SelectColor v-model:value="xAxis.nameTextStyle.color" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('名称大小')" :colon="false" labelAlign="left">
              <a-input-number
                v-model:value="xAxis.nameTextStyle.fontSize"
                @change="changeAxisData"
              />
            </a-form-item>
            <a-form-item :label="t('名称粗细')" :colon="false" labelAlign="left">
              <a-select
                v-model:value="xAxis.nameTextStyle.fontWeight"
                style="width: 100%"
                allowClear
                @change="changeAxisData"
              >
                <a-select-option value="normal">{{ t('正常') }}</a-select-option>
                <a-select-option value="bold">{{ t('加粗') }}</a-select-option>
                <a-select-option value="bolder">{{ t('更粗') }}</a-select-option>
                <a-select-option value="lighter">{{ t('细') }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item :label="t('类型')" :colon="false" labelAlign="left">
              <a-select
                v-model:value="xAxis.type"
                style="width: 100%"
                allowClear
                @change="changeAxisData"
              >
                <a-select-option value="value">{{ t('数值') }}</a-select-option>
                <a-select-option value="category">{{ t('类目') }}</a-select-option>
                <a-select-option value="time">{{ t('时间') }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item :label="t('格式')" :colon="false" labelAlign="left">
              <div class="flex">
                <a-input v-model:value="xAxis.axisLabel.formatter" @change="changeAxisData" />
                <a-tooltip :title="formatterText" color="#545454">
                  <Icon
                    icon="ant-design:question-circle-outlined"
                    :size="20"
                    style="display: inline-flex; align-items: center; justify-content: center"
                  />
                </a-tooltip>
              </div>
            </a-form-item>
            <a-form-item :label="t('数值颜色')" :colon="false" labelAlign="left">
              <SelectColor v-model:value="xAxis.axisLabel.color" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('线条显示')" :colon="false" labelAlign="left">
              <a-switch v-model:checked="xAxis.axisLine.show" @change="changeAxisData" />
            </a-form-item>
            <a-form-item :label="t('线条颜色')" :colon="false" labelAlign="left">
              <SelectColor
                v-model:value="xAxis.axisLine.lineStyle.color"
                @change="changeAxisData"
              />
            </a-form-item>
          </div>
        </div>
      </a-collapse-panel>
      <a-collapse-panel key="5" :header="t('柱状图')">
        <a-form-item :label="t('是否堆叠')" :colon="false" labelAlign="left">
          <a-switch v-model:checked="data.info.config.bar.stack" @change="changeIndicatorData" />
        </a-form-item>
        <a-form-item :label="t('显示文本')" :colon="false" labelAlign="left">
          <a-switch
            v-model:checked="data.info.config.bar.label.show"
            @change="changeIndicatorData"
          />
        </a-form-item>
        <a-form-item :label="t('文本颜色')" :colon="false" labelAlign="left">
          <SelectColor
            v-model:value="data.info.config.bar.label.color"
            @change="changeIndicatorData"
          />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="6" :header="t('折线图')">
        <a-form-item :label="t('平滑')" :colon="false" labelAlign="left">
          <a-switch v-model:checked="data.info.config.line.smooth" @change="changeIndicatorData" />
        </a-form-item>
        <a-form-item :label="t('显示标记')" :colon="false" labelAlign="left">
          <a-switch
            v-model:checked="data.info.config.line.showSymbol"
            @change="changeIndicatorData"
          />
        </a-form-item>
        <a-form-item :label="t('面积图')" :colon="false" labelAlign="left">
          <a-switch
            v-model:checked="data.info.config.line.showAreaStyle"
            @change="changeIndicatorData"
          />
        </a-form-item>
        <a-form-item :label="t('渐变开始')" :colon="false" labelAlign="left">
          <SelectColor
            v-model:value="data.info.config.line.gradualStartColor"
            @change="changeIndicatorData"
          />
        </a-form-item>
        <a-form-item :label="t('渐变结束')" :colon="false" labelAlign="left">
          <SelectColor
            v-model:value="data.info.config.line.gradualEndColor"
            @change="changeIndicatorData"
          />
        </a-form-item>
        <a-form-item :label="t('是否堆叠')" :colon="false" labelAlign="left">
          <a-switch v-model:checked="data.info.config.line.stack" @change="changeIndicatorData" />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="7" :header="t('文本标签')">
        <Label
          v-model:label="data.info.config.line.label"
          :formatterText="labelFormatterText"
          @change="changeIndicatorData"
        />
      </a-collapse-panel>
      <a-collapse-panel key="8" :header="t('图例设置')">
        <Legend
          v-model:legend="data.info.config.legend"
          :formatterText="legendFormatterText"
          @change="changeIndicatorData"
        />
      </a-collapse-panel>
      <a-collapse-panel key="10" :header="t('大小定位')">
        <Location v-model:info="data.info" />
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, onUnmounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import { chartLineInfo } from '../config/info';
  import { MixLineBarInfo } from '/@/model/desktop/designer';
  import ApiSelect from './ApiSelect.vue';
  import SelectColor from './SelectColor.vue';
  import Label from './collapse/Label.vue';
  import Location from './collapse/Location.vue';
  import Legend from './collapse/Legend.vue';
  import Icon from '/@/components/Icon/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { cloneDeep, debounce } from 'lodash-es';

  const { t } = useI18n();
  import {
    chartLineDataListsConfig,
    chartLineEchartsConfig,
    chartLineIndicatorConfig,
    chartLineSeriesConfig,
    chartLineYAxis,
  } from '../config/properties';
  const props = withDefaults(
    defineProps<{
      info: MixLineBarInfo;
    }>(),
    {
      info: () => {
        return chartLineInfo;
      },
    },
  );
  const data: {
    info: MixLineBarInfo;
    show: boolean;
    dataApiIndex: number;
  } = reactive({
    info: chartLineInfo,
    show: false,
    dataApiIndex: 0,
  });
  watch(
    () => props.info,
    (val: MixLineBarInfo) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  watch(
    () => data?.info?.config?.dataList[0].apiData,
    () => {
      if (
        Array.isArray(data?.info?.config?.dataList) &&
        data?.info.config.dataList.length &&
        data?.info.config.dataList[0].apiConfig?.path
      ) {
        changeIndicatorData();
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );
  const activeKey = ref(['1', '2']);
  const debounceArea = debounce(changeArea, 500, {
    leading: true, // 延长开始后调用
    trailing: false, // 延长结束前调用
  });
  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  onUnmounted(() => {
    debounceArea.cancel();
  });
  function changeApiIndex(index: number) {
    data.dataApiIndex = index;
  }
  function saveApiConfig(val, index) {
    data.dataApiIndex = index;
    resetApiData();
    changeIndicatorData();
    data.info.config.dataList[data.dataApiIndex].apiConfig = val;
    resetDisplay();
  }
  function resetApiData() {
    data.info.config.dataList[data.dataApiIndex].apiData = [];
    data.info.config.dataList[data.dataApiIndex].apiColumns = [];
    data.info.config.dataList[data.dataApiIndex].valueKey = '';
    data.info.config.dataList[data.dataApiIndex].indicator = data.info.config.dataList[
      data.dataApiIndex
    ].indicator.map((ele) => {
      ele.name = '';
      ele.title = '';
      ele.color = '';
      ele.value = '';
      ele.showAreaStyle = false;
      ele.gradualStartColor = '';
      ele.gradualEndColor = '';
      return ele;
    });
  }
  function addDataList() {
    // 默认颜色
    let colors = [
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
    ];
    data.info.config.echarts.push({
      ...cloneDeep(chartLineEchartsConfig),
      color: colors,
    });
    data.info.config.dataList.push(cloneDeep(chartLineDataListsConfig));
  }
  function deleteDataList(index) {
    data.info.config.echarts.splice(index, 1);
    data.info.config.dataList.splice(index, 1);
  }
  function addIndicator(index) {
    data.dataApiIndex = index;

    data.info.config.dataList[index].indicator.push(cloneDeep(chartLineIndicatorConfig));
    data.info.config.echarts[index].series.push(cloneDeep(chartLineSeriesConfig));
  }
  function changeArea(valIndex) {
    let index = data.dataApiIndex;
    let val = data.info.config.dataList[index].indicator[valIndex];
    if (val.type == 'line') {
      data.info.config.echarts[index].series[valIndex].areaStyle =
        (val.showAreaStyle || data.info.config.line.showAreaStyle) && val.gradualStartColor
          ? {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: val.gradualStartColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: val.gradualEndColor || val.gradualStartColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            }
          : null;
    }
  }
  function changeIndicatorType(index: number, subindex: number, type: string) {
    data.dataApiIndex = index;
    data.info.config.dataList[index].indicator[subindex].type = type;
    changeIndicatorData();
  }
  function deleteIndicator(index: number, subindex: number) {
    data.dataApiIndex = index;
    data.info.config.dataList[index].indicator.splice(subindex, 1);
    data.info.config.echarts[index].series.splice(subindex, 1);
    changeIndicatorData();
  }
  function changeIndicatorData() {
    let colors = [
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
    ];
    let index = data.dataApiIndex;
    colors = data.info.config.dataList[index].indicator.map((ele, eleIndex) => {
      return ele.color ? ele.color : colors[eleIndex];
    });
    data.info.config.echarts[index].color = colors;
    let legendData: Array<string> = [];
    if (
      data.info.config.dataList[index].apiData &&
      data.info.config.dataList[index].apiData.length > 0
    ) {
      data.info.config.echarts[index].xAxis[0].data = data.info.config.dataList[index].apiData.map(
        (cate) => {
          return data.info.config.dataList[index].valueKey
            ? cate[data.info.config.dataList[index].valueKey]
            : '';
        },
      );
    } else {
      data.info.config.echarts[index].xAxis[0].data = [];
    }
    //是否为横向
    data.info.config.echarts[index].xAxis.forEach(
      (x) => (x.type = data.info.config.isCrosswise ? 'value' : 'category'),
    );
    data.info.config.echarts[index].yAxis.forEach(
      (x) => (x.type = data.info.config.isCrosswise ? 'category' : 'value'),
    );
    if (data.info.config.dataList[index].indicator.length > 0) {
      data.info.config.dataList[index].indicator.forEach((item, itemIndex) => {
        legendData.push(item.name);
        let valueData = data.info.config.dataList[index].apiData.map((ele) => {
          return item.value ? ele[item.value] : 0;
        });
        data.info.config.echarts[index].series[itemIndex].data = valueData;
        // data.info.config.echarts[index].series[itemIndex].showAreaStyle = item.showAreaStyle;
        data.info.config.echarts[index].series[itemIndex].name = item.name;
        data.info.config.echarts[index].series[itemIndex].type = item.type;
        // bar 样式
        if (item.type == 'bar') {
          data.info.config.echarts[index].series[itemIndex].stack = data.info.config.bar.stack
            ? 'total'
            : '';
          data.info.config.echarts[index].series[itemIndex].label = data.info.config.bar.label;
        }
        if (item.type == 'line') {
          data.info.config.echarts[index].series[itemIndex].stack = data.info.config.line.stack
            ? 'total'
            : '';
          data.info.config.echarts[index].series[itemIndex].smooth = data.info.config.line.smooth
            ? data.info.config.line.smooth
            : false;
          data.info.config.echarts[index].series[itemIndex].symbol = data.info.config.line
            .showSymbol
            ? 'emptyCircle'
            : 'none';
          // data.info.config.echarts[index].series[itemIndex].areaStyle =
          //   data.info.config.line.showAreaStyle || item.showAreaStyle ? {} : null;
          data.info.config.echarts[index].series[itemIndex].label = data.info.config.line.label!;
        }
      });
    }
    data.info.config.echarts[index].legend = { ...data.info.config.legend, data: legendData };
  }
  function addYAxis() {
    data.info.config.yAxis.push({
      ...chartLineYAxis,
      position: 'right',
    });
    setAxisData();
  }
  function deleteYAxis(index: number) {
    data.info.config.yAxis.splice(index, 1);
    setAxisData();
  }
  function changeAxisData() {
    let yAxisConfig = data.info.config.yAxis;
    data.info.config.echarts.forEach((_element, index) => {
      data.info.config.echarts[index].yAxis = yAxisConfig;
    });
    let xAxisConfig = data.info.config.xAxis;
    data.info.config.echarts.forEach((_element, index) => {
      data.info.config.echarts[index].xAxis = xAxisConfig;
    });
    changeIndicatorData();
    resetDisplay();
  }
  function setAxisData() {
    changeAxisData();
    resetDisplay();
  }
  function resetDisplay() {
    if (data.info.config.renderKey >= 0) {
      data.info.config.renderKey++;
    }
  }

  const exampleStr = ` {
      code: 0,
      msg: 'success',
      data: {
        columns: [
          {prop:'name',label:'测试1'},
          {prop:'value',label:'测试2'}
        ],
        list: [
          {name:'demo1',value:10},
          {name:'demo2',value:30}
        ],
      },
    }`;
  const labelFormatterText = `参数说明
{a}：系列名。

{b}：数据名。

{c}：数据值。

{d}：百分比。

{e}：换行。

{@xxx}：数据中名为 'xxx' 的维度的值，如 {@product} 表示名为'product' 的维度的值。

{@[n]}：数据中维度 n 的值，如 {@[3]}表示维度 3 的值，从 0 开始计数。`;
  const formatterText = `参数说明
{a}：系列名。

{b}：数据名。

{c}：数据值。

{d}：百分比。

{e}：换行。

{@xxx}：数据中名为 'xxx' 的维度的值，如 {@product} 表示名为'product' 的维度的值。

{@[n]}：数据中维度 n 的值，如 {@[3]}表示维度 3 的值，从 0 开始计数。`;
  const legendFormatterText = `参数说明
{name}：数据名。

{value}：数据值。`;
</script>

<style lang="less" scoped>
  .card-box {
    .card-item {
      position: relative;
      margin: 10px 0;
      padding: 20px 10px;
      background: #f5f5f5;
      border-radius: 4px;
    }

    .close-icon {
      position: absolute;
      top: -10px;
      right: -6px;
    }
  }

  .indicator-box {
    display: flex;
    flex-direction: column;
  }

  .indicator-box .indicator-item {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 40px;
    margin: 4px 0;
    background-color: #eaedf1;
    border-radius: 6px;
    padding: 16px 4px;
    border: 1px solid #d3dbe5;
  }

  .indicator-box .indicator-btn {
    align-self: flex-end;
    background-color: #8cd38c;
    color: #fff;
    border: none;
    margin-top: 10px;
  }

  .indicator-box .indicator-icon {
    background-color: @primary-color;
    border-radius: 4px;
    padding: 4px;
  }

  .delete-icon {
    color: @clear-color;
  }

  .setting-icon {
    color: @primary-color;
  }

  .indicator-box :deep(.rule-input) {
    display: none;
  }
</style>
