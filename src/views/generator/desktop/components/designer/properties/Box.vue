<template>
  <div class="properties-box">
    <a-form name="basic" :label-col="labelCol" autocomplete="off">
      <slot></slot>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  const labelCol = { style: { width: '70px' } };
</script>

<style lang="less" scoped>
  .properties-box {
    height: 90vh;
    position: relative;
    margin-top: 50px;
    overflow: auto;
    padding-bottom: 80px;
  }

  :deep(.ant-collapse-ghost > .ant-collapse-item) {
    border-bottom: 1px solid #f5f5f5;
  }

  :deep(.ant-collapse-icon-position-right > .ant-collapse-item > .ant-collapse-header) {
    padding: 10px 16px;
  }

  :deep(.ant-collapse-content-box .ant-form-item) {
    margin-bottom: 10px;
  }

  :deep(.ant-collapse-content-box) {
    padding-left: 30px;
  }

  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-collapse-ghost
      > .ant-collapse-item
      > .ant-collapse-content
      > .ant-collapse-content-box) {
    padding: 0 30px;
    margin: 10px 0;
  }
</style>
