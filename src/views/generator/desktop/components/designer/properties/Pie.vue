<template>
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('基础配置')">
        <a-form-item :label="t('标题')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.title" />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('数据配置')">
        <a-form-item :label="t('数据视图')" :colon="false" labelAlign="left">
          <ApiSelect
            v-model:value="data.info.config.apiConfig"
            :exampleStr="exampleStr"
            @save="saveApiConfig"
          />
        </a-form-item>
        <a-form-item :label="t('维度')" :colon="false" labelAlign="left">
          <BindApiColumns
            v-model:value="data.info.config.labelKey"
            :apiColumns="data.info.config.apiColumns"
            @change="resetDisplay"
          />
        </a-form-item>
        <a-form-item :label="t('指标')" :colon="false" labelAlign="left">
          <BindApiColumns
            v-model:value="data.info.config.valueKey"
            :apiColumns="data.info.config.apiColumns"
            @change="resetDisplay"
          />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="3" :header="t('饼图设置')">
        <a-form-item :label="t('圆心大小')" :colon="false" labelAlign="left">
          <a-slider
            v-model:value="data.pieConfig.centerOfCircleSize"
            @change="changeCenterOfCircleSize"
          />
        </a-form-item>
        <a-form-item :label="t('外圆大小')" :colon="false" labelAlign="left">
          <a-slider
            v-model:value="data.pieConfig.sizeOfOuterCircle"
            @change="changeSizeOfOuterCircle"
          />
        </a-form-item>
        <a-form-item :label="t('水平位置')" :colon="false" labelAlign="left">
          <a-slider
            v-model:value="data.pieConfig.horizontalPosition"
            @change="changeHorizontalPosition"
          />
        </a-form-item>
        <a-form-item :label="t('垂直位置')" :colon="false" labelAlign="left">
          <a-slider
            v-model:value="data.pieConfig.verticalPosition"
            @change="changeVerticalPosition"
          />
        </a-form-item>
        <a-form-item :label="t('南丁格尔')" :colon="false" labelAlign="left">
          <a-select
            v-model:value="data.info.config.echarts.series[0].roseType"
            style="width: 100%"
            allowClear
            @change="resetDisplay"
          >
            <a-select-option
              v-for="(item, index) in roseTypeOptions"
              :key="index"
              :value="item.value"
              >{{ item.label }}</a-select-option
            >
          </a-select>
        </a-form-item>
        <a-form-item :label="t('圆角大小')" :colon="false" labelAlign="left">
          <a-slider v-model:value="data.pieConfig.borderRadius" @change="changeBorderRadius" />
        </a-form-item>
        <a-form-item :label="t('选中模式')" :colon="false" labelAlign="left">
          <a-select
            v-model:value="data.info.config.echarts.series[0].selectedMode"
            style="width: 100%"
            allowClear
            @change="resetDisplay"
          >
            <a-select-option
              v-for="(item, index) in selectedModeOptions"
              :key="index"
              :value="item.value"
              >{{ item.label }}</a-select-option
            >
          </a-select>
        </a-form-item>
        <a-form-item :label="t('默认选中')" :colon="false" labelAlign="left">
          <a-input-number
            v-model:value="data.info.config.defaultSelect"
            :min="0"
            @change="resetDisplay"
          />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="4" :header="t('文本标签')">
        <Label
          v-model:label="data.info.config.echarts.series[0].label"
          :formatterText="formatterText"
          @change="resetDisplay"
        />
      </a-collapse-panel>
      <a-collapse-panel key="5" :header="t('图例设置')">
        <Legend
          v-model:legend="data.info.config.echarts.legend"
          :formatterText="legendFormatterText"
          v-model:autoWidth="data.info.config.autoWidth"
          @change="resetDisplay"
        />
      </a-collapse-panel>
      <a-collapse-panel key="6" :header="t('配色设置')">
        <Colors v-model:info="data.info" @change="resetDisplay" />
      </a-collapse-panel>
      <a-collapse-panel key="7" :header="t('大小定位')">
        <Location v-model:info="data.info" />
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import ApiSelect from './ApiSelect.vue';
  import { pieInfo } from '../config/info';
  import Label from './collapse/Label.vue';
  import Legend from './collapse/Legend.vue';
  import Location from './collapse/Location.vue';
  import BindApiColumns from './collapse/BindApiColumns.vue';
  import { LocationType } from '/@/enums/desktop';
  import { PieItemInfo } from '/@/model/desktop/designer';
  import Colors from './collapse/Colors.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: PieItemInfo;
    }>(),
    {
      info: () => {
        return pieInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    info: any;
    show: boolean;
    pieConfig: {
      centerOfCircleSize: number;
      sizeOfOuterCircle: number;
      horizontalPosition: number;
      verticalPosition: number;
      borderRadius: number;
      location: LocationType;
    };
  } = reactive({
    info: pieInfo,
    show: false,
    pieConfig: {
      centerOfCircleSize: 30,
      sizeOfOuterCircle: 60,
      horizontalPosition: 50,
      verticalPosition: 40,
      borderRadius: 0,
      color: [],
      location: LocationType.BOTTOM_CENTER,
    },
  });
  const activeKey = ref(['1', '2', '3']);

  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  function saveApiConfig() {
    resetApiData();
    resetDisplay();
  }
  function resetApiData() {
    data.info.config.apiData = [];
    data.info.config.apiColumns = [];
    data.info.config.valueKey = '';
    data.info.config.labelKey = '';
  }
  function resetDisplay() {
    if (data.info.config.renderKey >= 0) {
      data.info.config.renderKey++;
    }
  }

  function changeCenterOfCircleSize(val: number) {
    if (
      data.info.config.echarts &&
      Array.isArray(data.info.config.echarts.series) &&
      Array.isArray(data.info.config.echarts.series[0].radius)
    ) {
      data.info.config.echarts.series[0].radius[0] = val + '%';
    }
    resetDisplay();
  }
  function changeSizeOfOuterCircle(val: number) {
    if (
      data.info.config.echarts &&
      Array.isArray(data.info.config.echarts.series) &&
      Array.isArray(data.info.config.echarts.series[0].radius)
    ) {
      data.info.config.echarts.series[0].radius[1] = val + '%';
    }
    resetDisplay();
  }
  function changeHorizontalPosition(val: number) {
    if (
      data.info.config.echarts &&
      Array.isArray(data.info.config.echarts.series) &&
      Array.isArray(data.info.config.echarts.series[0].center)
    ) {
      data.info.config.echarts.series[0].center[0] = val + '%';
    }
    resetDisplay();
  }
  function changeVerticalPosition(val: number) {
    if (
      data.info.config.echarts &&
      Array.isArray(data.info.config.echarts.series) &&
      Array.isArray(data.info.config.echarts.series[0].center)
    ) {
      data.info.config.echarts.series[0].center[1] = val + '%';
    }
    resetDisplay();
  }
  function changeBorderRadius(val: number) {
    if (
      data.info.config.echarts &&
      Array.isArray(data.info.config.echarts.series) &&
      data.info.config.echarts.series[0].itemStyle &&
      data.info.config.echarts.series[0].itemStyle
    ) {
      data.info.config.echarts.series[0].itemStyle.borderRadius = val + '%';
    }
    resetDisplay();
  }
  const exampleStr = ` {
      code: 0,
      msg: 'success',
      data: {
        columns: [
          {prop:'name',label:'测试1'},
          {prop:'value',label:'测试2'}
        ],
        list: [
          {name:'demo1',value:10},
          {name:'demo2',value:30}
        ],
      },
    }`;
  const roseTypeOptions = [
    {
      label: t('圆心角数据百分比'),
      value: 'radius',
    },
    {
      label: t('圆心角相同'),
      value: 'area',
    },
  ];
  const selectedModeOptions = [
    {
      label: t('单选'),
      value: 'single',
    },
    {
      label: t('多选'),
      value: 'multiple',
    },
  ];
  const formatterText = `参数说明
{a}：系列名。

{b}：数据名。

{c}：数据值。

{d}：百分比。

{e}：换行。

{@xxx}：数据中名为 'xxx' 的维度的值，如 {@product} 表示名为'product' 的维度的值。

{@[n]}：数据中维度 n 的值，如 {@[3]}表示维度 3 的值，从 0 开始计数。`;

  const legendFormatterText = `参数说明
{name}：数据名。

{value}：数据值。`;
</script>

<style lang="less" scoped>
  .box {
    position: relative;
    padding-top: 50px;
    overflow: auto;
  }

  .item-title {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: #262626;
    font-size: 14px;
    padding-left: 16px;
  }

  .item-title::after {
    content: '';
    display: block;
    position: absolute;
    height: 1px;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #f0f0f0;
  }
</style>
