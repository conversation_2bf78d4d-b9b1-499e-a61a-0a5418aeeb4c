<template>
  <!-- 图例设置 -->
  <a-form :label-col="labelCol">
    <a-form-item :label="t('是否显示')" :colon="false" labelAlign="left">
      <a-switch v-model:checked="data.legend.show" @change="changeLegend" />
    </a-form-item>
    <template v-if="data.legend.show">
      <a-form-item :label="t('布局朝向')" :colon="false" labelAlign="left">
        <a-select
          v-model:value="data.legend.orient"
          style="width: 100%"
          :options="orientTypeOptions"
          @change="changeLegend"
        />
      </a-form-item>
      <a-form-item :label="t('所在位置')" :colon="false" labelAlign="left">
        <a-select
          v-model:value="data.legendConfig.location"
          style="width: 100%"
          :options="locationTypeOptions"
          @change="changeLocation"
        />
      </a-form-item>
      <a-form-item :label="t('边距大小')" :colon="false" labelAlign="left">
        <a-input-number v-model:value="data.legend.padding" :min="0" @change="changeLegend" />
      </a-form-item>
      <a-form-item
        v-if="isBoolean(data.autoWidth)"
        :label="t('宽度自动')"
        :colon="false"
        labelAlign="left"
      >
        <a-switch v-model:checked="data.autoWidth" @change="changeLegend" />
      </a-form-item>
      <a-form-item
        v-if="isBoolean(data.autoWidth) && !data.autoWidth"
        :label="t('宽度')"
        :colon="false"
        labelAlign="left"
      >
        <a-input-number v-model:value="data.legend.width" :min="0" @change="changeLegend" />
      </a-form-item>
      <a-form-item :label="t('标记宽度')" :colon="false" labelAlign="left">
        <a-input-number v-model:value="data.legend.itemWidth" @change="changeLegend" />
      </a-form-item>
      <a-form-item :label="t('标记高度')" :colon="false" labelAlign="left">
        <a-input-number v-model:value="data.legend.itemHeight" @change="changeLegend" />
      </a-form-item>
      <a-form-item :label="t('标记形状')" :colon="false" labelAlign="left">
        <a-select
          v-model:value="data.legend.icon"
          style="width: 100%"
          :options="legendTypeOptions"
          @change="changeLegend"
        />
      </a-form-item>
      <a-form-item :label="t('文本颜色')" :colon="false" labelAlign="left">
        <SelectColor v-model:value="data.legend.textStyle.color" @change="changeLegend" />
      </a-form-item>
      <a-form-item
        :label="t('格式文本')"
        :colon="false"
        labelAlign="left"
        v-if="props.formatterText"
      >
        <div class="flex">
          <a-input v-model:value="data.legend.formatter" @change="changeLegend" />
          <FormatterText :formatterText="props.formatterText" />
        </div>
      </a-form-item>
    </template>
  </a-form>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import SelectColor from './../SelectColor.vue';
  import FormatterText from './FormatterText.vue';
  import { LegendType, LocationType, OrientType } from '/@/enums/desktop';
  import { ChartLegend } from '/@/model/desktop/designer';
  import { legendProperties } from '../../config/properties';
  import { isBoolean } from '/@/utils/is';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const labelCol = { style: { width: '70px' } };
  const props = withDefaults(
    defineProps<{
      legend: ChartLegend;
      formatterText: string;
      autoWidth?: boolean;
    }>(),
    {
      legend: () => {
        return legendProperties;
      },
      formatterText: '',
      autoWidth: undefined,
    },
  );

  const emit = defineEmits(['update:legend', 'change', 'update:autoWidth']);
  const data = reactive({
    legend: props.legend,
    legendConfig: {
      centerOfCircleSize: 30,
      sizeOfOuterCircle: 60,
      horizontalPosition: 50,
      verticalPosition: 40,
      borderRadius: 0,
      color: [],
      location: LocationType.BOTTOM_CENTER,
    },
    autoWidth: props.autoWidth,
  });
  function changeLegend() {
    emit('update:autoWidth', data.autoWidth);
    emit('update:legend', data.legend);
    emit('change');
  }
  function changeLocation(val: LocationType) {
    let left = 'auto'; //'left', 'center', 'right'
    let top = 'auto'; //'top', 'middle', 'bottom'，
    if (val == LocationType.LEFT_TOP) {
      left = 'left';
      top = 'top';
    } else if (val == LocationType.RIGHT_TOP) {
      left = 'right';
      top = 'top';
    } else if (val == LocationType.LEFT_BOTTOM) {
      left = 'left';
      top = 'bottom';
    } else if (val == LocationType.RIGHT_BOTTOM) {
      left = 'right';
      top = 'bottom';
    } else if (val == LocationType.TOP_CENTER) {
      left = 'center';
      top = 'top';
    } else if (val == LocationType.BOTTOM_CENTER) {
      left = 'center';
      top = 'bottom';
    }
    data.legend.left = left;
    data.legend.top = top;
    changeLegend();
  }
  const orientTypeOptions = [
    {
      label: t('横向'),
      value: OrientType.HORIZONTAL,
    },
    {
      label: t('竖向'),
      value: OrientType.VERTICAL,
    },
  ];
  const locationTypeOptions = [
    {
      label: t('左上角'),
      value: LocationType.LEFT_TOP,
    },
    {
      label: t('右上角'),
      value: LocationType.RIGHT_TOP,
    },
    {
      label: t('左下角'),
      value: LocationType.LEFT_BOTTOM,
    },
    {
      label: t('右下角'),
      value: LocationType.RIGHT_BOTTOM,
    },
    {
      label: t('顶部居中'),
      value: LocationType.TOP_CENTER,
    },
    {
      label: t('底部居中'),
      value: LocationType.BOTTOM_CENTER,
    },
  ];
  const legendTypeOptions = [
    {
      label: t('无'),
      value: LegendType.NONE,
    },
    {
      label: t('圆形'),
      value: LegendType.CIRCLE,
    },
    {
      label: t('菱形'),
      value: LegendType.DIAMOND,
    },
    {
      label: t('箭头'),
      value: LegendType.ARROW,
    },
    {
      label: t('水滴'),
      value: LegendType.PIN,
    },
    {
      label: t('矩形'),
      value: LegendType.RECT,
    },
    {
      label: t('圆角矩形'),
      value: LegendType.ROUND_RECT,
    },
    {
      label: t('三角形'),
      value: LegendType.TRIANGLE,
    },
  ];
</script>

<style scoped></style>
