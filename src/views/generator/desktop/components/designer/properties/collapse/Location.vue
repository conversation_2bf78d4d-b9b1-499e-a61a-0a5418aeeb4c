<template>
  <a-form :label-col="labelCol">
    <a-form-item :label="t('宽度')" :colon="false" labelAlign="left">
      <a-input-number
        v-model:value="data.info.w"
        :min="data.info.minW"
        :max="data.info.maxW"
        @change="changeInfo"
      />
    </a-form-item>
    <a-form-item :label="t('高度')" :colon="false" labelAlign="left">
      <a-input-number
        v-model:value="data.info.h"
        :min="data.info.minH"
        :max="data.info.maxH"
        @change="changeInfo"
      />
    </a-form-item>
    <a-form-item :label="t('x轴')" :colon="false" labelAlign="left">
      <a-input-number v-model:value="data.info.x" :min="0" @change="changeInfo" />
    </a-form-item>
    <a-form-item :label="t('y轴')" :colon="false" labelAlign="left">
      <a-input-number v-model:value="data.info.y" :min="0" @change="changeInfo" />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
  import { reactive, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const labelCol = { style: { width: '70px' } };
  const props = withDefaults(
    defineProps<{
      info: {
        x: number;
        y: number;
        w: number;
        h: number;
        i: string;
        maxH: number;
        minH: number;
        maxW: number;
        minW: number;
      };
    }>(),
    {
      info: () => {
        return {
          x: 0,
          y: 0,
          w: 6,
          h: 10,
          i: '0',
          minW: 6,
          minH: 8,
          maxW: 12,
          maxH: 12,
        };
      },
    },
  );

  const emit = defineEmits(['update:info']);
  const data = reactive({
    info: props.info,
  });
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  function changeInfo() {
    emit('update:info', data.info);
  }
</script>

<style scoped></style>
