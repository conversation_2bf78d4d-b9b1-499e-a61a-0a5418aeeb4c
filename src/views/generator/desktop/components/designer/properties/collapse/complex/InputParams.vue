<template>
  <a-tabs>
    <a-tab-pane :key="item.key" :tab="item.title" v-for="item in props.apiParams">
      <a-table
        :dataSource="item.tableInfo"
        :columns="apiConfigColumns"
        :pagination="false"
        :scroll="{ y: '400px' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'assignmentType'">
            <a-select
              v-model:value="record.assignmentType"
              style="width: 100%"
              :placeholder="t('请选择赋值类型')"
              @change="
                () => {
                  record.config = null;
                  changeList();
                }
              "
              allowClear
            >
              <a-select-option :value="bind.value" v-for="bind in bindType" :key="bind.value">
                {{ bind.label }}
              </a-select-option>
            </a-select>
          </template>
          <template v-else-if="column.key === 'value'">
            <!-- 组件数据 -->
            <a-tree-select
              v-if="record.assignmentType === 'formData'"
              v-model:value="record.value"
              show-search
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :placeholder="t('点击选择表单数据')"
              allow-clear
              tree-default-expand-all
              :tree-data="data.dataInfo"
              @select="(_, node) => handleSelect(node, record)"
            />

            <!-- <a-tree-select
              v-if="record.assignmentType === 'formData'"
              v-model:value="record.config"
              show-search
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :placeholder="t('请选择表单数据')"
              allow-clear
              tree-default-expand-all
              :tree-data="data.dataInfo"
              :field-names="{
                children: 'children',
                label: 'title',
                value: 'key',
              }"
            /> -->
            <a-input
              v-else
              v-model:value="record.value"
              :placeholder="record.type ? t('请填写值') : t('请先选择赋值类型后再配置值')"
            />
          </template>
        </template>
      </a-table>
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
  // import { TreeProps } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { ApiParams } from '/@/components/ApiConfig/src/interface';
  import { message } from 'ant-design-vue';
  import { DesktopComponent } from '/@/enums/desktop';
  import { inject, onMounted, reactive } from 'vue';
  const getTableList = inject('getTableList') as any;
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      apiParams: Array<ApiParams>;
      updateIds?: Array<string>;
    }>(),
    {
      apiParams: () => {
        return [];
      },
    },
  );
  let data = reactive({
    dataInfo: [] as any[],
  });
  let bindType = [
    {
      label: t('值'),
      value: 'value',
    },
    {
      label: t('组件数据'),
      value: 'formData',
    },
  ];
  let apiConfigColumns = [
    {
      title: t('API入参名称'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: t('API入参类型'),
      dataIndex: 'dataType',
      key: 'dataType',
      align: 'center',
    },
    {
      title: t('赋值类型'),
      dataIndex: 'assignmentType',
      key: 'assignmentType',
      align: 'center',
    },
    {
      title: t('赋值配置'),
      dataIndex: 'value',
      key: 'value',
      align: 'center',
    },
  ];
  let currentInfo = {
    title: '当前信息',
    value: t('currentInfo'),
    disabled: true,
    children: [
      {
        title: t('当前人员名称'),
        value: 'currentInfo----name',
      },
      {
        title: t('当前人员ID'),
        value: 'currentInfo----id',
      },
      {
        title: t('当前人员编码'),
        value: 'currentInfo----code',
      },
      {
        title: t('当前人员手机号'),
        value: 'currentInfo----mobile',
      },
      {
        title: t('当前人员所属组织架构名称'),
        value: 'currentInfo----departmentName',
      },
      {
        title: t('当前人员所属组织架构ID'),
        value: 'currentInfo----departmentId',
      },
      {
        title: t('当前人员岗位ID'),
        value: 'currentInfo----postId',
      },
      {
        title: t('当前人员角色ID'),
        value: 'currentInfo----roles.id',
      },
    ],
  };
  onMounted(() => {
    changeList();
  });
  function changeList() {
    let dataList: Array<any> = [];
    if (getTableList) {
      let temp = getTableList().filter((ele) => {
        return ele.type == DesktopComponent.TABLE;
      });
      if (temp.length > 0) {
        temp.forEach((element) => {
          let children = [];
          if (
            props.updateIds == undefined ||
            (props.updateIds && props.updateIds.includes(element.i))
          ) {
            if (element.config && element.config.columns.length > 0) {
              children = element.config.columns.map((ele) => {
                ele.value = element.i + '___' + ele.dataIndex;
                return ele;
              });
            }
            let obj = {
              title: element.config.title,
              value: element.i,
              disabled: true,
              children,
            };
            dataList.push(obj);
          }
        });
      }
    }
    data.dataInfo = [...dataList, currentInfo];
  }

  const handleSelect = ({ value }, record) => {
    if (!value) {
      message.error(t('请先选择该组件的绑定表及绑定字段'));
      record.value = null;
    }
  };
</script>

<style scoped></style>
