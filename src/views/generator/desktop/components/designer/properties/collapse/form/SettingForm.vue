<template>
  <div @click="show" class="slot-item">
    <slot></slot>
  </div>
  <ModalPanel :visible="visible" :title="t('表单设置')" @submit="submit" @close="close">
    <template #left>
      <Category v-model="setting.checkFormType" @change="changeType" />
    </template>
    <SearchBox @search="searchList" />
    <div class="list-page-box" v-if="setting.list.length > 0">
      <FormCard
        v-for="(item, index) in setting.list"
        :key="index"
        :item="item"
        :class="setting.checkedFormId === item.formId ? 'picked' : 'notPicked'"
        @click="checked(item)"
      >
        <template #check>
          <a-checkbox
            size="small"
            :checked="setting.checkedFormId === item.formId ? true : false"
            :disabled="item.enabledMark == 1 ? false : true"
          />
        </template>
      </FormCard>
      <div class="page-box">
        <a-pagination
          v-model:current="setting.page.current"
          :pageSize="setting.page.pageSize"
          :total="setting.page.total"
          show-less-items
          @change="getList"
        />
      </div>
    </div>
    <EmptyBox v-else />
  </ModalPanel>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { ModalPanel, SearchBox, EmptyBox } from '/@/components/ModalPanel/index';
  import Category from './Category.vue';
  import FormCard from '/@bpmn/components/card/FormCard.vue';
  import { FormType } from '/@/enums/workflowEnum';
  import { getFormComplexList } from '/@/api/form/design/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let props = withDefaults(
    defineProps<{
      formId: string;
      formName: string;
      systemComponent?: {
        functionalModule: string;
        functionName: string;
        functionFormName: string;
      };
    }>(),
    {
      formId: '',
      formName: '',
      systemComponent: () => {
        return {
          functionalModule: '',
          functionName: '',
          functionFormName: 'Form',
        };
      },
    },
  );
  let emits = defineEmits(['update:formName', 'update:formId', 'update:systemComponent', 'change']);

  const visible = ref(false);
  let setting: {
    checkFormType: FormType;
    checkedFormId: string;
    checkedFormName: string;
    list: Array<any>;
    searchKeyword: string;
    page: { current: number; total: number; pageSize: number };
    systemComponent: {
      functionalModule: string;
      functionName: string;
      functionFormName: string;
    };
  } = reactive({
    checkFormType: FormType.CUSTOM,
    checkedFormId: '',
    checkedFormName: '',
    list: [],
    searchKeyword: '',
    page: { current: 1, total: 0, pageSize: 9 },
    systemComponent: {
      functionalModule: '',
      functionName: '',
      functionFormName: 'Form',
    },
  });
  function show() {
    setting.checkFormType = FormType.CUSTOM;
    setting.list = [];
    setting.page.total = 0;
    setting.checkedFormId = props.formId;
    setting.checkedFormName = props.formName;
    getList();
    visible.value = true;
  }
  function changeType() {
    setting.list = [];
    setting.page.total = 0;
    getList();
  }
  async function getList() {
    await getTemplateList();
  }
  async function getTemplateList() {
    let params = {
      limit: setting.page.current,
      size: setting.page.pageSize,
      type: setting.checkFormType,
      keyword: setting.searchKeyword,
    };
    let res = await getFormComplexList(params);
    if (res.total) {
      setting.page.total = res.total;
    }
    setting.list = [];
    if (res.list.length > 0) {
      res.list.forEach((ele) => {
        setting.list.push({
          formType: ele.formType,
          formName: ele.name,
          formId: ele.id,
          enabledMark: ele.enabledMark,
          functionalModule: ele.functionalModule,
          functionName: ele.functionName,
        });
      });
    }
  }

  async function submit() {
    if (!setting.checkedFormId) {
      message.error(t('请至少选择一个表单'));
      return false;
    }
    emits('update:formId', setting.checkedFormId);
    emits('update:formName', setting.checkedFormName);
    emits('update:systemComponent', setting.systemComponent);
    emits('change', setting);
    close();
  }
  function close() {
    visible.value = false;
  }
  function searchList(keyword: string) {
    setting.searchKeyword = keyword;
    setting.page.current = 1;
    getList();
  }
  function checked(item: {
    formId: string;
    formName: string;
    functionalModule: string;
    functionName: string;
  }) {
    setting.checkedFormId = item.formId;
    setting.checkedFormName = item.formName;
    setting.systemComponent = {
      functionalModule: item.functionalModule,
      functionName: item.functionName,
      functionFormName: 'Form',
    };
  }
</script>
<style lang="less" scoped>
  .list-box {
    display: flex;
    flex-wrap: wrap;
    padding: 10px 0;
  }

  .page-box {
    position: absolute;
    bottom: 80px;
    right: 20px;
  }

  .picked {
    border-width: 1px;
    border-style: dotted;
  }

  .notPicked {
    border-width: 1px;
    border-style: dotted;
    border-color: transparent;
  }

  .flow-used {
    height: calc(100% - 100px);
    overflow: auto;
  }

  :deep(.ant-collapse-ghost > .ant-collapse-item) {
    border-bottom: 1px solid #f5f5f5;
  }

  :deep(.ant-collapse-icon-position-right > .ant-collapse-item > .ant-collapse-header) {
    padding: 10px 16px;
  }

  :deep(.ant-collapse-content-box .ant-form-item) {
    margin-bottom: 12px;
  }

  :deep(.ant-collapse-content-box) {
    padding-left: 30px;
  }

  :deep(.ant-input-number) {
    width: 100%;
  }

  .opr-box {
    display: flex;
    padding: 30px 20px;

    .opr-icon {
      margin-right: 10px;
    }

    .opr-content {
      .title {
        font-weight: 700;
      }

      .desc {
        color: #a0a0a0;
      }
    }
  }
</style>
