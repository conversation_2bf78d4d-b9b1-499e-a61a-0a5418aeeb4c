<template>
  <div @click.stop="open">
    <InputModel
      :value="eventConfig.processEvent.length > 0 ? '已配置' : ''"
      :placeholder="t('执行事件配置')"
      style="width: 100%; min-width: 100px"
    />
  </div>
  <ModalPanel
    :visible="eventConfig.visible"
    :width="800"
    :title="t('执行事件配置')"
    @submit="submit"
    @close="close"
  >
    <div>
      <NodeHead :nodeName="t('执行事件列表')" class="title" />
      <div class="config-box">
        <a-button type="primary" @click="addEvent">添加执行事件</a-button>
        <a-table :columns="columns" :data-source="eventConfig.processEvent" :pagination="false">
          <template #headerCell="{ column }">
            <template v-if="column.key === 'sort'">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-fangxiang1" />
              </svg>
            </template>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'sort'">
              <svg class="icon draggable-icon" aria-hidden="true" style="cursor: move">
                <use xlink:href="#icon-paixu" />
              </svg>
            </template>
            <template v-if="column.key === 'operateType'">
              <a-select v-model:value="record[column.dataIndex]">
                <a-select-option :value="ExecuteType.API">{{ t('执行API') }}</a-select-option>
                <a-select-option :value="ExecuteType.JS">{{ t('执行JS脚本') }}</a-select-option>
                <a-select-option :value="ExecuteType.RULE">{{ t('规则引擎') }}</a-select-option>
                <a-select-option :value="ExecuteType.PUSH_MESSAGE">
                  {{ t('消息模板') }}
                </a-select-option>
              </a-select>
            </template>
            <template v-if="column.key === 'operateConfig'">
              <SelectApi
                v-if="record.operateType == ExecuteType.API"
                v-model:value="record.operateConfig.apiConfig"
                :exampleStr="exampleStr2"
              />
              <a-select
                v-else-if="record.operateType === ExecuteType.RULE"
                v-model:value="record.operateConfig.rule"
                :options="liteFlowOptions"
                placeholder="配置规则"
                :field-names="{ label: 'chainName', value: 'id' }"
              />
              <InputModel
                v-else-if="record.operateType === ExecuteType.JS"
                :value="record.operateConfig.js ? '已配置' : ''"
                :placeholder="t('配置JS脚本')"
                style="width: 100%; min-width: 100px"
                @click="showJsConfig(index, record.operateConfig.js)"
              />
              <SelectPushMessage
                v-else-if="record.operateType === ExecuteType.PUSH_MESSAGE"
                style="width: 100%"
                v-model="record.operateConfig.messageConfig"
                :need-hide-components="true"
              />
            </template>
            <template v-if="column.key === 'action'">
              <DeleteTwoTone two-tone-color="#ff8080" @click="deleteEvent(index)" />
            </template>
          </template>
        </a-table>
        <ScriptConfig @register="registerModal" @success="submitConfig" />
      </div>
    </div>
  </ModalPanel>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted, nextTick } from 'vue';
  import { ExecuteType } from '/@/enums/desktop';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { DeleteTwoTone } from '@ant-design/icons-vue';
  import { getLiteflowList } from '/@/api/liteflow';
  import ScriptConfig from './ScriptConfig.vue';
  import SelectApi from './complex/SelectApi.vue';
  import SelectPushMessage from './SelectPushMessage.vue';
  import { InputModel } from '/@/components/ApiConfig';
  import { NodeHead, ModalPanel } from '/@/components/ModalPanel/index';
  import { useModal } from '/@/components/Modal';
  import { processEventItem } from '/@/model/desktop/designer';
  import { cloneDeep } from 'lodash-es';
  import Sortable from 'sortablejs';
  const { t } = useI18n();
  const liteFlowOptions = ref();
  const props = withDefaults(
    defineProps<{
      topIndex: number;
      processEvent: Array<processEventItem>;
    }>(),
    {
      topIndex: -1,
      config: () => {
        return [];
      },
    },
  );
  const emits = defineEmits(['submit']);
  const eventConfig: {
    visible: boolean;
    topIndex: number;
    index: number;
    processEvent: Array<processEventItem>;
  } = reactive({
    visible: false,
    index: -1,
    topIndex: -1,
    processEvent: [],
  });
  const [registerModal, { openModal }] = useModal();
  onMounted(async () => {
    await getLiteFlowOptions();
  });
  const columns = ref([
    {
      dataIndex: 'sort',
      key: 'sort',
    },
    {
      title: t('操作类别'),
      dataIndex: 'operateType',
      key: 'operateType',
      width: '35%',
      align: 'center',
    },
    {
      title: t('操作配置'),
      dataIndex: 'operateConfig',
      key: 'operateConfig',
      width: '50%',
      align: 'center',
    },
    {
      title: t('操作'),
      dataIndex: 'action',
      key: 'action',
      width: '25%',
      align: 'center',
    },
  ]);
  async function getLiteFlowOptions() {
    liteFlowOptions.value = (await getLiteflowList()) || [];
  }
  function open() {
    eventConfig.processEvent = cloneDeep(props.processEvent);
    eventConfig.topIndex = props.topIndex;
    if (eventConfig.processEvent && eventConfig.processEvent.length) {
      nextTick(() => {
        const tbody: any = document.querySelector('.config-box .ant-table-tbody');
        Sortable.create(tbody, {
          handle: '.draggable-icon',
        });
      });
    }
    eventConfig.visible = true;
  }
  function addEvent() {
    eventConfig.processEvent.push({
      operateType: ExecuteType.API,
      operateConfig: {
        rule: '',
        js: '',
        apiConfig: {
          id: '',
          name: '',
          method: '',
          path: '',
          requestParamsConfigs: [], //Query Params
          requestHeaderConfigs: [], //Header
          requestBodyConfigs: [], //Body
        },
      },
    });
  }
  function showJsConfig(index, val) {
    eventConfig.index = index;
    openModal(true, {
      content: val,
    });
  }
  function submitConfig(value) {
    eventConfig.processEvent[eventConfig.index].operateConfig.js = value;
  }
  function deleteEvent(index) {
    eventConfig.processEvent.splice(index, 1);
  }
  function submit() {
    eventConfig.visible = false;
    emits('submit', { index: eventConfig.topIndex, processEvent: eventConfig.processEvent });
  }
  function close() {
    eventConfig.visible = false;
  }
  const exampleStr2 = ` {
      "code":0,
      "msg":"提示信息"
    }`;
</script>

<style lang="less" scoped>
  .process-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .process-title {
      line-height: 18px;
      padding-left: 6px;
      border-left: 6px solid #5e95ff;
    }
  }

  :deep(.ant-select) {
    width: 100%;
  }

  :deep(.ant-table-cell) {
    padding: 10px !important;
  }

  .icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentcolor;
    overflow: hidden;
  }

  .config-box {
    margin: 10px 0;
    padding: 0 30px;
  }
</style>
