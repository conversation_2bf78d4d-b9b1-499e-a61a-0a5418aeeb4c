<template>
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('基础配置')">
        <a-form-item :label="t('标题')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.title" />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('数据配置')">
        <a-form-item :label="t('数据视图')" :colon="false" labelAlign="left">
          <ApiSelect
            v-model:value="data.info.config.apiConfig"
            :exampleStr="exampleStr"
            @save="saveApiConfig"
          />
        </a-form-item>
        <a-form-item :label="t('行数限制')" :colon="false" labelAlign="left">
          <a-input-number v-model:value="data.info.config.maxRows" :min="0" :max="100" />
        </a-form-item>

        <div class="card-box">
          <div class="card-item" v-for="(item, index) in data.info.config.columns" :key="index">
            <a-form-item :label="t('绑定字段')" :colon="false" labelAlign="left">
              <BindApiColumns
                v-model:value="item.id"
                :apiColumns="data.info.config.apiColumns"
                @change="resetDisplay"
              />
            </a-form-item>
            <a-form-item :label="t('宽度')" :colon="false" labelAlign="left">
              <a-input-number v-model:value="item.width">
                <template #addonAfter>%</template>
              </a-input-number>
            </a-form-item>
            <a-form-item :label="t('对齐')" :colon="false" labelAlign="left">
              <a-select
                v-model:value="item.align"
                style="width: 100%"
                :options="alignTypeOptions"
              />
            </a-form-item>
            <div class="close-icon">
              <Icon
                icon="ant-design:close-circle-outlined"
                color="#ff8080"
                :size="20"
                @click="deleteItem(index)"
              />
            </div>
          </div>
        </div>
        <a-form-item>
          <a-button type="primary" @click="add">{{ t('添加') }}</a-button>
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="3" :header="t('交互设置')">
        <a-form-item :label="t('跳转功能')" :colon="false" labelAlign="left">
          <JumpMenu
            v-model:value="data.info.config.jumpId"
            @path="
              (path) => {
                data.info.config.path = path;
              }
            "
          />
        </a-form-item>
        <!-- <a-form-item>
          <a-button>设置跳转条件</a-button>
        </a-form-item> -->
      </a-collapse-panel>
      <a-collapse-panel key="4" :header="t('大小定位')">
        <Location v-model:info="data.info" />
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import ApiSelect from './ApiSelect.vue';
  import Icon from '/@/components/Icon/index';
  import { JumpMenu } from '/@/components/MenuSelect';
  import { informationInfo } from '../config/info';
  import { AlignType } from '/@/enums/desktop';
  import Location from './collapse/Location.vue';
  import BindApiColumns from './collapse/BindApiColumns.vue';
  import { InformationInfo } from '/@/model/desktop/designer';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: InformationInfo;
    }>(),
    {
      info: () => {
        return informationInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    show: boolean;
    info: InformationInfo;
    renderKey: number;
  } = reactive({
    show: false,
    info: informationInfo,
    renderKey: 0,
  });
  const activeKey = ref(['1', '2', '3', '4']);
  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  function saveApiConfig() {
    resetApiData();
    resetDisplay();
  }
  function resetApiData() {
    data.info.config.apiData = {};
    data.info.config.apiColumns = [];
    data.info.config.columns = [];
  }
  function add() {
    data.info.config.columns.push({
      id: '',
      name: '',
      width: 25,
      align: AlignType.LEFT,
    });
  }
  function deleteItem(index: number) {
    data.info.config.columns.splice(index, 1);
    resetDisplay();
  }
  function resetDisplay() {
    if (data.info.config.renderKey >= 0) {
      data.info.config.renderKey++;
    }
  }
  const exampleStr = ` {
      code: 0,
      msg: 'success',
      data: {
        columns: [
          {prop:'label',label:'测试1'},
          {prop:'value',label:'测试2'}
        ],
        list: [
          {label:'demo1',value:10},
          {label:'demo2',value:30}
        ],
      },
    }`;
  const alignTypeOptions = [
    {
      label: t('靠左'),
      value: AlignType.LEFT,
    },
    {
      label: t('居中'),
      value: AlignType.CENTER,
    },
    {
      label: t('靠右'),
      value: AlignType.RIGHT,
    },
  ];
</script>

<style lang="less" scoped>
  .card-box {
    .card-item {
      position: relative;
      margin: 10px 0;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
    }

    .close-icon {
      position: absolute;
      top: -10px;
      right: -6px;
    }
  }
</style>
