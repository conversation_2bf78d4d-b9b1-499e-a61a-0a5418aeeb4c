<template>
  <div class="basic-box">
    <div class="form-box">
      <a-form :model="data.formState" :label-col="labelCol">
        <a-form-item :label="t('编号')" :rules="[{ required: true, message: t('请填写编号!') }]">
          <a-input v-model:value="data.formState.code" />
        </a-form-item>
        <a-form-item :label="t('名称')" :rules="[{ required: true, message: t('请填写名称!') }]">
          <a-input v-model:value="data.formState.name" />
        </a-form-item>
        <a-form-item :label="t('图标')" required v-if="data.formState.isMenu">
          <IconPicker v-model:value="data.formState.icon" />
        </a-form-item>
        <a-form-item :label="t('上级')" required v-if="data.formState.isMenu">
          <MenuSelect
            v-model:value="data.formState.parentId"
            @change="
              (val, node) => {
                data.formState.parentId = val;
                data.formState.systemId = node.systemId;
              }
            "
          />
        </a-form-item>
        <a-form-item :label="t('排序')" v-if="data.formState.isMenu">
          <a-input-number v-model:value="data.formState.sortCode" :min="0" style="width: 100%" />
        </a-form-item>
        <a-form-item :label="t('菜单')">
          <a-switch v-model:checked="data.formState.isMenu" :checkedValue="1" :unCheckedValue="0" />
        </a-form-item>
        <!-- <a-form-item :label="t('首页')">
          <a-switch
            v-model:checked="data.formState.isFirst"
            :checkedValue="1"
            :unCheckedValue="0"
            :disabled="disabledHome"
          />
        </a-form-item> -->
        <a-form-item :label="t('描述')">
          <a-textarea v-model:value="data.formState.remark" :rows="8" />
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive } from 'vue';
  import { IconPicker } from '/@/components/Icon';
  import { MenuSelect } from '/@/components/MenuSelect';
  import { message } from 'ant-design-vue';
  import { DesktopBasicData } from '/@/model/desktop/designer';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const labelCol = { style: { width: '70px' } };
  const props = withDefaults(
    defineProps<{
      basicData: DesktopBasicData;
    }>(),
    {
      basicData: () => {
        return {
          code: '', //编码
          name: '', //名称
          icon: '', //图标
          parentId: '', //上级
          sortCode: 0, //排序
          isFirst: 0, //首屏
          isMenu: 0, //菜单
          enabledMark: 0, //状态
          remark: '', //描述
          backgroundUrl: '', //封面图片
          systemId: '', // 系统主键
        };
      },
    },
  );
  const data: {
    formState: DesktopBasicData;
  } = reactive({
    formState: {
      code: '', //编码
      name: '', //名称
      icon: '', //图标
      parentId: '', //上级
      sortCode: 0, //排序
      isFirst: 0, //首屏
      isMenu: 0, //菜单
      enabledMark: 0, //状态
      remark: '', //描述
      backgroundUrl: '', //封面图片
      systemId: '', // 系统主键
    },
  });
  // const disabledHome = ref(false);
  onMounted(() => {
    data.formState = props.basicData;
    // if (props.basicData.isFirst === 1) {
    //   disabledHome.value = true;
    // }
  });

  async function validateForm() {
    if (data.formState.code == '') {
      message.error(t('请填写编号!'));
      return false;
    }
    if (data.formState.name == '') {
      message.error(t('请填写名称!'));
      return false;
    }
    if (data.formState.isMenu && data.formState.icon == '') {
      message.error(t('请选择图标'));
      return false;
    }
    if (
      data.formState.isMenu &&
      (data.formState.parentId == '' || data.formState.parentId == undefined)
    ) {
      message.error(t('请选择上级'));
      return false;
    }
    return true;
  }
  function getBasicData() {
    return data.formState;
  }
  defineExpose({
    validateForm,
    getBasicData,
  });
</script>

<style lang="less" scoped>
  .basic-box {
    height: 100%;
    padding: 24px;
  }

  .form-box {
    height: calc(100vh - 114px);
    width: 100%;
    max-width: 794px;
    overflow: hidden auto;
    background-color: #fff;
    border-radius: 4px;
    margin: auto;
    padding: 24px;
  }
</style>
