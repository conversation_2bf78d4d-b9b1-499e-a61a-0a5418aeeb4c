<template>
  <div class="box" v-if="data.show">
    <div class="item-title">{{ props.title }}</div>
    <div v-if="config.functions.length > 0" class="menu-box">
      <div class="menu-item" v-for="(item, index) in config.functions" :key="index">
        <router-link :to="item.path || ''" class="menu-link">
          <div class="menu-icon" v-if="item.path">
            <div class="bg" :style="'background-color:' + item.color + ''"></div>
            <Icon :icon="item.icon" :size="36" :color="item.color" class="icon" />
          </div>
          <span class="menu-title">{{ item.name }}</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import Icon from '/@/components/Icon/index';
  import { modulesProperties } from '../config/properties';
  import { DesktopComponent } from '/@/enums/desktop';
  import { ModulesConfig } from '/@/model/desktop/designer';
  import { onMounted, reactive } from 'vue';
  const props = withDefaults(
    defineProps<{
      type: DesktopComponent;
      w: number;
      h: number;
      title: string;
      config: ModulesConfig;
    }>(),
    {
      type: DesktopComponent.DEFAULT,
      w: 0,
      h: 0,
      title: '',
      config: () => {
        return modulesProperties;
      },
    },
  );
  const data: {
    show: boolean;
  } = reactive({
    show: false,
  });
  onMounted(() => {
    data.show = true;
  });
</script>

<style lang="less" scoped>
  .box {
    padding-top: 40px;

    .item-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      color: #262626;
      font-size: 14px;
      padding-left: 16px;
    }

    .item-title::after {
      content: '';
      display: block;
      position: absolute;
      height: 1px;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #f0f0f0;
    }

    .menu-box {
      display: flex;
      align-items: center;
      padding: 10px;

      .menu-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 0 8px;

        .menu-link {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .menu-icon {
          position: relative;
          height: 64px;
          display: flex;
          justify-content: center;
          align-items: center;

          .bg {
            border-radius: 50%;
            opacity: 0.1;
            height: 64px;
            width: 64px;
            position: relative;
          }

          .icon {
            position: absolute;
            top: 0;
            left: 0;
            height: 64px;
            width: 64px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        .menu-title {
          color: #262626;
          font-size: 14px;
          margin: 2px 8px;
        }
      }
    }
  }
</style>
