<template>
  <BasicForm @register="registerForm" @submit="handleSubmit" @reset="resetSchema" />
</template>

<script setup lang="ts">
  import { FormSchema } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FieldMapToTime } from '/@/components/Form/src/types/form';
  const props = withDefaults(
    defineProps<{
      formSchema: Array<FormSchema>;
      fieldMapToTime: FieldMapToTime;
    }>(),
    {
      formSchema: () => {
        return [];
      },
      fieldMapToTime: () => {
        return [];
      },
    },
  );
  const emits = defineEmits(['search']);
  const [registerForm] = useForm({
    schemas: props.formSchema,
    fieldMapToTime: props.fieldMapToTime,
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: false,
  });
  async function handleSubmit(formData) {
    try {
      emits('search', formData);
    } catch (error) {}
  }
  function resetSchema() {
    emits('search', null);
  }
</script>

<style lang="less" scoped>
  :deep(.text-right) {
    position: absolute;
    top: -36px;
  }

  :deep(.ant-form-item-control-input-content) {
    display: flex;
  }
</style>
