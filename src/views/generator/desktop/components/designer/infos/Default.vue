<template>
  <div class="box">
    <div class="item-title">{{ props.title }}</div>
    <div ref="chartRef" class="item"></div>
  </div>
</template>

<script setup lang="ts">
  import { defaultProperties } from '../config/properties';
  import { DesktopComponent } from '/@/enums/desktop';
  import { DesktopConfig } from '/@/model/desktop/designer';
  const props = withDefaults(
    defineProps<{
      type: DesktopComponent;
      w: number;
      h: number;
      title: string;
      config: DesktopConfig;
    }>(),
    {
      type: DesktopComponent.DEFAULT,
      w: 0,
      h: 0,
      config: () => {
        return defaultProperties;
      },
    },
  );
</script>

<style lang="less" scoped>
  .box {
    padding-top: 40px;

    .item-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      color: #262626;
      font-size: 14px;
      padding-left: 16px;
    }

    .item-title::after {
      content: '';
      display: block;
      position: absolute;
      height: 1px;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #f0f0f0;
    }

    .item {
      width: 100%;
      height: 100%;
    }
  }
</style>
