<template>
  <div class="menu">
    <div class="line"></div>
    <!-- 预览图标 -->
    <div placement="right">
      <div class="menu-item" @click="$emit('preview')">
        <IconFontSymbol icon="icon1" fontSize="26" />
        <div class="menu-text">
          <span>{{ t('预览') }}</span>
        </div>
      </div>
    </div>

    <div class="line"></div>
    <!-- 基础配置 -->
    <div
      class="menu-item"
      v-for="(item, index) in basicComponents"
      :key="index"
      @click="add(item.type)"
    >
      <IconFontSymbol :icon="item.icon" fontSize="24" />
      <div class="menu-text">
        <span>{{ item.label }}</span>
      </div>
    </div>
    <div class="line"></div>
    <!-- 图 -->
    <div
      class="menu-item"
      v-for="(item, index) in chartComponents"
      :key="index"
      @click="add(item.type)"
    >
      <IconFontSymbol :icon="item.icon" fontSize="24" />
      <div class="menu-text">
        <span>{{ item.label }}</span>
      </div>
    </div>
    <div class="line"></div>
    <!-- 系统任务-->
    <div
      class="menu-item"
      v-for="(item, index) in systemComponents"
      :key="index"
      @click="add(item.type)"
    >
      <IconFontSymbol :icon="item.icon" fontSize="24" />
      <div class="menu-text">
        <span>{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  import { basicComponents, chartComponents, systemComponents } from './config/components';
  import { DesktopComponent } from '/@/enums/desktop';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let emits = defineEmits(['add', 'preview']);
  function add(type: DesktopComponent) {
    console.log('type: ', type);
    emits('add', type);
  }
</script>

<style lang="less" scoped>
  .menu {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .menu-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin: 4px 0;

    .icon {
      color: @primary-color;
    }
  }

  .menu-text {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #39f;
    margin: 4px 0;
    font-size: 12px;
  }

  .line {
    width: 40px;
    height: 1px;
    margin: 8px 4px;
    background-color: #f0f0f0;
  }
</style>
