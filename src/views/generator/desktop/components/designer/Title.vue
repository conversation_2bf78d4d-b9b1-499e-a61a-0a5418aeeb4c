<template>
  <div class="item-title" v-if="title">{{ title }}</div>
</template>

<script setup lang="ts">
  withDefaults(
    defineProps<{
      title: string | null;
    }>(),
    {
      title: null,
    },
  );
</script>

<style lang="less" scoped>
  .item-title {
    position: fixed;
    top: 60px;
    right: 30px;
    width: calc(25% - 30px);
    height: 44px;
    line-height: 46px;
    color: #262626;
    font-size: 16px;
    padding-left: 8px;
    background-color: #fff;
  }

  .item-title::after {
    content: '';
    display: block;
    position: absolute;
    height: 1px;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #f0f0f0;
  }
</style>
