<template>
  <DesktopLayout>
    <template #head>
      <DesignHead :title="t('新增桌面设计')">
        <template #steps
          ><a-steps :current="current">
            <a-step v-for="item in steps" :key="item.title" :title="item.title" />
          </a-steps>
        </template>
        <template #buttons>
          <div class="design-button">
            <a-button :disabled="current == 0" class="common-button" @click="prev">{{
              t('上一步')
            }}</a-button>
            <a-button :disabled="current == steps.length - 1" class="common-button" @click="next">{{
              t('下一步')
            }}</a-button>
            <a-button class="common-button" @click="$emit('hold')">{{ t('暂存') }}</a-button>
            <a-button
              type="primary"
              :disabled="current == 0"
              class="common-button"
              @click="$emit('save')"
              >{{ t('保存') }}</a-button
            >
            <a-button type="primary" class="clean-icon" @click="$emit('close')">{{
              t('关闭')
            }}</a-button>
          </div></template
        >
      </DesignHead></template
    >
    <Basic v-if="current == 0" />
    <Design v-if="current == 1" />
  </DesktopLayout>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import DesignHead from './layout/Head.vue';
  import DesktopLayout from './layout/Desktop.vue';
  import Design from './Design.vue';
  import Basic from './Basic.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  defineEmits(['save', 'close', 'hold']);
  const steps = [
    {
      title: t('基本配置'),
      content: t('基本配置'),
    },
    {
      title: t('桌面设计'),
      content: t('桌面设计'),
    },
  ];
  const current = ref<number>(0);
  const next = () => {
    current.value++;
  };
  const prev = () => {
    current.value--;
  };
</script>

<style lang="less" scoped>
  .head {
    height: 56px;
    width: 100%;
    box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
  }

  .design-containers {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: #f5f5f5;
  }

  [data-theme='dark'] .flow-containers {
    background-color: #151515;
  }

  .common-button {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .clean-icon {
    background-color: @clear-color;
    border-color: @clear-color;
  }

  .layout {
    position: relative;
    width: 100%;
    height: 100%;
    top: 56px;
  }

  :deep(.ant-steps) {
    width: 280px;
  }

  .design-button {
    display: flex;
    margin: 0 20px;
  }
</style>
