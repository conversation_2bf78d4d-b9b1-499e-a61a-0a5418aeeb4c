import {
  DashboardShowType,
  DesktopComponent,
  ImgType,
  LegendType,
  OrientType,
  RadarShapeType,
  JumpToPathType,
  ButtonType,
  TableType,
  ExecuteType,
  AlignType,
} from '/@/enums/desktop';
import { FormTypeEnum } from '/@/enums/formtypeEnum';
import {
  ButtonItem,
  ChartBarConfig,
  ChartLegend,
  DesktopConfig,
  PieConfig,
} from '/@/model/desktop/designer';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
// 默认属性
export const defaultProperties = {
  title: '',
  renderKey: 0,
};
export const legendProperties: ChartLegend = {
  show: true,
  orient: OrientType.HORIZONTAL,
  left: 'center',
  top: 'bottom',
  right: 'auto',
  bottom: 'bottom',
  padding: 8,
  icon: LegendType.ROUND_RECT,
  itemWidth: 25,
  itemHeight: 14,
  textStyle: {
    color: '#000',
  },
  formatter: '{name}',
};

//  数据视图属性
export const apiConfigInfo = {
  apiData: {},
  apiColumns: [],
  apiConfig: {
    id: '',
    name: '',
    method: '',
    path: '',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};
//  数据视图属性
export const dataViewInfo = {
  labelKey: '',
  valueKey: '',
};
//
//数据面板
export const dashboardProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  labelKey: 'name',
  valueKey: 'value',
  title: t('数据面板'),
  title1: '',
  title2: '',
  showType: DashboardShowType.ONE,
  flagKey: 'isUp', //标记
  flagIsUp: -1, //标记值
  mainIndex: 'value', //主指标
  mainIndexValue: 0, //主指标值
  mainIndex2: '', //主指标2
  mainIndex2Value: 0, //主指标2值
  secondaryIndex: 'add', //副指标
  secondaryIndexValue: 0, //副指标值
  jumpId: '',
  path: '',
  numColor: '',
  des: undefined,
  imgType: ImgType.DEFAULT,
  imgIcon: '',
  imgIconColor: '',
  pictureUrl: '',
  apiConfig: {
    id: '456925ab7ad348329ae3d4995646bcd6',
    method: 'GET',
    name: '数据面板',
    path: 'DesktopDesign/data-panel',
    requestParamsConfigs: [],
    requestHeaderConfigs: [],
    requestBodyConfigs: [],
  },
};

//信息列表
export const informationProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  title: t('信息列表'),
  jumpId: '',
  path: '',
  maxRows: 10,
  apiColumns: [],
  columns: [
    { id: 'name', width: 25, align: AlignType.LEFT },
    { id: 'quantity', width: 25, align: AlignType.LEFT },
    { id: 'price', width: 25, align: AlignType.LEFT },
    { id: 'unitPrice', width: 25, align: AlignType.LEFT },
  ],
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [],
    requestHeaderConfigs: [],
    requestBodyConfigs: [],
  },
};

//折线/柱状图
export const chartLineIndicatorConfig = {
  title: '',
  type: 'line',
  color: '',
  name: '',
  value: '',
  showAreaStyle: false, //是否显示面积图
  gradualStartColor: '',
  gradualEndColor: '',
};
export const chartLineDataListsConfig = {
  title: '',
  valueKey: '',
  apiColumns: [],
  apiData: [],
  total: 0,
  indicator: [chartLineIndicatorConfig],
  apiConfig: {
    id: '',
    name: '',
    method: '',
    path: '',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};
export const chartLineSeriesConfig = {
  name: '',
  type: 'line',
  stack: '',
  smooth: false,
  areaStyle: {},
  label: {
    show: false,
    color: '#000000',
    position: 'outside',
    fontWeight: 'normal',
    fontSize: 12,
    formatter: null,
  },
  data: [],
  showAreaStyle: false,
  gradualEndColor: '',
  gradualStartColor: '',
};
export const chartLineEchartsConfig = {
  color: [],
  legend: {
    data: [],
  },
  tooltip: {
    trigger: 'axis',
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisLine: {
        show: true,
        lineStyle: {
          color: '',
        },
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: '',
        },
      },
    },
  ],
  series: [chartLineSeriesConfig],
};
export const chartLineYAxis = {
  name: '',
  nameLocation: 'end',
  nameTextStyle: {
    color: '',
    fontSize: 12,
    fontWeight: 'normal',
  },
  min: 'dataMin',
  max: 'dataMax',
  interval: null,
  position: 'left',
  type: 'value',
  axisLabel: {
    formatter: '{value}',
    color: '',
  },
  axisLine: {
    show: true,
    lineStyle: {
      color: '',
    },
  },
};
export const chartLineProperties = {
  ...defaultProperties,
  title: t('折线/柱状图'),
  condition: {
    color: '',
    selected: '',
  },
  legend: {
    ...legendProperties,
  },
  // 统计
  count: {
    show: false,
    unit: '',
    title: '',
  },
  dataList: [
    {
      title: '',
      valueKey: 'name',
      apiColumns: [],
      apiData: [],
      total: 0,
      indicator: [
        {
          title: '',
          type: 'line',
          color: '',
          name: '',
          value: 'quantity',
          showAreaStyle: false, //是否显示面积图
          gradualStartColor: '',
          gradualEndColor: '',
        },
      ],
      apiConfig: {
        id: '********************************',
        method: 'GET',
        name: '图表数据',
        path: 'DesktopDesign/chart',
        requestParamsConfigs: [], //Query Params
        requestHeaderConfigs: [], //Header
        requestBodyConfigs: [], //Body
      },
    },
  ],
  bar: {
    stack: false, //是否显示堆叠
    label: {
      show: true,
      color: '',
    },
  },
  line: {
    smooth: false,
    stack: false, //是否显示堆叠
    showAreaStyle: false, //是否显示面积图
    gradualStartColor: '',
    gradualEndColor: '',
    showSymbol: true,
  },
  label: {
    show: false,
    color: '#000000',
    position: 'outside',
    fontWeight: 'normal',
    fontSize: 12,
    formatter: '',
  },
  yAxis: [chartLineYAxis],
  xAxis: [
    {
      position: 'bottom',
      name: '',
      nameLocation: 'end',
      nameTextStyle: {
        color: '',
        fontSize: 12,
        fontWeight: 'normal',
      },
      type: 'category',
      axisLabel: {
        formatter: '{value}',
        color: '',
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '',
        },
      },
      data: [],
    },
  ],
  echarts: [chartLineEchartsConfig],
  isCrosswise: false,
};
//图
export const chartProperties: PieConfig = {
  ...defaultProperties,
  ...apiConfigInfo,
  ...dataViewInfo,
  colors: [],
  title: t('饼图'),
  echarts: {
    alignTicks: true,
  },
};
//饼图
export const pieProperties: PieConfig = {
  ...defaultProperties,
  ...apiConfigInfo,
  colors: [],
  title: t('饼图'),
  autoWidth: true,
  labelKey: 'name',
  valueKey: 'quantity',
  echarts: {
    alignTicks: true,
    legend: {
      ...legendProperties,
      width: 'auto',
    },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        type: 'pie',
        roseType: undefined,
        selectedMode: undefined,
        radius: ['0%', '50%'],
        center: ['50%', '50%'],
        label: {
          show: true,
          color: '#000000',
          position: 'outside',
          fontWeight: 'normal',
          fontSize: 12,
          formatter: '{b}: {d}',
        },
        itemStyle: {
          borderRadius: 0,
        },
        data: [],
      },
    ],
  },
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};

//雷达图
export const radarProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  title: t('雷达图'),
  colors: [],
  labelKey: 'name',
  echarts: {
    alignTicks: true,
    legend: {
      data: [],
      ...legendProperties,
      formatter: '',
    },
    radar: {
      radius: 100,
      shape: RadarShapeType.CIRCLE,
      indicator: [
        { value: 'quantity', name: '指标1' },
        { value: 'price', name: '指标2' },
        { value: 'unitPrice', name: '指标3' },
      ],
    },
    showAreaStyle: true,
    series: [
      {
        type: 'radar',
        itemStyle: {
          borderRadius: 0,
        },
        label: {
          show: false,
          color: '#000000',
          position: 'outside',
          fontWeight: 'normal',
          fontSize: 12,
          formatter: '',
        },
        symbol: LegendType.CIRCLE,
        areaStyle: {},
        data: [
          {
            name: t('指标1'),
            value: [],
          },
          {
            name: t('指标2'),
            value: [],
          },
          {
            name: t('指标3'),
            value: [],
          },
        ],
      },
    ],
  },
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};

//仪表盘

export const gaugeProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  title: t('仪表盘'),
  valueKey: 'quantity',
  echarts: {
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c}',
    },
    series: [
      {
        name: t('仪表盘'),
        type: 'gauge',
        progress: {
          show: true,
        },
        data: [
          {
            name: t('仪表盘'),
            value: 0,
          },
        ],
      },
    ],
  },
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};

//漏斗图

export const funnelProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  title: t('漏斗图'),
  labelKey: 'name',
  valueKey: 'price',
  colors: [],
  autoWidth: true,
  echarts: {
    alignTicks: true,
    legend: {
      ...legendProperties,
      width: 'auto',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c}',
    },
    series: [
      {
        name: '',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid',
          },
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
        },
        emphasis: {
          label: {
            fontSize: 20,
          },
        },
        data: [],
      },
    ],
  },
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};

//柱状百分比

export const chartBarProperties: ChartBarConfig = {
  ...defaultProperties,
  ...apiConfigInfo,
  labelKey: 'name',
  valueKey: 'unitPrice',
  targetKey: 'price', //目标值
  title: t('柱状百分比'),
  unit: t('万元'),
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};
// 甘特图
export const categoryStackProperties = {
  ...defaultProperties,
  ...apiConfigInfo,
  labelKey: 'name',
  valueKey: 'unitPrice',
  targetKey: 'price', //目标值
  title: t('甘特图'),
  echarts: {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow', // 'shadow' as default; can also be 'line' or 'shadow'
      },
    },
    legend: {},
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: [],
    },
    series: [],
  },
  apiConfig: {
    id: '********************************',
    method: 'GET',
    name: '图表数据',
    path: 'DesktopDesign/chart',
    requestParamsConfigs: [], //Query Params
    requestHeaderConfigs: [], //Header
    requestBodyConfigs: [], //Body
  },
};
//我的任务

export const myTaskProperties = {
  ...defaultProperties,
  title: t('我的任务'),
};

//待办事项

export const todoListProperties = {
  ...defaultProperties,
  title: t('待办事项'),
  maxRows: 2,
  path: JumpToPathType.PROCESSTASKS,
};

//常用功能

export const modulesProperties = {
  ...defaultProperties,
  title: t('常用功能'),
  functions: [],
};
// 按钮组件
export const createButtonConfig: ButtonItem = {
  name: '新增数据',
  type: ButtonType.CREATE,
  formId: '',
  formName: '',
  processEvent: [],
};
export const searchButtonConfig: ButtonItem = {
  name: '查询数据',
  type: ButtonType.SEARCH,
  apiColumn: [],
  apiConfig: {
    id: '',
    name: '',
    method: '',
    path: '',
    requestParamsConfigs: [],
    requestHeaderConfigs: [],
    requestBodyConfigs: [], //Body
  },
  processEvent: [],
};
export const executeButtonConfig: ButtonItem = {
  name: '执行按钮',
  type: ButtonType.EXECUTE,
  processEvent: [
    {
      operateType: ExecuteType.API,
      operateConfig: {
        rule: '',
        js: '',
        apiConfig: {
          id: '',
          name: '',
          method: '',
          path: '',
          requestParamsConfigs: [], //Query Params
          requestHeaderConfigs: [], //Header
          requestBodyConfigs: [], //Body
        },
      },
    },
  ],
};
export const buttonProperties = {
  ...defaultProperties,
  title: t('按钮组件'),
  buttons: [createButtonConfig, searchButtonConfig, executeButtonConfig],
};
export const tableDefaultColumnItem = {
  dataIndex: '',
  title: '',
  show: true,
  listStyle: '',
  width: 80,
  align: 'left',
};
export const tableProperties = {
  ...defaultProperties,
  title: t('表格组件'),
  type: TableType.API,
  formId: '',
  formName: '',
  formType: FormTypeEnum.CUSTOM_FORM,
  systemComponent: {
    functionalModule: '',
    functionName: '',
    functionFormName: 'Form',
  },
  apiColumns: [],
  columns: [
    {
      dataIndex: 'name',
      title: '名称',
      show: true,
      width: 80,
      align: 'left',
    },
    {
      dataIndex: 'quantity',
      title: '数量',
      show: true,
      width: 80,
      align: 'left',
    },
    {
      dataIndex: 'price',
      title: '价格',
      show: true,
      width: 80,
      align: 'left',
    },
    {
      dataIndex: 'unitPrice',
      title: '单价',
      show: true,
      width: 80,
      align: 'left',
    },
  ],
  releaseId: '',
  updateIds: [],
  primaryKey: '',
  associatedForm: true,
  pageSize: 5,
  apiConfig: {
    id: '271e22922d9843d8badd270c685636cd',
    method: 'GET',
    name: '列表表格数据',
    path: 'DesktopDesign/list-chart',
    requestParamsConfigs: [
      {
        assignmentType: 'value',
        config: '',
        dataType: 'Integer',
        name: 'limit',
        value: '1',
      },
      {
        assignmentType: 'value',
        config: '',
        dataType: 'Integer',
        name: 'size',
        value: '5',
      },
    ],
    requestHeaderConfigs: [],
    requestBodyConfigs: [],
  },
};
export const tablePanesItem = {
  title: 'New Tab',
  key: 'newTab0',
  type: TableType.API,
  closable: true,
  formId: '',
  formName: '',
  formType: FormTypeEnum.CUSTOM_FORM,
  systemComponent: {
    functionalModule: '',
    functionName: '',
    functionFormName: 'Form',
  },
  apiColumns: [],
  columns: [
    {
      dataIndex: 'name',
      title: '名称',
      show: true,
      width: 80,
      align: 'left',
    },
    {
      dataIndex: 'quantity',
      title: '数量',
      show: true,
      width: 80,
      align: 'left',
    },
    {
      dataIndex: 'price',
      title: '价格',
      show: true,
      width: 80,
      align: 'left',
    },
    {
      dataIndex: 'unitPrice',
      title: '单价',
      show: true,
      width: 80,
      align: 'left',
    },
  ],
  releaseId: '',
  updateIds: [],
  primaryKey: '',
  associatedForm: true,
  pageSize: 5,
  current: 1,
  total: 0,
  count: false,
  renderKey: 0,
  apiConfig: {
    id: '271e22922d9843d8badd270c685636cd',
    method: 'GET',
    name: '列表表格数据',
    path: 'DesktopDesign/list-chart',
    requestParamsConfigs: [
      {
        assignmentType: 'value',
        config: '',
        dataType: 'Integer',
        name: 'limit',
        value: '1',
      },
      {
        assignmentType: 'value',
        config: '',
        dataType: 'Integer',
        name: 'size',
        value: '5',
      },
    ],
    requestHeaderConfigs: [],
    requestBodyConfigs: [],
  },
};
export const TabsTableProperties = {
  ...defaultProperties,
  title: t('多表格组件'),
  panes: [tablePanesItem],
};
export const ImageProperties = {
  ...defaultProperties,
  title: t('图片'),
  showTitle: true,
  renderKey: 0,
  folderId: '',
};
export const MapProperties = {
  ...defaultProperties,
  title: t('地图'),
  showTitle: true,
  renderKey: 0,
};
// 获取组件属性
export const propertiesByType: Map<DesktopComponent, DesktopConfig> = new Map([
  [DesktopComponent.DASHBOARD, dashboardProperties],
  [DesktopComponent.INFORMATION, informationProperties],
  [DesktopComponent.CHARTLINE, chartLineProperties],
  [DesktopComponent.PIE, pieProperties],
  [DesktopComponent.RADAR, radarProperties],
  [DesktopComponent.GAUGE, gaugeProperties],
  [DesktopComponent.FUNNEL, funnelProperties],
  [DesktopComponent.CATEGORY_STACK, categoryStackProperties],
  [DesktopComponent.CHARTBAR, chartBarProperties],
  [DesktopComponent.MYTASK, myTaskProperties],
  [DesktopComponent.TODOLIST, todoListProperties],
  [DesktopComponent.MODULES, modulesProperties],
  [DesktopComponent.BUTTON, buttonProperties],
  [DesktopComponent.TABLE, tableProperties],
  [DesktopComponent.TABSTABLE, TabsTableProperties],
  [DesktopComponent.IMAGE, ImageProperties],
  [DesktopComponent.MAP, MapProperties],
]);
