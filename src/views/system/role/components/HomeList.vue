<template>
  <PageLayout
    :hasOperationSlot="false"
    :searchConfig="searchConfig"
    @search="search"
    @scroll-height="scrollHeight"
  >
    <template #search> </template>
    <template #right>
      <div v-if="data.dataSource.length > 0">
        <a-row
          :gutter="16"
          class="list-box"
          :style="{ overflowY: 'auto', maxHeight: tableOptions.scrollHeight + 70 + 'px' }"
          :key="data.renderKey"
        >
          <a-col :span="6" class="item" v-for="(item, index) in data.dataSource" :key="index">
            <div class="image"><img :src="item.backgroundUrl" /></div>

            <div class="main">
              <a-checkbox v-model:checked="item.checked" @change="handleCheck(item)">{{
                item.name
              }}</a-checkbox>
            </div>
          </a-col>
        </a-row>
        <div class="page-box">
          <a-pagination
            v-model:current="data.pagination.current"
            :pageSize="data.pagination.pageSize"
            :total="data.pagination.total"
            show-less-items
            @change="getList"
          />
        </div>
      </div>
      <div v-else>
        <EmptyBox />
      </div>
    </template>
  </PageLayout>
</template>
<script lang="ts" setup>
  import { onMounted, reactive, watch } from 'vue';

  import { DesktopAuthPage } from '/@/api/desktop/model';
  import { PageLayout, EmptyBox } from '/@/components/ModalPanel';
  import userTableScrollHeight from '/@/hooks/setting/userTableScrollHeight';

  import { getRolePageList } from '/@/api/desktop';

  import { useI18n } from '/@/hooks/web/useI18n';
  const props = defineProps({
    checkedIds: { type: Array as PropType<String[]> },
  });
  const { t } = useI18n();
  const emit = defineEmits(['checked']);
  watch(
    () => props.checkedIds,
    () => {
      checkItems();
    },
    { deep: true },
  );
  const searchConfig = [
    {
      field: 'keyword',
      label: t('关键字'),
      type: 'input',
    },
  ];
  const { tableOptions, scrollHeight } = userTableScrollHeight();
  let data: {
    dataSource: Array<DesktopAuthPage>;
    renderKey: number;
    keyword: string;
    pagination: {
      current: number;
      total: number;
      pageSize: number;
    };
  } = reactive({
    dataSource: [],
    renderKey: 0,
    pagination: { current: 1, total: 0, pageSize: 8 },
    keyword: '',
  });
  onMounted(() => {
    data.pagination.current = 1;
    data.dataSource = [];
    getList();
  });
  async function getList() {
    let params = {
      limit: data.pagination.current,
      size: data.pagination.pageSize,
      keyword: data.keyword,
    };
    let res = await getRolePageList(params);
    data.dataSource = res.list;
    data.pagination.total = res.total;
    data.renderKey++;
    checkItems();
  }
  function checkItems() {
    data.dataSource.forEach((o) => {
      o.checked = false;
      if (props.checkedIds?.includes(o.id)) {
        o.checked = true;
      }
    });
  }
  function search(params?: any) {
    data.keyword = params.keyword;
    data.pagination.current = 1;
    data.dataSource = [];
    getList();
  }
  function handleCheck(item) {
    emit('checked', item);
  }
</script>
<style lang="less" scoped>
  .list-box {
    display: flex;
    flex-wrap: wrap;
    margin: 10px -8px;

    .ant-row {
      margin: 0 !important;
    }

    .item {
      position: relative;
      margin-bottom: 16px;
      overflow: hidden;
      border-radius: 4px;
    }

    .image {
      width: 100%;
      height: 140px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .main {
      font-size: 12px;
      text-align: center;
      line-height: 40px;
    }
  }

  .page-box {
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
</style>
