<template>
  <PageWrapper dense fixedHeight contentFullHeight>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="'subSystem:add'" @click="handleCreate">
          {{ t('新增') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                auth: 'subSystem:edit',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                auth: 'subSystem:delete',
                color: 'error',
                ifShow: record.code !== 'chinese',
                popConfirm: {
                  title: t('是否确认删除？'),
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <LgTypeDrawer @register="registerDrawer" @success="handleSuccess" />
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';

  import { BasicTable, useTable, TableAction, BasicColumn, FormSchema } from '/@/components/Table';

  import { useModal } from '/@/components/Modal';
  import LgTypeDrawer from './components/systemModal.vue';

  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { usePermission } from '/@/hooks/web/usePermission';

  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useAppInject } from '/@/hooks/web/useAppInject';
  import { PageWrapper } from '/@/components/Page';
  import { getSubSystemPage, deleteSubSystem } from '/@/api/system/subSystem';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export const columns: BasicColumn[] = [
    {
      title: t('名称'),
      dataIndex: 'name',
      align: 'left',
      resizable: true,
    },
    {
      title: t('编码'),
      dataIndex: 'code',
      align: 'left',
      resizable: true,
    },
    {
      title: t('描述'),
      dataIndex: 'description',
      align: 'left',
      resizable: true,
    },
    {
      title: t('排序'),
      dataIndex: 'sortCode',
      resizable: true,
    },
  ];

  export const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入名称或编码'),
      },
    },
  ];

  export default defineComponent({
    name: 'SubSystemManagement',
    components: { BasicTable, LgTypeDrawer, TableAction, PageWrapper },
    setup() {
      const { notification } = useMessage();
      const { getShowTopMenu } = useMenuSetting();
      const { getIsMobile } = useAppInject();
      const { hasPermission } = usePermission();

      const [registerDrawer, { openModal }] = useModal();

      const [registerTable, { reload }] = useTable({
        title: t('子系统列表'),
        api: getSubSystemPage,
        columns,
        formConfig: {
          rowProps: {
            gutter: 16,
          },
          schemas: searchFormSchema,
          showResetButton: false,
        },
        useSearchForm: true,
        showTableSetting: true,
        striped: false,
        actionColumn: {
          width: 80,
          title: t('操作'),
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
        tableSetting: {
          size: false,
        },
        customRow: (record) => {
          return {
            ondblclick: () => {
              if (hasPermission('subSystem:edit')) {
                handleEdit(record);
              }
            },
          };
        },
      });

      function handleCreate() {
        openModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        openModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        const permissionStore = usePermissionStore();

        deleteSubSystem([record.id]).then(async (_) => {
          await permissionStore.changeSubsystem(getShowTopMenu.value, getIsMobile.value);
          reload();
          notification.success({
            message: t('提示'),
            description: t('删除成功'),
          }); //提示消息
        });
      }

      function handleSuccess() {
        reload();
      }

      return {
        registerTable,
        registerDrawer,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        t,
      };
    },
  });
</script>
