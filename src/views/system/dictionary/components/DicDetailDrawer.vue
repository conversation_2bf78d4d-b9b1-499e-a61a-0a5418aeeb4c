<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    :title="getTitle"
    width="500px"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  import { defineComponent, ref, computed, unref } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';

  import { addDicDetail, updateDicDetail } from '/@/api/system/dic';

  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: t('字典名称'),
      required: true,
      title: t('基本信息'),
      component: 'Input',
      componentProps: {
        placeholder: t('请输入字典名称'),
      },
      colProps: { span: 24 },
    },
    {
      field: 'code',
      label: t('字典编码'),
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: t('请输入字典编码'),
      },
      colProps: { span: 24 },
    },
    {
      field: 'value',
      label: t('字典值'),
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: t('请输入字典值'),
      },
      colProps: { span: 24 },
    },
    {
      field: 'sortCode',
      label: t('排序'),
      component: 'InputNumber',
      componentProps: {
        style: { width: '100%' },
        placeholder: t('请输入排序号'),
      },
      colProps: { span: 24 },
    },
    {
      label: t('备注'),
      field: 'remark',
      component: 'InputTextArea',
      componentProps: {
        placeholder: t('请输入备注'),
      },
      colProps: { span: 24 },
    },
  ];

  export default defineComponent({
    name: 'DicDetailDrawer',
    components: { BasicDrawer, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const isUpdate = ref(true);
      const { notification } = useMessage();
      const rowId = ref('');
      const itemId = ref('');

      const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
        labelWidth: 90,
        schemas: formSchema,
        showActionButtonGroup: false,
      });

      const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
        resetFields();
        setDrawerProps({ confirmLoading: false });
        console.log(data);
        isUpdate.value = !!data?.isUpdate;

        if (unref(isUpdate)) {
          rowId.value = data.record.id;
          itemId.value = data.record.itemId;
          setFieldsValue({
            ...data.record,
          });
        } else {
          itemId.value = data.itemId;
        }
      });

      const getTitle = computed(() => (!unref(isUpdate) ? t('新增数据字典') : t('编辑数据字典')));

      async function handleSubmit() {
        try {
          const values = await validate();

          setDrawerProps({ confirmLoading: true });
          values.itemId = itemId.value;
          // TODO custom api
          if (!unref(isUpdate)) {
            //false 新增

            await addDicDetail(values);
            notification.success({
              message: t('提示'),
              description: t('新增数据字典成功'),
            }); //提示消息
          } else {
            values.id = rowId.value;
            await updateDicDetail(values);
            notification.success({
              message: t('提示'),
              description: t('编辑数据字典成功'),
            }); //提示消息
          }
          closeDrawer();
          emit('success');
        } catch (error) {
          setDrawerProps({ confirmLoading: false });
        }
      }

      return {
        registerDrawer,
        registerForm,
        getTitle,
        handleSubmit,
      };
    },
  });
</script>
