<template>
  <ScrollContainer>
    <PageWrapper dense contentFullHeight fixedHeight>
      <div ref="wrapperRef" class="account-setting">
        <div class="left-box">
          <div class="left-label">账号类型</div>
        </div>
        <Tabs tab-position="left" :tabBarStyle="tabBarStyle" class="h-full">
          <template v-for="item in settingList" :key="item.key">
            <TabPane :tab="item.name" class="h-full">
              <component :is="item.component" />
            </TabPane>
          </template>
        </Tabs>
      </div>
    </PageWrapper>
  </ScrollContainer>
</template>
<script lang="ts" setup>
  import { Tabs, TabPane } from 'ant-design-vue';
  import { ScrollContainer } from '/@/components/Container/index';
  import { PageWrapper } from '/@/components/Page';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { defineAsyncComponent } from 'vue';
  const Email = defineAsyncComponent(() => import('./components/Email.vue'));
  const Sms = defineAsyncComponent(() => import('./components/Sms.vue'));
  const WeCom = defineAsyncComponent(() => import('./components/WeCom.vue'));
  const DingTalk = defineAsyncComponent(() => import('./components/DingTalk.vue'));
  const WebHook = defineAsyncComponent(() => import('./components/WebHook.vue'));
  const WeChat = defineAsyncComponent(() => import('./components/WeChat.vue'));
  const { t } = useI18n();
  const tabBarStyle = {
    width: '220px',
  };
  const settingList = [
    {
      key: '1',
      name: t('邮箱配置'),
      component: Email,
    },
    {
      key: '2',
      name: t('短信配置'),
      component: Sms,
    },
    {
      key: '3',
      name: t('企业微信配置'),
      component: WeCom,
    },
    {
      key: '4',
      name: t('钉钉配置'),
      component: DingTalk,
    },
    {
      key: '5',
      name: t('WebHook配置'),
      component: WebHook,
    },
    {
      key: '6',
      name: t('微信公众号配置'),
      component: WeChat,
    },
  ];
</script>
<style lang="less" scoped>
  .account-setting {
    height: 100%;
    background-color: @component-background;
    margin-right: 7px;

    .left-box {
      height: 50px;
      border: 1px solid #f0f0f0;
      width: 220px;
      line-height: 50px;
      margin-bottom: 10px;

      .left-label {
        margin-left: 20px;
      }
    }

    .base-title {
      padding-left: 0;
    }

    .ant-tabs-tab-active {
      background-color: @item-active-bg;
    }

    :deep(.ant-tabs-content-holder),
    :deep(.ant-tabs-content) {
      height: 100%;
    }
  }

  :deep(.ant-tabs-content) {
    overflow: auto;
  }
</style>
