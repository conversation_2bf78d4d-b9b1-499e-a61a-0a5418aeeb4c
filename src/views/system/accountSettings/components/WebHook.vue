<template>
  <div class="flex flex-col items-center justify-center py-8 bg-white">
    <div class="form-box">
      <a-form
        :model="formState"
        name="basic"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        ref="FormRef"
      >
        <a-form-item
          label="WebHook地址"
          name="url"
          :rules="[{ required: true, message: '请填写WebHook地址!' }]"
        >
          <a-input v-model:value="formState.url" placeholder="请填写" />
        </a-form-item>
        <a-form-item
          label="认证类型"
          name="authType"
          :rules="[{ required: true, message: '请填写认证类型!' }]"
        >
          <a-select
            v-model:value="formState.authType"
            style="width: 100%"
            :options="authTypeOptions"
          />
        </a-form-item>
        <a-form-item
          label="请求格式"
          name="contentType"
          :rules="[{ required: true, message: '请填写请求格式!' }]"
        >
          <a-input v-model:value="formState.contentType" placeholder="请填写" />
        </a-form-item>
        <a-form-item
          label="密钥token"
          name="secret"
          :rules="[{ required: true, message: '请填写请求格式!' }]"
        >
          <a-input v-model:value="formState.secret" placeholder="请填写" />
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 4, span: 20 }">
          <a-button class="!ml-4" type="primary" @click="handleSubmit">
            {{ t('确认') }}
          </a-button>
        </a-form-item>
      </a-form>
    </div>
    <div class="flex justify-center"></div>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, reactive, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { set, getInfo } from '/@/api/system/generator/accountSettings';
  import { WebHookAuthType, MessageType } from '/@/enums/messageTemplate';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { notification } = useMessage();
  const FormRef = ref();
  const { t } = useI18n();

  interface FormState {
    url: string;
    authType: boolean;
    secret: string;
    contentType: string;
  }

  const formState = reactive<FormState>({
    url: '',
    authType: false,
    secret: '',
    contentType: '',
  });
  const authTypeOptions = [
    {
      value: WebHookAuthType.BASIC,
      label: t('BASIC认证'),
    },
    {
      value: WebHookAuthType.DIGEST,
      label: t('DIGEST认证'),
    },
    {
      value: WebHookAuthType.SSL,
      label: t('SSL认证'),
    },
    {
      value: WebHookAuthType.FORM,
      label: t('FORM认证'),
    },
  ];
  onMounted(async () => {
    let res = await getInfo(MessageType.WEB_HOOK);
    if (res && res.configJson) {
      let config = JSON.parse(res.configJson);
      if (config.url) formState.url = config.url;
      formState.authType = config.authType;
      if (config.secret) formState.secret = config.secret;
      if (config.contentType) formState.contentType = config.contentType;
    }
  });
  async function handleSubmit() {
    try {
      await FormRef.value.validate();
      await set(MessageType.WEB_HOOK, formState);
      notification.success({
        message: t('提示'),
        description: t('配置成功'),
      }); //提示消息
    } catch (error) {}
  }
</script>
<style lang="less" scoped>
  .form-box {
    width: 100%;
    padding: 0 20px;
  }
</style>
