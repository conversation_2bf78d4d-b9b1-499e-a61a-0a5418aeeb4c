<template>
  <div class="overflow-y-auto h-full overflow-x-hidden">
    <CollapseContainer :title="t('基本设置')">
      <a-row :gutter="24">
        <a-col :span="14">
          <BasicForm @register="register" />
        </a-col>
        <a-col :span="10">
          <div class="change-avatar">
            <div class="mb-2"> {{ t('头像') }} </div>
            <CropperAvatar
              :btnText="t('更换头像')"
              :uploadApi="uploadAvatar"
              :value="avatar"
              :maxSize="2"
              @change="uploadChange"
            />
          </div>
        </a-col>
      </a-row>
      <a-button type="primary" @click="handleSubmit"> {{ t('更新基本信息') }} </a-button>
    </CollapseContainer>
    <CollapseContainer :title="t('个人资料')">
      <a-row :gutter="24">
        <a-col :span="14">
          <a-descriptions :column="1">
            <a-descriptions-item
              label="所属组织"
              :contentStyle="contentStyle"
              :labelStyle="labelStyle"
            >
              <div v-for="item in departmentNameList" :key="item">{{ item }}</div>
            </a-descriptions-item>
            <a-descriptions-item
              label="所属角色"
              :contentStyle="contentStyle"
              :labelStyle="labelStyle"
            >
              <div v-for="item in roleNameList" :key="item">{{ item }}</div>
            </a-descriptions-item>
            <a-descriptions-item
              label="所属岗位"
              :contentStyle="contentStyle"
              :labelStyle="labelStyle"
            >
              <div v-for="item in postNameList" :key="item">{{ item }}</div>
            </a-descriptions-item>
          </a-descriptions>
        </a-col>
      </a-row>
    </CollapseContainer>
  </div>
</template>
<script lang="ts">
  import { Row, Col } from 'ant-design-vue';
  import { computed, defineComponent, onMounted, ref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { CollapseContainer } from '/@/components/Container/index';
  import { CropperAvatar } from '/@/components/Cropper';

  import { useMessage } from '/@/hooks/web/useMessage';
  import headerImg from '/@/assets/images/header.jpg';
  // import { accountInfoApi } from '/@/api/demo/account';
  import { baseSetschemas } from './data';
  import { useUserStore } from '/@/store/modules/user';
  import { getUserProfile, updateUserInfo, uploadAvatar } from '/@/api/system/user';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const contentStyle = { flexDirection: 'column' };
  const labelStyle = { width: '120px', textAlign: 'right', display: 'block' };
  export default defineComponent({
    components: {
      BasicForm,
      CollapseContainer,
      CropperAvatar,
      [Row.name]: Row,
      [Col.name]: Col,
    },
    setup() {
      const { createMessage } = useMessage();
      const userStore = useUserStore();
      const id = ref('');
      const departmentNameList = ref([]);
      const postNameList = ref([]);
      const roleNameList = ref([]);

      const [register, { setFieldsValue, validate }] = useForm({
        labelWidth: 120,
        schemas: baseSetschemas,
        showActionButtonGroup: false,
      });

      onMounted(async () => {
        const data = userStore.getUserInfo;
        id.value = data.id;
        setFieldsValue(data);
        getUserProfile().then((res) => {
          departmentNameList.value = res.departmentNameList || [];
          postNameList.value = res.postNameList || [];
          roleNameList.value = res.roleNameList || [];
        });
      });

      const avatar = computed(() => {
        const { avatar } = userStore.getUserInfo;
        return avatar || headerImg;
      });

      return {
        avatar,
        register,
        uploadAvatar,
        postNameList,
        roleNameList,
        departmentNameList,
        contentStyle,
        labelStyle,
        handleSubmit: async () => {
          const values = await validate();
          const userInfo = userStore.getUserInfo;

          values.id = id.value;
          await updateUserInfo(values);

          userInfo.id = id.value;
          userInfo.name = values.name;
          userInfo.mobile = values.mobile;
          userInfo.email = values.email;
          userInfo.remark = values.remark;
          userInfo.address = values.address;

          userStore.setUserInfo(userInfo);

          createMessage.success(t('更新成功！'));
        },
        uploadChange: (url: string) => {
          const userInfo = userStore.getUserInfo;
          userInfo.avatar = url;
          userStore.setUserInfo(userInfo);
        },
        t,
      };
    },
  });
</script>

<style lang="less" scoped>
  .change-avatar {
    img {
      display: block;
      margin-bottom: 15px;
      border-radius: 50%;
    }
  }
</style>
