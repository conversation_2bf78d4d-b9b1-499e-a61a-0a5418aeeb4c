<template>
  <CollapseContainer :title="t('密码修改')" :canExpan="false">
    <div class="flex flex-col items-center justify-center py-8 bg-white">
      <BasicForm @register="register" />
      <div class="flex justify-center">
        <a-button @click="resetFields"> {{ t('重置') }} </a-button>
        <a-button class="!ml-4" type="primary" @click="handleSubmit"> {{ t('确认') }} </a-button>
      </div>
    </div>
  </CollapseContainer>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { CollapseContainer } from '/@/components/Container/index';
  import { PageEnum } from '/@/enums/pageEnum';
  import { useGo } from '/@/hooks/web/usePage';
  import { updatePassword } from '/@/api/system/user';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const pwdScore = ref(0);
  export const formSchema: FormSchema[] = [
    {
      field: 'oldPassword',
      label: t('当前密码'),
      component: 'InputPassword',
      required: true,
      colProps: {
        span: 24,
      },
    },
    {
      field: 'newPassword',
      label: t('新密码'),
      component: 'StrengthMeter',
      componentProps: {
        placeholder: t('新密码'),
        onChange: (_, score) => {
          pwdScore.value = score;
        },
      },
      rules: [
        {
          validator: (_, value) => {
            if (!value) {
              return Promise.reject('请输入新密码');
            }
            if (pwdScore.value < 50) {
              return Promise.reject('密码强度设置过低，请至少保证中等强度。');
            }
            return Promise.resolve();
          },
          trigger: 'change',
        },
      ],
      colProps: {
        span: 24,
      },
    },
    {
      field: 'confirmPassword',
      label: t('确认密码'),
      component: 'InputPassword',
      colProps: {
        span: 24,
      },

      dynamicRules: ({ values }) => {
        return [
          {
            required: true,
            validator: (_, value) => {
              if (!value) {
                return Promise.reject(t('密码不能为空'));
              }
              if (value !== values.newPassword) {
                return Promise.reject(t('两次输入的密码不一致!'));
              }
              return Promise.resolve();
            },
          },
        ];
      },
    },
  ];

  export default defineComponent({
    name: 'ChangePassword',
    components: { BasicForm, CollapseContainer },
    setup() {
      const go = useGo();
      const [register, { validate, resetFields }] = useForm({
        size: 'large',
        labelWidth: 100,
        showActionButtonGroup: false,
        schemas: formSchema,
      });

      async function handleSubmit() {
        try {
          const values = await validate();
          // const { passwordOld, passwordNew } = values;

          // TODO custom api
          await updatePassword(values);

          go(PageEnum.BASE_LOGIN);
        } catch (error) {}
      }

      return { register, resetFields, handleSubmit, t };
    },
  });
</script>
