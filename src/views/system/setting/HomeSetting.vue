<template>
  <PageLayout
    layout-class="!pl-0 -ml-14px"
    :hasOperationSlot="false"
    :searchConfig="searchConfig"
    @search="search"
    @scroll-height="scrollHeight"
  >
    <template #search> </template>
    <template #right>
      <div v-if="data.dataSource.length > 0">
        <a-radio-group v-model:value="data.checked" style="width: 100%" @change="handleChange">
          <a-row
            :gutter="16"
            class="list-box"
            :style="{ overflowY: 'auto', maxHeight: tableOptions.scrollHeight + 70 + 'px' }"
            :key="data.renderKey"
          >
            <a-col :span="6" class="item" v-for="(item, index) in data.dataSource" :key="index">
              <div class="image relative">
                <img :src="item.backgroundUrl" />
                <a-tag color="processing" v-if="item.isFirst" class="absolute right-0 bottom-2"
                  >默认首页</a-tag
                >
              </div>
              <div class="main">
                <a-radio :value="item.id">{{ item.name }}</a-radio>
              </div>
            </a-col>
          </a-row>
        </a-radio-group>
        <!-- <div class="page-box">
          <a-pagination
            v-model:current="data.pagination.current"
            :pageSize="data.pagination.pageSize"
            :total="data.pagination.total"
            show-less-items
            @change="getList"
          />
        </div> -->
      </div>
      <div v-else>
        <EmptyBox />
      </div>
    </template>
  </PageLayout>
</template>
<script lang="ts" setup>
  import { onMounted, reactive } from 'vue';

  import { DesktopPageModel } from '/@/api/desktop/model';
  import { PageLayout, EmptyBox } from '/@/components/ModalPanel';
  import userTableScrollHeight from '/@/hooks/setting/userTableScrollHeight';

  import { useI18n } from '/@/hooks/web/useI18n';
  import { getRolePrivateHome, setPrivateHome } from '/@/api/system/role';
  import { notification } from 'ant-design-vue';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { useUserStore } from '/@/store/modules/user';

  const { t } = useI18n();

  const searchConfig = [
    {
      field: 'keyword',
      label: t('关键字'),
      type: 'input',
    },
  ];
  const { tableOptions, scrollHeight } = userTableScrollHeight();
  let data: {
    dataSource: Array<DesktopPageModel>;
    renderKey: number;
    keyword: string;
    checked: string;
    // pagination: {
    //   current: number;
    //   total: number;
    //   pageSize: number;
    // };
  } = reactive({
    dataSource: [],
    renderKey: 0,
    //pagination: { current: 1, total: 0, pageSize: 8 },
    keyword: '',
    checked: '',
  });

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  onMounted(async () => {
    //data.pagination.current = 1;
    data.dataSource = [];
    await getList();
    setFirstHome();
  });
  async function getList() {
    let params = {
      //limit: data.pagination.current,
      //size: data.pagination.pageSize,
      keyword: data.keyword,
    };
    let res = await getRolePrivateHome(params);
    data.dataSource = res;

    // data.pagination.total = res.total;
    data.renderKey++;
  }

  function search(params?: any) {
    data.keyword = params.keyword;
    // data.pagination.current = 1;
    data.dataSource = [];
    getList();
  }
  function setFirstHome() {
    data.checked = userInfo.desktopSchema.id;
  }
  async function handleChange(v) {
    try {
      await setPrivateHome({ desktopId: v.target.value });
      notification.success({
        message: t('提示'),
        description: t('修改首页成功'),
      });
      const permissionStore = usePermissionStore();
      await permissionStore.changePermissionCode();
    } catch (error) {
      notification.error({
        message: t('提示'),
        description: t('修改首页失败'),
        onClose: () => {
          setFirstHome();
        },
      });
    }
  }
</script>
<style lang="less" scoped>
  .list-box {
    display: flex;
    flex-wrap: wrap;
    margin: 10px -8px;

    .ant-row {
      margin: 0 !important;
    }

    .item {
      position: relative;
      margin-bottom: 16px;
      overflow: hidden;
      border-radius: 4px;
    }

    .image {
      width: 100%;
      height: 140px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .main {
      font-size: 12px;
      text-align: center;
      line-height: 40px;
    }
  }

  .page-box {
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
</style>
