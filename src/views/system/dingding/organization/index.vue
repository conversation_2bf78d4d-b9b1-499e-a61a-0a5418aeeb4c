<template>
  <PageWrapper dense contentFullHeight fixedHeight>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'name'">
          <a-tag color="processing" v-if="record.departmentType === 1">公司</a-tag>
          <a-tag color="warning" v-else-if="record.departmentType === 0">部门</a-tag>
          &nbsp;{{ record.name }}
        </template>
        <template v-else-if="column.dataIndex == 'syncState'">
          <a-tag color="success" v-if="!!record.dingtalkDeptId">同步成功</a-tag>
        </template>
        <template v-else-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                icon: 'tongbu|svg',
                auth: 'organization:sync',
                tooltip: '同步',
                onClick: handleSync.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, BasicColumn, FormSchema, TableAction } from '/@/components/Table';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDepartmentsList, updateSyncDept } from '/@/api/system/dingding';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();

  const columns: BasicColumn[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 600,
      align: 'left',
      sorter: true,
      resizable: true,
    },
    {
      title: '编码',
      dataIndex: 'code',
      align: 'left',
      sorter: true,
      resizable: true,
    },
    {
      title: '钉钉组织Id',
      dataIndex: 'dingtalkDeptId',
      align: 'left',
      sorter: true,
      resizable: true,
    },
    {
      title: '同步状态',
      dataIndex: 'syncState',
      align: 'left',
      sorter: true,
      resizable: true,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: '关键字',
      component: 'Input',
      colProps: { lg: 8, md: 12, sm: 12 },
      componentProps: {
        placeholder: '请输入要查询的关键字',
      },
    },
  ];

  const { notification } = useMessage();
  const [registerTable, { reload }] = useTable({
    title: '钉钉组织列表',
    api: getDepartmentsList,
    rowKey: 'id',
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      actionColOptions: { span: 16 },
      showResetButton: false,
    },
    striped: false,
    useSearchForm: true,
    showTableSetting: true,
    showIndexColumn: false,
    tableSetting: {
      size: false,
    },
    actionColumn: {
      width: 60,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  async function handleSync(record) {
    if (import.meta.env.VITE_GLOB_PRODUCTION === 'true') {
      notification.warning({
        message: '提示',
        description: '由于您当前处于体验状态，无法操作进行同步',
      });
      return false;
    }
    await updateSyncDept(record.id);
    notification.success({
      message: '提示',
      description: '同步成功',
    });
    reload();
  }
</script>
