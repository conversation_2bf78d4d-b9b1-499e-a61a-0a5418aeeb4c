<template>
  <PageWrapper dense contentFullHeight fixedHeight>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'departmentType'">
          <a-tag color="processing" v-if="record.departmentType === 1">公司</a-tag>
          <a-tag color="warning" v-else-if="record.departmentType === 0">部门</a-tag>
        </template>
        <template v-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:setting-outlined',
                auth: 'configuration:setting',
                tooltip: '设置',
                onClick: handleSetting.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ConfigModal @register="registerModal" @success="reload" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, BasicColumn, FormSchema, TableAction } from '/@/components/Table';
  import { PageWrapper } from '/@/components/Page';
  import { getDepartmentsList } from '/@/api/system/dingding';
  import ConfigModal from './components/ConfigModal.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useModal } from '/@/components/Modal';
  import { usePermission } from '/@/hooks/web/usePermission';

  const { t } = useI18n();
  const columns: BasicColumn[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 600,
      align: 'left',
      sorter: true,
      resizable: true,
    },
    {
      title: '类型',
      dataIndex: 'departmentType',
      align: 'left',
      sorter: true,
      resizable: true,
    },
    {
      title: '编码',
      dataIndex: 'code',
      align: 'left',
      sorter: true,
      resizable: true,
    },
    {
      title: '简称',
      dataIndex: 'name',
      align: 'left',
      sorter: true,
      resizable: true,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: '关键字',
      component: 'Input',
      colProps: { lg: 8, md: 12, sm: 12 },
      componentProps: {
        placeholder: '请输入要查询的关键字',
      },
    },
  ];
  const { hasPermission } = usePermission();

  const [registerModal, { openModal }] = useModal();
  const [registerTable, { reload }] = useTable({
    title: '钉钉配置列表',
    api: getDepartmentsList,
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      actionColOptions: { span: 16 },
      showResetButton: false,
    },
    striped: false,
    useSearchForm: true,
    showTableSetting: true,
    showIndexColumn: false,
    actionColumn: {
      width: 100,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('configuration:setting')) {
            handleSetting(record);
          }
        },
      };
    },
  });

  function handleSetting(record: Recordable) {
    openModal(true, { record });
  }
</script>
