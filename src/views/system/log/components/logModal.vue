<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="t('清空')" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { FormSchema } from '/@/components/Table';

  import { useMessage } from '/@/hooks/web/useMessage';

  import { clearLog } from '/@/api/system/log';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export const accountFormSchema: FormSchema[] = [
    {
      label: t('保留时间'),
      field: 'type',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          {
            label: t('保留近一周'),
            value: '0',
          },
          {
            label: t('保留近一个月'),
            value: '1',
          },
          {
            label: t('保留近三个月'),
            value: '2',
          },
          {
            label: t('不保留，全部删除'),
            value: '3',
          },
        ],
      },
      colProps: { span: 24 },
    },
  ];

  export default defineComponent({
    name: 'ClearModel',
    components: { BasicModal, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const { notification } = useMessage();

      const [registerForm, { resetFields, validate }] = useForm({
        labelWidth: 100,
        schemas: accountFormSchema,
        showActionButtonGroup: false,
        actionColOptions: {
          span: 23,
        },
      });

      const [registerModal, { setModalProps, closeModal }] = useModalInner(() => {
        resetFields();
        setModalProps({ confirmLoading: false });
      });

      async function handleSubmit() {
        try {
          const values = await validate();
          setModalProps({ confirmLoading: true });

          await clearLog(values.type);
          notification.success({
            message: t('清空日志'),
            description: t('成功'),
          }); //提示消息

          closeModal();
          emit('success');
        } catch (error) {
          setModalProps({ confirmLoading: false });
        }
      }

      return { registerModal, registerForm, handleSubmit, t };
    },
  });
</script>
