<template>
  <BasicModal
    @register="registerModal"
    title="审批专员"
    :width="600"
    fixedHeight
    destroyOnClose
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <a-table
      :key="tableKey"
      :columns="columns"
      :data-source="data"
      :pagination="false"
      class="table-box"
    >
      <template #bodyCell="{ column, index, record }">
        <template v-if="column.dataIndex === 'index'"> {{ index + 1 }}</template>
        <template v-else-if="column.dataIndex === 'sort'">
          <svg class="icon sortDraggable-icon" aria-hidden="true" style="cursor: move">
            <use xlink:href="#icon-fangxiang1" />
          </svg>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <DeleteOutlined @click="handleDelete(index)" style="color: #ed6f6f" />
        </template>
        <template v-else>
          <a-input
            v-model:value="record[column.dataIndex]"
            :placeholder="`请填写${column.title}`"
          />
        </template>
      </template>
    </a-table>
    <a-button type="dashed" block @click="handleAdd">
      <PlusOutlined />
      {{ t('新增') }}
    </a-button>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, watch, nextTick } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { updateApprovalSpe, getApprovalList } from '/@/api/system/department';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import Sortable from 'sortablejs';
  import { isNullAndUnDef } from '/@/utils/is';
  import { cloneDeep } from 'lodash-es';

  const { t } = useI18n();

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 50,
    },
    {
      title: '排序',
      dataIndex: 'sort',
      width: 50,
    },
    {
      title: '专员名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 70,
      align: 'center',
    },
  ];
  defineEmits(['register']);
  const { notification } = useMessage();
  const tableKey = ref<number>(0);
  const data = ref<any[]>([]);

  watch(
    () => data.value,
    (val) => {
      if (val && val.length) {
        nextTick(() => {
          const tbody: any = document.querySelector('.table-box .ant-table-tbody');
          if (!tbody) return;
          Sortable.create(tbody, {
            handle: '.sortDraggable-icon',
            onEnd: ({ oldIndex, newIndex }) => {
              if (isNullAndUnDef(oldIndex) || isNullAndUnDef(newIndex) || newIndex === oldIndex) {
                return;
              }
              const columns = cloneDeep(data.value);
              if (oldIndex > newIndex) {
                columns.splice(newIndex, 0, columns[oldIndex]);
                columns.splice(oldIndex + 1, 1);
              } else {
                columns.splice(newIndex + 1, 0, columns[oldIndex]);
                columns.splice(oldIndex, 1);
              }
              data.value = cloneDeep(columns);
              tableKey.value++;
            },
          });
        });
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const [registerModal, { closeModal, getVisible }] = useModalInner();

  watch(
    () => getVisible?.value,
    async (val) => {
      if (val) data.value = await getApprovalList();
    },
  );
  const handleDelete = async (index) => {
    data.value.splice(index, 1);
  };

  const handleSubmit = async () => {
    await updateApprovalSpe(data.value);
    notification.success({
      message: t('提示'),
      description: t('添加成功'),
    });
    closeModal();
  };

  const handleAdd = () => {
    data.value.push({
      name: '',
      remark: '',
    });
  };
</script>
<style lang="less" scoped>
  .icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentcolor;
    overflow: hidden;
  }
</style>
