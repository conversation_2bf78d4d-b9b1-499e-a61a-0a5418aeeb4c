<template>
  <ResizePageWrapper>
    <template #resizeLeft>
      <BasicTree
        title="登录终端"
        :treeData="treeData"
        @select="handleSelect"
        :selectedKeys="['PC']"
      />
    </template>

    <template #resizeRight>
      <BasicTable @register="registerTable">
        <template #toolbar>
          <a-button type="primary" v-auth="'online:logout'" @click="handleLogout()">
            强制下线
          </a-button>
        </template>
        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:poweroff-outlined',
                auth: 'online:logoutSingle',
                tooltip: '下线',
                color: 'error',
                onClick: handleLogout.bind(null, record.id),
              },
            ]"
          />
        </template>
      </BasicTable>
    </template>
  </ResizePageWrapper>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicTable, useTable, FormSchema, BasicColumn, TableAction } from '/@/components/Table';
  import { getOnlineUserPageList, OffOnlineUser } from '/@/api/system/user';
  import { ResizePageWrapper } from '/@/components/Page';
  import { BasicTree, TreeItem } from '/@/components/Tree';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { t } = useI18n();
  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: '关键字',
      component: 'Input',
      componentProps: {
        placeholder: '请输入人员名称或用户名',
      },
    },
  ];

  const columns: BasicColumn[] = [
    {
      title: '人员名称',
      dataIndex: 'name',
      align: 'left',
      resizable: true,
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      align: 'left',
      resizable: true,
    },
    {
      title: '所属组织',
      dataIndex: 'departmentNames',
      align: 'left',
      resizable: true,
    },
    {
      title: '在线时长',
      dataIndex: 'onlineTime',
      align: 'left',
      sorter: true,
      resizable: true,
    },
  ];
  const treeData = ref<TreeItem[]>([
    { key: 'PC', title: 'Web端' },
    { key: 'APP', title: '移动端' },
  ]);

  const { notification } = useMessage();
  const loginPort = ref('PC');
  const [registerTable, { reload, getSelectRowKeys }] = useTable({
    title: '在线人员列表',
    api: getOnlineUserPageList,
    rowKey: 'id',
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },
    beforeFetch: (params) => {
      return { ...params, device: loginPort.value };
    },
    rowSelection: {
      type: 'checkbox',
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    actionColumn: {
      width: 100,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
  });

  async function handleLogout(id?) {
    if (import.meta.env.VITE_GLOB_PRODUCTION === 'true') {
      notification.warning({
        message: '在线环境暂不允许该操作，请联系管理员。',
      });
      return;
    }
    if (!id && !getSelectRowKeys().length) {
      notification.warning({
        message: '提示',
        description: '请先选择人员后再进行强制下线。',
      });
      return false;
    }
    const params = {
      device: loginPort.value,
      userIds: id ? [id] : getSelectRowKeys(),
    };
    await OffOnlineUser(params);
    notification.success({
      message: '提示',
      description: '下线成功',
    });
    reload();
  }

  function handleSelect(key) {
    loginPort.value = key[0];
    reload();
  }
</script>
<style lang="less" scoped>
  .left-box {
    overflow: hidden;
    background: #fff;
    height: 100%;
    border-right: 1px solid #e5e7eb;
  }
</style>
