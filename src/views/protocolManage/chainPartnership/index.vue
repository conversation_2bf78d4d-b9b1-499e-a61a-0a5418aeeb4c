<template>
  <div id="chainPartnership">
    <div class="comPage_Box">
      <div class="filterForm_box">
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.name"
          placeholder="协议名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.code"
          placeholder="协议编码"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-select
          style="width: 240px"
          v-model:value="searchForm.signType"
          placeholder="签署类型"
          :options="signTypeOptions"
          allow-clear
          @change="getList()"
        />
        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
        <a-button class="export_btn" type="primary" @click="onAdd()">新增</a-button>
        <a-button
          class="export_btn"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="reloadT()"
          >重推合同系统</a-button
        >
        <!-- <a-button
          class="export_btn"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="reloadR()"
          >重新生成协议附件</a-button
        > -->
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
          :isSelection="true"
          rowKey="id"
          :checkedKeys="selectedKeys"
          @onSelectChange="onSelectChange"
        >
          <template #fileStatus="{ record }">
            <span>{{ fileStatus[record.fileStatus] }}</span>
          </template>
          <template #pushContractStatus="{ record }">
            <span>{{ pushContractStatus[record.pushContractStatus] }}</span>
          </template>
          <template #action="{ record }">
            <a-button
              v-if="record.pushContractStatus != 2"
              type="link"
              @click.stop="onPush(record)"
              >{{
                ['1', '4', '5'].includes(record.approvalStatus)
                  ? '推送OA'
                  : record.approvalStatus === '3'
                  ? '推送合同'
                  : ''
              }}</a-button
            >
            <a-button
              v-if="['1', '4', '5'].includes(record.approvalStatus)"
              type="link"
              @click.stop="onEdit(record)"
              >编辑</a-button
            >
            <a-button type="link" @click.stop="onView(record)">查看</a-button>
          </template>
        </c-table>
      </div>
    </div>
  </div>
  <!-- 协议管理弹窗 -->
  <a-modal
    v-model:visible="modalVisible"
    :title="modalTitle"
    width="100%"
    wrap-class-name="full-modala"
    :footer="null"
    :destroy-on-close="true"
  >
    <a-tabs v-if="isView" v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="表单信息" />
      <a-tab-pane key="2" tab="流程信息" />
      <a-tab-pane key="3" tab="流转记录" />
    </a-tabs>
    <div v-if="activeKey == '1'" class="contract-modal-content">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        class="contract-form"
      >
        <a-collapse v-model:activeKey="activeKeys" class="contract-collapse">
          <!-- 基本信息 -->
          <a-collapse-panel key="basic" header="基本信息">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="合同编码" name="code">
                  <a-input
                    v-model:value="formData.code"
                    placeholder="请输入合同编码"
                    :disabled="true"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="协议类型" name="contractType">
                  <a-select
                    v-model:value="formData.contractType"
                    placeholder="请选择协议类型"
                    :disabled="isView"
                    allowClear
                  >
                    <a-select-option value="1">连锁合作协议</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="地区" name="areaValues">
                  <!-- <a-cascader
                    v-model:value="formData.areaValues"
                    :options="areaOptions"
                    :load-data="loadAreaData"
                    :loading="loadingArea"
                    placeholder="请选择省市区"
                    :disabled="isView"
                    change-on-select
                    @change="handleAreaChange"
                    style="width: 100%"
                    allowClear
                  /> -->
                  <a-input
                    v-model:value="formData.provinceName"
                    placeholder="请输入地区"
                    :disabled="isView"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="签署类型" name="signType">
                  <a-select
                    v-model:value="formData.signType"
                    placeholder="请选择签署类型"
                    :disabled="isView"
                    allowClear
                  >
                    <a-select-option value="1">新签</a-select-option>
                    <a-select-option value="2">续签</a-select-option>
                    <a-select-option value="3">变更</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- <a-col :span="8">
                <a-form-item label="产品类型" name="productType">
                  <a-select
                    v-model:value="formData.productType"
                    placeholder="请选择产品类型"
                    :disabled="isView"
                    allowClear
                  >
                    <a-select-option value="1">单产品协议</a-select-option>
                    <a-select-option value="2">多产品协议</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col> -->
            </a-row>
          </a-collapse-panel>

          <!-- 协议内容 -->
          <a-collapse-panel key="content" header="协议内容">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="我方签约主体名称" name="recruiterCompanyId">
                  <a-select
                    v-model:value="formData.recruiterCompanyId"
                    placeholder="请选择"
                    mode="multiple"
                    :disabled="isView"
                    allowClear
                    @change="
                      () => {
                        formData.signingMethod = undefined;
                      }
                    "
                  >
                    <a-select-option
                      v-for="(item, index) in recruiterList"
                      :key="index"
                      :value="item.enterpriseCode"
                      >{{ item.enterpriseName }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="乙方名称" name="companyId">
                  <a-select
                    v-model:value="formData.companyId"
                    placeholder="请输入乙方名称"
                    :disabled="isView"
                    show-search
                    :filter-option="false"
                    :not-found-content="companyNameLoading ? undefined : null"
                    @search="handleCompanyNameSearch"
                    @dropdown-visible-change="handleCompanyNameDropdownChange"
                    @popup-scroll="handleCompanyNameScroll"
                    @change="handleCompanyNameChange"
                    allowClear
                  >
                    <template #notFoundContent v-if="companyNameLoading">
                      <a-spin size="small" />
                    </template>
                    <a-select-option
                      v-for="item in companyNameOptions"
                      :key="item.value"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="商业供货单位" name="supplyId">
                  <a-select
                    v-model:value="formData.supplyId"
                    placeholder="请输入商业供货单位"
                    :disabled="isView"
                    show-search
                    :filter-option="false"
                    :not-found-content="supplierLoading ? undefined : null"
                    @search="handleSupplierSearch"
                    @dropdown-visible-change="handleSupplierDropdownChange"
                    @popup-scroll="handleSupplierScroll"
                    @change="handleSupplierChange"
                    allowClear
                  >
                    <template #notFoundContent v-if="supplierLoading">
                      <a-spin size="small" />
                    </template>
                    <a-select-option
                      v-for="item in supplierOptions"
                      :key="item.value"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="连锁上一年度销售规模" name="lastYearSaleMoney">
                  <a-input
                    v-model:value="formData.lastYearSaleMoney"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="直营店家数" name="directSaleStoreNum">
                  <a-input
                    v-model:value="formData.directSaleStoreNum"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="加盟店家数" name="joinSaleStoreNum">
                  <a-input
                    v-model:value="formData.joinSaleStoreNum"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="年度/季度任务量类型" name="quarterTaskNumType">
                  <a-select
                    v-model:value="formData.quarterTaskNumType"
                    placeholder="请选择"
                    :disabled="isView"
                    allowClear
                  >
                    <a-select-option value="门店纯销数量">门店纯销数量</a-select-option>
                    <a-select-option value="门店配送数量">门店配送数量</a-select-option>
                    <a-select-option value="连锁购进数量">连锁购进数量</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- <a-col :span="8">
                <a-form-item label="年度任务量" name="yearTaskNumType">
                  <a-select
                    v-model:value="formData.yearTaskNumType"
                    placeholder="请选择"
                    :disabled="isView"
                    allowClear
                  >
                    <a-select-option value="门店纯销数量">门店纯销数量</a-select-option>
                    <a-select-option value="门店配送数量">门店配送数量</a-select-option>
                    <a-select-option value="连锁购进数量">连锁购进数量</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col> -->
              <a-col :span="8">
                <a-form-item label="B2C核算类型" name="b2cAmountType">
                  <a-select
                    v-model:value="formData.b2cAmountType"
                    placeholder="请选择"
                    :disabled="isView"
                    allowClear
                  >
                    <a-select-option value="门店纯销数量">门店纯销数量</a-select-option>
                    <a-select-option value="门店配送数量">门店配送数量</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="费用核算类型" name="costAmountType">
                  <a-select
                    v-model:value="formData.costAmountType"
                    placeholder="请选择"
                    :disabled="isView"
                    allowClear
                  >
                    <a-select-option value="门店纯销金额">门店纯销金额</a-select-option>
                    <a-select-option value="门店配送金额">门店配送金额</a-select-option>
                    <a-select-option value="连锁购进金额">连锁购进金额</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="第二阶梯达成金额等于第一阶梯达成金额上限"
                  name="firstUpperReachSecond"
                >
                  <a-input
                    v-model:value="formData.firstUpperReachSecond"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="第三阶梯达成金额等于第二阶梯达成金额上限"
                  name="secondUpperReachThird"
                >
                  <a-input
                    v-model:value="formData.secondUpperReachThird"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="第一阶梯达成金额上限" name="firstStepReachUpperLimit">
                  <a-input
                    v-model:value="formData.firstStepReachUpperLimit"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="第一阶梯达成金额下限" name="firstStepReachLowerLimit">
                  <a-input
                    v-model:value="formData.firstStepReachLowerLimit"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="第二阶梯达成金额上限" name="secondStepReachUpperLimit">
                  <a-input
                    v-model:value="formData.secondStepReachUpperLimit"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="第一阶梯任务奖励百分比" name="firstStepAwardRate">
                  <a-input
                    v-model:value="formData.firstStepAwardRate"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="第二阶梯任务奖励百分比" name="secondStepAwardRate">
                  <a-input
                    v-model:value="formData.secondStepAwardRate"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="第三阶梯任务奖励百分比" name="thirdStepAwardRate">
                  <a-input
                    v-model:value="formData.thirdStepAwardRate"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="铺货率" name="coverageRate">
                  <a-input
                    v-model:value="formData.coverageRate"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="单店要求盒数" name="singleStoreBoxNum">
                  <a-input
                    v-model:value="formData.singleStoreBoxNum"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :min="0"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="阶梯考核数量类型" name="ladderStepAmountType">
                  <a-select
                    v-model:value="formData.ladderStepAmountType"
                    placeholder="请选择"
                    :disabled="isView"
                    allowClear
                  >
                    <a-select-option value="门店纯销数量">门店纯销数量</a-select-option>
                    <a-select-option value="门店配送数量">门店配送数量</a-select-option>
                    <a-select-option value="连锁购进数量">连锁购进数量</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="甲方授权代表康缘工号" name="recruiterId">
                  <a-input
                    v-model:value="formData.recruiterId"
                    placeholder="请输入"
                    :disabled="isView"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="甲方授权代表" name="recruiterName">
                  <a-input
                    v-model:value="formData.recruiterName"
                    placeholder="请输入"
                    :disabled="isView"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="甲方联系人电话" name="recruiterPhone">
                  <a-input
                    v-model:value="formData.recruiterPhone"
                    placeholder="请输入"
                    :disabled="isView"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="乙方授权代表" name="signatoryName">
                  <a-input
                    v-model:value="formData.signatoryName"
                    placeholder="请输入"
                    :disabled="isView"
                    allowClear
                    @blur="handleBlur('signatoryName')"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="乙方联系人电话" name="signatoryPhone">
                  <a-input
                    v-model:value="formData.signatoryPhone"
                    placeholder="请输入"
                    :disabled="isView"
                    allowClear
                    @blur="handleBlur('signatoryPhone')"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="签约方式" name="signingMethod">
                  <a-select
                    v-model:value="formData.signingMethod"
                    placeholder="请选择签约方式"
                    @change="signChange"
                    :disabled="isView"
                    allowClear
                  >
                    <a-select-option value="ESEAL">电子签约</a-select-option>
                    <a-select-option value="PHYSICALSEAL">纸质签约</a-select-option>
                    <a-select-option value="MIXTURE">电签+纸质</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="签约顺序" name="signingSort">
                      <a-select
                        v-model:value="formData.signingSort"
                        placeholder=""
                        :disabled="true"
                        allowClear
                      >
                        <a-select-option value="DISORDER">无序签署</a-select-option>
                        <a-select-option value="ORDER">无序签署</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="签约份数（一式几份）" name="signingNumber">
                      <a-input-number
                        v-model:value="formData.signingNumber"
                        placeholder=""
                        :disabled="isView"
                        style="width: 100%"
                        :min="0"
                        allowClear
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="8">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="用章次数" name="useNumber">
                      <a-input-number
                        v-model:value="formData.useNumber"
                        placeholder=""
                        :disabled="isView"
                        style="width: 100%"
                        :min="0"
                        allowClear
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="骑缝章用章次数" name="straddleUseNumber">
                      <a-input-number
                        v-model:value="formData.straddleUseNumber"
                        placeholder=""
                        :disabled="isView"
                        style="width: 100%"
                        :min="0"
                        allowClear
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-col>
            </a-row>
            <!-- 协议签署信息表格 -->
            <a-row>
              <a-col :span="24">
                <a-form-item label="">
                  <a-table
                    :columns="signNodeColumns"
                    :data-source="formData.signNodes"
                    :pagination="false"
                    row-key="id"
                    size="small"
                    bordered
                  >
                    <template #bodyCell="{ column, record, index }">
                      <template v-if="column.dataIndex === 'counterpartyName'">
                        <span>{{ record.counterpartyName }}</span>
                        <!-- <a-input
                          v-else
                          v-model:value="record.name"
                          placeholder="请输入签约方名称"
                          allowClear
                        /> -->
                      </template>
                      <template v-else-if="column.dataIndex === 'counterpartyCode'">
                        <span>{{ record.counterpartyCode }}</span>
                        <!-- <a-input
                          v-else
                          v-model:value="record.counterpartyCode"
                          placeholder="请输入相对方编码"
                          allowClear
                        /> -->
                      </template>
                      <template v-else-if="column.dataIndex === 'signingMethodName'">
                        <span>{{ record.signingMethodName }}</span>
                        <!-- <a-select
                          v-else
                          v-model:value="record.signingMethod"
                          placeholder="请选择签约方式"
                          style="width: 100%"
                          allowClear
                          @change="(value) => handleSigningMethodChange(value, record)"
                        >
                          <a-select-option value="ESEAL">电子签约</a-select-option>
                          <a-select-option value="PHYSICALSEAL">纸质签约</a-select-option>
                          <a-select-option value="MIXTURE">电签+纸质</a-select-option>
                        </a-select> -->
                      </template>
                      <template v-else-if="column.dataIndex === 'signingTypeName'">
                        <span v-if="!record.editing">{{ record.signingTypeName }}</span>
                        <a-select
                          v-else
                          v-model:value="record.signingType"
                          placeholder="请选择签章类型"
                          style="width: 100%"
                          allowClear
                          @change="(value) => handleSigningTypeChange(value, record)"
                        >
                          <a-select-option value="COMPANY_SEAL">企业盖章</a-select-option>
                          <a-select-option value="PERSONAL_SIGN">个人签字</a-select-option>
                          <a-select-option value="LP_SIGN">法定代表人签字</a-select-option>
                        </a-select>
                      </template>
                      <template v-else-if="column.dataIndex === 'signatoryName'">
                        <span v-if="!record.editing">{{ record.signatoryName }}</span>
                        <a-input
                          v-else
                          v-model:value="record.signatoryName"
                          placeholder="请输入签约人姓名"
                          allowClear
                        />
                      </template>
                      <template v-else-if="column.dataIndex === 'signatoryPhone'">
                        <span v-if="!record.editing">{{ record.signatoryPhone }}</span>
                        <a-input
                          v-else
                          v-model:value="record.signatoryPhone"
                          placeholder="请输入签约人手机号"
                          allowClear
                        />
                      </template>
                      <template v-else-if="column.dataIndex === 'straddleStamp'">
                        <span>{{ getStraddleStampText(record.straddleStamp) }}</span>
                        <!-- <a-select
                          v-else
                          v-model:value="record.straddleStamp"
                          placeholder="请选择"
                          style="width: 100%"
                          allowClear
                        >
                          <a-select-option :value="0">不盖骑缝章</a-select-option>
                          <a-select-option :value="1">加盖骑缝章</a-select-option>
                        </a-select> -->
                      </template>
                      <template v-else-if="column.dataIndex === 'action'">
                        <div
                          v-if="
                            !['江苏康缘药业股份有限公司', '江苏康缘阳光药业有限公司'].includes(
                              record.counterpartyName,
                            )
                          "
                        >
                          <div v-if="!isView">
                            <a-button
                              v-if="!record.editing"
                              type="link"
                              @click="editSignNode(index)"
                            >
                              编辑
                            </a-button>
                            <a-button v-else type="link" @click="saveSignNode(index)">
                              完成
                            </a-button>
                          </div>
                        </div>
                      </template>
                    </template>
                  </a-table>
                </a-form-item>
              </a-col>
            </a-row>
          </a-collapse-panel>

          <!-- 结算信息 -->
          <a-collapse-panel key="settlement" header="结算信息">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="结算账户名称" name="settleAccountName">
                  <a-input
                    v-model:value="formData.settleAccountName"
                    placeholder="请输入结算账户名称"
                    :disabled="isView"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="结算账户卡号" name="settleCardNumber">
                  <a-input
                    v-model:value="formData.settleCardNumber"
                    placeholder="请输入结算账户卡号"
                    :disabled="isView"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="开户银行" name="settleBankName">
                  <a-input
                    v-model:value="formData.settleBankName"
                    placeholder="请输入开户银行"
                    :disabled="isView"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-collapse-panel>

          <!-- 协议效期 -->
          <a-collapse-panel key="validity" header="协议效期">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="有效期从" name="startTime">
                  <a-date-picker
                    v-model:value="formData.startTime"
                    placeholder="请选择开始日期"
                    :disabled="isView"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="有效期至" name="endTime">
                  <a-date-picker
                    v-model:value="formData.endTime"
                    placeholder="请选择结束日期"
                    :disabled="isView"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-collapse-panel>

          <!-- 合作产品信息 -->
          <a-collapse-panel key="products" header="合作产品信息">
            <a-table
              :columns="productColumns"
              :data-source="formData.contractProducts"
              :pagination="false"
              row-key="id"
              size="small"
              bordered
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === 'productName'">
                  <span>{{ record.productName || '-' }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'productFormatName'">
                  <span>{{ record.productFormatName || '-' }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'unit'">
                  <span>{{ record.unit || '-' }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'negotiatedPrice'">
                  <a-input-number
                    v-model:value="record.negotiatedPrice"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :precision="2"
                    :min="0"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'suggestionPrice'">
                  <a-input-number
                    v-model:value="record.suggestionPrice"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :precision="2"
                    :min="0"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'action'">
                  <a-button
                    v-if="!isView"
                    type="link"
                    danger
                    @click="removeProduct(index)"
                    :disabled="isView"
                    >删除</a-button
                  >
                </template>
              </template>
            </a-table>
            <div class="table-action-row" v-if="!isView">
              <a-button type="primary" @click="addProduct">新增合作产品</a-button>
              <a-button
                style="margin-left: 10px"
                type="primary"
                @click="() => openExcelImportModal('product', '导入合作产品')"
                >导入</a-button
              >
              <a-button
                style="margin-left: 10px"
                type="primary"
                @click="() => exportTableData('product', '合作产品信息')"
                >导出</a-button
              >
            </div>
          </a-collapse-panel>

          <!-- 年度任务量分解 -->
          <a-collapse-panel key="tasks" header="年度任务量分解">
            <a-table
              :columns="taskColumns"
              :data-source="formData.contractProductTasks"
              :pagination="false"
              row-key="id"
              size="small"
              bordered
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === 'productName'">
                  <span>{{ record.productName || '-' }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'productFormatName'">
                  <span>{{ record.productFormatName || '-' }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'unit'">
                  <span>{{ record.unit || '-' }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'yearTask'">
                  <a-input
                    v-model:value="record.yearTask"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :precision="0"
                    :min="0"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'firQuarterTask'">
                  <a-input
                    v-model:value="record.firQuarterTask"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :precision="0"
                    :min="0"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'twoQuarterTask'">
                  <a-input
                    v-model:value="record.twoQuarterTask"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :precision="0"
                    :min="0"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'thrQuarterTask'">
                  <a-input
                    v-model:value="record.thrQuarterTask"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :precision="0"
                    :min="0"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'fouQuarterTask'">
                  <a-input
                    v-model:value="record.fouQuarterTask"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :precision="0"
                    :min="0"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'cooperationCharge'">
                  <a-input
                    v-model:value="record.cooperationCharge"
                    placeholder="请输入"
                    :disabled="isView"
                    style="width: 100%"
                    :precision="2"
                    :min="0"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'action'">
                  <a-button
                    v-if="!isView"
                    type="link"
                    danger
                    @click="removeTask(index)"
                    :disabled="isView"
                    >删除</a-button
                  >
                </template>
              </template>
            </a-table>
            <div class="table-action-row" v-if="!isView">
              <a-button type="primary" @click="addTask">新增年度任务</a-button>
              <a-button
                style="margin-left: 10px"
                type="primary"
                @click="() => openExcelImportModal('task', '导入年度任务量分解')"
                >导入</a-button
              >
              <a-button
                style="margin-left: 10px"
                type="primary"
                @click="() => exportTableData('task', '年度任务量分解')"
                >导出</a-button
              >
            </div>
          </a-collapse-panel>

          <!-- 店员推荐体系产品 -->
          <a-collapse-panel key="employees" header="店员推荐体系产品">
            <a-table
              :columns="employeeColumns"
              :data-source="formData.contractProductEmployees"
              :pagination="false"
              row-key="id"
              size="small"
              bordered
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === 'productName'">
                  <span>{{ record.productName || '-' }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'productFormatName'">
                  <span>{{ record.productFormatName || '-' }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'unit'">
                  <span>{{ record.unit || '-' }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'incentivePolicy'">
                  <a-input
                    v-model:value="record.incentivePolicy"
                    placeholder="请输入连锁店员单品激励政策"
                    :disabled="isView"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'suggestionMark'">
                  <a-input
                    v-model:value="record.suggestionMark"
                    placeholder="请输入连锁货架推荐符号或标志"
                    :disabled="isView"
                    allowClear
                  />
                </template>
                <template v-else-if="column.dataIndex === 'action'">
                  <a-button
                    v-if="!isView"
                    type="link"
                    danger
                    @click="removeEmployee(index)"
                    :disabled="isView"
                    >删除</a-button
                  >
                </template>
              </template>
            </a-table>
            <div class="table-action-row" v-if="!isView">
              <a-button type="primary" @click="addEmployee">新增店员推荐产品</a-button>
              <a-button
                style="margin-left: 10px"
                type="primary"
                @click="() => openExcelImportModal('employee', '导入店员推荐体系产品')"
                >导入</a-button
              >
              <a-button
                style="margin-left: 10px"
                type="primary"
                @click="() => exportTableData('employee', '店员推荐体系产品')"
                >导出</a-button
              >
            </div>
          </a-collapse-panel>
        </a-collapse>
      </a-form>

      <!-- 底部按钮 -->
      <div class="modal-footer" v-if="!isView">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="submitLoading">保存</a-button>
        </a-space>
      </div>
      <div class="modal-footer" v-else>
        <a-button @click="handleCancel">关闭</a-button>
      </div>
    </div>
    <!-- 流程信息 -->
    <template v-if="activeKey == '2'">
      <div style="padding: 20px">
        <WorkflowChart ref="workflowChartRef" :key="`workflow-${activeKey}`" />
      </div>
    </template>
    <!-- 流转记录 -->
    <template v-if="activeKey == '3'">
      <div style="padding-left: 40px">
        <a-tabs v-if="isView" v-model:activeKey="activeKeyCurrent">
          <a-tab-pane key="1" tab="当前流程" />
        </a-tabs>
        <a-timeline v-if="approvalList.length">
          <a-timeline-item v-for="(item, index) in approvalList" :key="index">
            <div class="item-detail">
              <div class="name">
                <span class="stress-text">{{ item.activityName }}</span>
                <span>{{ item.nodes[0].approvaler[0].name }}</span>
              </div>
              <div class="status">
                <span class="stress-text">{{ APPROVAL_STATUS[item.nodes[0].displayStatus] }}</span>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </template>
  </a-modal>

  <!-- 品规选择弹窗 -->
  <a-modal
    v-model:visible="productSelectVisible"
    :title="productSelectTitle"
    width="800px"
    :footer="null"
    :destroy-on-close="true"
  >
    <div style="padding: 0 20px 20px 20px">
      <!-- 搜索区域 -->
      <!-- <div class="search-area" style="margin-bottom: 16px">
        <a-input
          v-model:value="productSearchForm.keyword"
          placeholder="请输入产品名称或规格搜索"
          style="width: 300px; margin-right: 16px"
          @press-enter="searchProducts"
        />
        <a-button type="primary" @click="searchProducts">搜索</a-button>
        <a-button @click="resetProductSearch" style="margin-left: 8px">重置</a-button>
      </div> -->

      <!-- 产品列表 -->
      <a-table
        :columns="productSelectColumns"
        :data-source="productSelectData"
        :loading="productSelectLoading"
        :pagination="productSelectPagination"
        :row-selection="productRowSelection"
        row-key="id"
        size="small"
        @change="handleProductTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'index'">
            {{
              (productSelectPagination.current - 1) * productSelectPagination.pageSize +
              record.index +
              1
            }}
          </template>
        </template>
      </a-table>

      <!-- 底部按钮 -->
      <div class="modal-footer" style="margin-top: 16px; text-align: right">
        <a-space>
          <a-button @click="cancelProductSelect">取消</a-button>
          <a-button
            type="primary"
            @click="confirmProductSelect"
            :disabled="!selectedProductKeys.length"
          >
            确定
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>

  <!-- Excel导入弹窗 -->
  <a-modal
    v-model:visible="excelImportVisible"
    :title="excelImportTitle"
    width="600px"
    :footer="null"
    :destroy-on-close="true"
    :maskClosable="false"
  >
    <div style="padding: 20px">
      <div class="import-area">
        <a-upload
          :file-list="fileList"
          :before-upload="beforeUpload"
          @remove="handleRemove"
          accept=".xlsx,.xls"
          :multiple="false"
        >
          <a-button>
            <upload-outlined />
            选择Excel文件
          </a-button>
        </a-upload>
        <div style="margin-top: 16px; color: #666; font-size: 12px">
          <p>支持格式：.xlsx、.xls</p>
          <p>请确保Excel文件格式正确，具体格式要求请参考模板</p>
          <a-button type="link" @click="downloadTemplate" style="padding: 0; margin-top: 8px">
            下载导入模板
          </a-button>
        </div>
      </div>

      <!-- 解析结果预览 -->
      <div v-if="parsedData.length > 0" style="margin-top: 20px">
        <a-divider>解析结果预览</a-divider>
        <a-table
          :columns="previewColumns"
          :data-source="parsedData"
          :pagination="{ pageSize: 5 }"
          size="small"
          bordered
        />
        <div style="margin-top: 8px; color: #666; font-size: 12px">
          共解析到 {{ parsedData.length }} 条数据
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="modal-footer" style="margin-top: 20px; text-align: right">
        <a-space>
          <a-button @click="cancelExcelImport">取消</a-button>
          <a-button
            type="primary"
            @click="confirmExcelImport"
            :disabled="!parsedData.length"
            :loading="importLoading"
          >
            确定导入
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import cTable from '/@/views/components/Table/index.vue';
  import { reactive, ref, onMounted } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import * as XLSX from 'xlsx';
  import {
    getContractPage,
    addContract,
    contractUpdate,
    contractInfo,
    submitApproval,
    signNodelist,
    getOtcContractEnterpriseList,
    generateSerialNumber,
    contractPushRetry,
    getApproveUrl,
  } from '/@/api/protocolManage/chainPartnership';
  import { getAreaProvinceList, getAreaList } from '/@/api/system/area';
  import dayjs from 'dayjs';
  import { getContractList, infoFieldList } from '/@/api/protocolManage/distributorManagement';
  import { getPageProduct } from '/@/api/purchaseSaleStock/store';
  import WorkflowChart from '/@/components/WorkflowChart/index.vue';
  import { downloadTemplate as downloadExcelTemplate } from '/@/utils/excelTemplates';
  import { jsonToSheetXlsx } from '/@/components/Excel';

  const { createMessage } = useMessage();

  // 搜索表单
  const searchForm = reactive({
    code: '',
    name: '',
    signType: undefined,
  });
  const activeKey = ref('1');
  const activeKeyCurrent = ref('1');
  const approvalList = ref([]);
  const workflowChartRef = ref();
  // 弹窗相关
  const modalVisible = ref(false);
  const modalTitle = ref('');
  const modalType = ref(''); // add, edit, view
  const isView = ref(false);
  const submitLoading = ref(false);
  const formRef = ref();
  const activeKeys = ref([
    'basic',
    'content',
    'settlement',
    'validity',
    'products',
    'tasks',
    'employees',
  ]);

  // 地区级联选择器数据
  const areaOptions = ref<any[]>([]);
  const loadingArea = ref(false);

  // 乙方名称下拉数据
  const companyNameOptions = ref<any[]>([]);
  const companyNameLoading = ref(false);
  const companyNamePagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0,
    hasMore: true,
  });

  // 商业供货单位下拉数据
  const supplierOptions = ref<any[]>([]);
  const supplierLoading = ref(false);
  const supplierPagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0,
    hasMore: true,
  });

  // 品规选择弹窗相关数据
  const productSelectVisible = ref(false);
  const productSelectTitle = ref('');
  const productSelectType = ref(''); // product, task, employee
  const productSelectLoading = ref(false);
  const productSelectData = ref<any[]>([]);
  const selectedProductKeys = ref<string[]>([]);
  const selectedProducts = ref<any[]>([]);

  // 品规搜索表单
  const productSearchForm = reactive({
    keyword: '',
  });

  // 品规选择分页
  const productSelectPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
  });

  // 品规选择表格列定义
  const productSelectColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      customRender: ({ index }: any) => index + 1,
    },
    {
      title: '产品名称',
      dataIndex: 'newProductgroupidName',
      key: 'newProductgroupidName',
    },
    {
      title: '产品规格',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '单位',
      dataIndex: 'defaultUoMscheduleIdName',
      key: 'defaultUoMscheduleIdName',
      width: 80,
    },
  ];

  // Excel导入相关数据
  const excelImportVisible = ref(false);
  const excelImportTitle = ref('');
  const excelImportType = ref(''); // product, task, employee
  const importLoading = ref(false);
  const fileList = ref<any[]>([]);
  const parsedData = ref<any[]>([]);
  const previewColumns = ref<any[]>([]);

  // 品规多选配置
  const productRowSelection = {
    selectedRowKeys: selectedProductKeys,
    onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
      selectedProductKeys.value = selectedRowKeys;
      selectedProducts.value = selectedRows;
    },
    getCheckboxProps: (record: any) => ({
      disabled: false,
      name: record.productName,
    }),
  };

  // 签署节点表格列定义
  const signNodeColumns = [
    {
      title: '签约方名称',
      dataIndex: 'counterpartyName',
      width: 120,
    },
    {
      title: '相对方编码',
      dataIndex: 'counterpartyCode',
      width: 120,
    },
    {
      title: '签约方式',
      dataIndex: 'signingMethodName',
      width: 120,
    },
    {
      title: '签章类型',
      dataIndex: 'signingTypeName',
      width: 120,
    },
    {
      title: '签约人姓名',
      dataIndex: 'signatoryName',
      width: 120,
    },
    {
      title: '签约人手机号',
      dataIndex: 'signatoryPhone',
      width: 130,
    },
    {
      title: '是否加盖骑缝章',
      dataIndex: 'straddleStamp',
      width: 130,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      fixed: 'right',
    },
  ];

  // 表单数据
  const formData = reactive({
    id: undefined as string | undefined,
    code: '',
    // name: '',
    contractType: undefined as string | undefined,
    productType: undefined as string | undefined,
    // provinceCode: '',
    provinceName: '',
    // cityCode: '',
    // cityName: '',
    // districtCode: '',
    // districtName: '',
    areaValues: [] as string[],
    companyName: undefined as string | undefined,
    companyId: undefined as string | undefined,
    supplyMerchantName: undefined as string | undefined,
    supplyMerchantId: undefined as string | undefined, // 商业供货单位ID
    supplyId: undefined as string | undefined, // 商业供货单位ID
    recruiterName: '',
    recruiterId: '', // 甲方授权代表康缘工号
    recruiterPhone: '', // 甲方联系人电话
    deptName: '',
    recruiterCompanyId: [],
    channel: '',
    purchaseTaskBreakdown: '',
    terminalTaskBreakdown: '',
    lastYearSaleMoney: null as number | null,
    yearTaskNumType: undefined as string | undefined,
    quarterTaskNumType: undefined as string | undefined,
    b2cAmountType: undefined as string | undefined, // B2C核算类型
    costAmountType: undefined as string | undefined,
    firstUpperReachSecond: null as number | null, // 第二阶梯达成金额等于第一阶梯达成金额上限
    secondUpperReachThird: null as number | null, // 第三阶梯达成金额等于第二阶梯达成金额上限
    firstStepAwardRate: null as number | null,
    firstStepReachLowerLimit: null as number | null,
    firstStepReachUpperLimit: null as number | null,
    secondStepAwardRate: null as number | null,
    secondStepReachUpperLimit: null as number | null,
    thirdStepAwardRate: null as number | null,
    ladderStepAmountType: undefined as string | undefined,
    coverageRate: null as number | null,
    directSaleStoreNum: null as number | null,
    joinSaleStoreNum: null as number | null,
    singleStoreBoxNum: null as number | null,
    signType: undefined as string | undefined,
    signingMethod: undefined as string | undefined,
    signingMethodName: undefined as string | undefined,
    signingSort: '', // 签约顺序
    signingSortName: undefined as string | undefined,
    signingNumber: null as number | null,
    useNumber: null as number | null,
    straddleUseNumber: null as number | null,
    signatoryType: '',
    signatoryName: '',
    signatoryPhone: '',
    signatoryIdNumber: '',
    companyCreditCode: '',
    companyPhone: '',
    settleAccountName: '',
    settleBankName: '',
    settleCardNumber: '',
    deliveryAddress: '',
    deliveryEmail: '',
    startTime: null as any,
    endTime: null as any,
    contractProducts: [] as any[],
    contractProductTasks: [] as any[],
    contractProductEmployees: [] as any[],
    signNodes: [] as any[],
  });

  // 表单验证规则
  const formRules = reactive({
    // 基本信息
    code: [{ required: true, message: '请输入合同编码', trigger: 'blur' }],
    contractType: [{ required: true, message: '请选择协议类型', trigger: 'change' }],
    // productType: [{ required: true, message: '请选择产品类型', trigger: 'change' }],
    // signType: [{ required: true, message: '请选择签署类型', trigger: 'change' }],
    // areaValues: [{ required: true, message: '请选择地区', trigger: 'change' }],
    useNumber: [{ required: true, message: '请输入用章次数', trigger: 'blur' }], // 用章次数校验规则，初始为空，后续可动态赋值
    straddleUseNumber: [{ required: true, message: '请输入骑缝章用章次数', trigger: 'blur' }], // 骑缝章用章次数校验规则，初始为空，后续可动态赋值

    // 协议内容
    recruiterCompanyId: [{ required: true, message: '请选择我方签约主体名称', trigger: 'change' }],
    companyId: [{ required: true, message: '请选择乙方名称', trigger: 'change' }],
    supplyId: [{ required: true, message: '请选择商业供货单位', trigger: 'change' }],
    // lastYearSaleMoney: [{ required: true, message: '请输入连锁上一年度销售规模', trigger: 'blur' }],
    // directSaleStoreNum: [{ required: true, message: '请输入直营店家数', trigger: 'blur' }],
    // joinSaleStoreNum: [{ required: true, message: '请输入加盟店家数', trigger: 'blur' }],
    // quarterTaskNumType: [{ required: true, message: '请选择季度任务量类型', trigger: 'change' }],
    // yearTaskNumType: [{ required: true, message: '请选择年度任务量类型', trigger: 'change' }],
    // b2cAmountType: [{ required: true, message: '请选择B2C核算类型', trigger: 'change' }],
    // costAmountType: [{ required: true, message: '请选择费用核算类型', trigger: 'change' }],
    // firstUpperReachSecond: [
    //   {
    //     required: true,
    //     message: '请输入第二阶梯达成金额等于第一阶梯达成金额上限',
    //     trigger: 'blur',
    //   },
    // ],
    // secondUpperReachThird: [
    //   {
    //     required: true,
    //     message: '请输入第三阶梯达成金额等于第二阶梯达成金额上限',
    //     trigger: 'blur',
    //   },
    // ],
    // firstStepReachUpperLimit: [
    //   { required: true, message: '请输入第一阶梯达成金额上限', trigger: 'blur' },
    // ],
    // firstStepReachLowerLimit: [
    //   { required: true, message: '请输入第一阶梯达成金额下限', trigger: 'blur' },
    // ],
    // secondStepReachUpperLimit: [
    //   { required: true, message: '请输入第二阶梯达成金额上限', trigger: 'blur' },
    // ],
    // firstStepAwardRate: [
    //   { required: true, message: '请输入第一阶梯任务奖励百分比', trigger: 'blur' },
    // ],
    // secondStepAwardRate: [
    //   { required: true, message: '请输入第二阶梯任务奖励百分比', trigger: 'blur' },
    // ],
    // thirdStepAwardRate: [
    //   { required: true, message: '请输入第三阶梯任务奖励百分比', trigger: 'blur' },
    // ],
    // coverageRate: [{ required: true, message: '请输入铺货率', trigger: 'blur' }],
    // singleStoreBoxNum: [{ required: true, message: '请输入单店要求盒数', trigger: 'blur' }],
    // ladderStepAmountType: [
    //   { required: true, message: '请选择阶梯考核数量类型', trigger: 'change' },
    // ],
    // recruiterName: [{ required: true, message: '请输入甲方授权代表', trigger: 'blur' }],
    // recruiterId: [{ required: true, message: '请输入甲方授权代表康缘工号', trigger: 'blur' }],
    // recruiterPhone: [{ required: true, message: '请输入甲方联系人电话', trigger: 'blur' }],
    // signatoryName: [{ required: true, message: '请输入乙方授权代表', trigger: 'blur' }],
    // signatoryPhone: [{ required: true, message: '请输入乙方联系人电话', trigger: 'blur' }],
    signingMethod: [{ required: true, message: '请选择签约方式', trigger: 'change' }],

    // 结算信息
    // settleAccountName: [{ required: true, message: '请输入结算账户名称', trigger: 'blur' }],
    // settleCardNumber: [{ required: true, message: '请输入结算账户卡号', trigger: 'blur' }],
    // settleBankName: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],

    // 协议效期
    startTime: [{ required: true, message: '请选择有效期从', trigger: 'change' }],
    endTime: [{ required: true, message: '请选择有效期至', trigger: 'change' }],
  });
  const onPush = async (record: any) => {
    try {
      if (['1', '4', '5'].includes(record.approvalStatus)) {
        await submitApproval([record.id]);
        createMessage.success('推送OA成功');
        // 刷新表格数据
        getList(1);
      } else if (record.approvalStatus === '3') {
        await contractPushRetry([record.id]);
        createMessage.success('推送合同系统成功');
        // 刷新表格数据
        getList(1);
      }
    } catch (error) {
      console.error('推送失败:', error);
      createMessage.error('推送失败');
    }
  };

  // 地区级联选择器方法
  const loadAreaData = async (selectedOptions?: any[]) => {
    const targetOption = selectedOptions?.[selectedOptions.length - 1];

    if (!targetOption) {
      // 加载省份数据
      try {
        loadingArea.value = true;
        const provinces = await getAreaProvinceList();
        areaOptions.value = (provinces as unknown as any[]).map((item: any) => ({
          value: item.code,
          label: item.name,
          isLeaf: false,
          id: item.id,
        }));
      } catch (error) {
        console.error('加载省份数据失败:', error);
      } finally {
        loadingArea.value = false;
      }
    } else {
      // 加载市或区县数据
      try {
        targetOption.loading = true;
        const children = await getAreaList({ id: targetOption.id });
        targetOption.children = children.map((item: any) => ({
          value: item.code,
          label: item.name,
          isLeaf: item.layer === 3, // 第三层（区县）为叶子节点
          id: item.id,
        }));
      } catch (error) {
        console.error('加载地区数据失败:', error);
      } finally {
        targetOption.loading = false;
      }
    }
  };

  // 编辑时加载地区数据以支持回显
  const loadAreaDataForEdit = async (areaValues: string[]) => {
    if (!areaValues || areaValues.length === 0) return;

    try {
      loadingArea.value = true;

      // 首先加载省份数据
      const provinces = await getAreaProvinceList();
      areaOptions.value = (provinces as unknown as any[]).map((item: any) => ({
        value: item.code,
        label: item.name,
        isLeaf: false,
        id: item.id,
      }));

      // 如果有省份代码，找到对应的省份并加载城市数据
      if (areaValues.length > 0) {
        const provinceOption = areaOptions.value.find((item) => item.value === areaValues[0]);
        if (provinceOption && areaValues.length > 1) {
          const cities = await getAreaList({ id: provinceOption.id });
          provinceOption.children = cities.map((item: any) => ({
            value: item.code,
            label: item.name,
            isLeaf: item.layer === 3,
            id: item.id,
          }));

          // 如果有城市代码，找到对应的城市并加载区县数据
          if (areaValues.length > 2) {
            const cityOption = provinceOption.children.find(
              (item: any) => item.value === areaValues[1],
            );
            if (cityOption && !cityOption.isLeaf) {
              const districts = await getAreaList({ id: cityOption.id });
              cityOption.children = districts.map((item: any) => ({
                value: item.code,
                label: item.name,
                isLeaf: true,
                id: item.id,
              }));
            }
          }
        }
      }
    } catch (error) {
      console.error('加载编辑地区数据失败:', error);
    } finally {
      loadingArea.value = false;
    }
  };

  // const handleAreaChange = (value: string[], _selectedOptions: any[]) => {
  //   formData.areaValues = value;
  //   if (value && value.length > 0 && _selectedOptions && _selectedOptions.length > 0) {
  //     // 清空所有区域相关字段
  //     formData.provinceCode = '';
  //     formData.provinceName = '';
  //     formData.cityCode = '';
  //     formData.cityName = '';
  //     formData.districtCode = '';
  //     formData.districtName = '';

  //     // 省份信息
  //     if (_selectedOptions[0]) {
  //       formData.provinceCode = _selectedOptions[0].value;
  //       formData.provinceName = _selectedOptions[0].label;
  //     }

  //     // 城市信息
  //     if (_selectedOptions[1]) {
  //       formData.cityCode = _selectedOptions[1].value;
  //       formData.cityName = _selectedOptions[1].label;
  //     }

  //     // 区县信息
  //     if (_selectedOptions[2]) {
  //       formData.districtCode = _selectedOptions[2].value;
  //       formData.districtName = _selectedOptions[2].label;
  //     }
  //   }
  // };
  const companyId = ref('');
  // 乙方名称搜索相关方法
  const handleCompanyNameSearch = async (searchValue: string) => {
    if (!searchValue.trim()) {
      companyNameOptions.value = [];
      return;
    }
    companyId.value = searchValue;
    companyNameLoading.value = true;
    companyNamePagination.current = 1;
    companyNamePagination.hasMore = true;

    try {
      const params = {
        limit: companyNamePagination.current,
        size: companyNamePagination.pageSize,
        relativeName: searchValue.trim(),
      };

      const res = await getContractList(params);
      if (res && res.list) {
        companyNameOptions.value = res.list.map((item: any) => ({
          value: item.id || item.relativeName,
          label: item.relativeName,
          ...item,
        }));
        companyNamePagination.total = res.total || 0;
        companyNamePagination.hasMore = res.list.length === companyNamePagination.pageSize;
      }
    } catch (error) {
      console.error('搜索乙方名称失败:', error);
      companyNameOptions.value = [];
    } finally {
      companyNameLoading.value = false;
    }
  };

  const handleCompanyNameDropdownChange = (open: boolean) => {
    if (open && companyNameOptions.value.length === 0) {
      // 下拉框打开时，如果没有数据，可以加载默认数据
      handleCompanyNameSearch('');
    }
  };

  const handleCompanyNameScroll = async (e: Event) => {
    const { target } = e;
    if (!target || !companyNamePagination.hasMore || companyNameLoading.value) return;

    const { scrollTop, scrollHeight, clientHeight } = target as HTMLElement;
    if (scrollTop + clientHeight >= scrollHeight - 10) {
      // 滚动到底部，加载更多数据
      companyNameLoading.value = true;
      companyNamePagination.current += 1;

      try {
        const params = {
          limit: companyNamePagination.current,
          size: companyNamePagination.pageSize,
          relativeName: companyId.value, // 这里可以保存上次的搜索关键字
        };

        const res = await getContractList(params);
        if (res && res.list) {
          const newOptions = res.list.map((item: any) => ({
            value: item.id || item.relativeName,
            label: item.relativeName,
            ...item,
          }));
          companyNameOptions.value = [...companyNameOptions.value, ...newOptions];
          companyNamePagination.hasMore = res.list.length === companyNamePagination.pageSize;
        }
      } catch (error) {
        console.error('加载更多乙方名称失败:', error);
      } finally {
        companyNameLoading.value = false;
      }
    }
  };

  // 乙方名称选择改变时的处理
  const handleCompanyNameChange = async (value: string, option: any) => {
    console.log('乙方名称选择改变:', value, option);

    // 这里可以根据选择的乙方名称调用相关接口
    if (value && option) {
      try {
        // 示例：根据选择的乙方名称获取相关信息
        const params = {
          id: value,
        };
        formData.signingMethod = undefined;

        // 调用相关接口，比如获取该公司的详细信息
        const companyInfo = await infoFieldList(params);
        console.log('调用接口参数:', companyInfo);

        // 可以在这里更新表单的其他字段
        formData.companyName = companyInfo.relativeName;
        formData.companyCreditCode = companyInfo.certificateNo;
      } catch (error) {
        console.error('获取乙方信息失败:', error);
        createMessage.error('获取乙方信息失败');
      }
    }
  };
  const supplierName = ref('');
  // 商业供货单位搜索相关方法
  const handleSupplierSearch = async (searchValue: string) => {
    if (!searchValue.trim()) {
      supplierOptions.value = [];
      return;
    }
    supplierName.value = searchValue;
    supplierLoading.value = true;
    supplierPagination.current = 1;
    supplierPagination.hasMore = true;

    try {
      const params = {
        limit: supplierPagination.current,
        size: supplierPagination.pageSize,
        relativeName: searchValue.trim(),
      };

      const res = await getContractList(params);
      if (res && res.list) {
        supplierOptions.value = res.list.map((item: any) => ({
          value: item.id || item.relativeName,
          label: item.relativeName,
          ...item,
        }));
        supplierPagination.total = res.total || 0;
        supplierPagination.hasMore = res.list.length === supplierPagination.pageSize;
      }
    } catch (error) {
      console.error('搜索商业供货单位失败:', error);
      supplierOptions.value = [];
    } finally {
      supplierLoading.value = false;
    }
  };

  const handleSupplierDropdownChange = (open: boolean) => {
    if (open && supplierOptions.value.length === 0) {
      // 下拉框打开时，如果没有数据，可以加载默认数据
      handleSupplierSearch('');
    }
  };

  const handleSupplierScroll = async (e: Event) => {
    const { target } = e;
    if (!target || !supplierPagination.hasMore || supplierLoading.value) return;

    const { scrollTop, scrollHeight, clientHeight } = target as HTMLElement;
    if (scrollTop + clientHeight >= scrollHeight - 10) {
      // 滚动到底部，加载更多数据
      supplierLoading.value = true;
      supplierPagination.current += 1;

      try {
        const params = {
          limit: supplierPagination.current,
          size: supplierPagination.pageSize,
          relativeName: supplierName.value, // 这里可以保存上次的搜索关键字
        };

        const res = await getContractList(params);
        if (res && res.list) {
          const newOptions = res.list.map((item: any) => ({
            value: item.id || item.relativeName,
            label: item.relativeName,
            ...item,
          }));
          supplierOptions.value = [...supplierOptions.value, ...newOptions];
          supplierPagination.hasMore = res.list.length === supplierPagination.pageSize;
        }
      } catch (error) {
        console.error('加载更多商业供货单位失败:', error);
      } finally {
        supplierLoading.value = false;
      }
    }
  };

  // 商业供货单位选择改变时的处理
  const handleSupplierChange = async (value: string, option: any) => {
    console.log('商业供货单位选择改变:', value, option);

    // 这里可以根据选择的商业供货单位调用相关接口
    if (value && option) {
      try {
        // 示例：根据选择的乙方名称获取相关信息
        const params = {
          id: value,
        };

        // 调用相关接口，比如获取该公司的详细信息
        const companyInfo = await infoFieldList(params);
        console.log('调用接口参数:', companyInfo);

        // 可以在这里更新表单的其他字段
        formData.supplyMerchantName = companyInfo.relativeName;
        formData.supplyMerchantId = companyInfo.certificateNo;
      } catch (error) {
        console.error('获取商业供货单位信息失败:', error);
        createMessage.error('获取商业供货单位信息失败');
      }
    }
  };

  const reSet = () => {
    searchForm.code = '';
    searchForm.name = '';
    searchForm.signType = undefined;
    getList();
  };

  const signTypeOptions = reactive([
    { label: '新签', value: '1' },
    { label: '续签', value: '2' },
    { label: '变更', value: '3' },
  ]);

  // 表格列定义
  const productColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      customRender: ({ index }) => index + 1,
    },
    { title: '产品名称', dataIndex: 'productName', key: 'productName', width: 200 },
    { title: '规格', dataIndex: 'productFormatName', key: 'productFormatName', width: 200 },
    { title: '单位', dataIndex: 'unit', key: 'unit', width: 100 },
    { title: '协议价（元/盒）', dataIndex: 'negotiatedPrice', key: 'negotiatedPrice', width: 150 },
    {
      title: '建议零售价（元/盒）',
      dataIndex: 'suggestionPrice',
      key: 'suggestionPrice',
      width: 150,
    },
    { title: '操作', dataIndex: 'action', key: 'action', width: 100 },
  ];

  const taskColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      customRender: ({ index }) => index + 1,
    },
    { title: '产品名称', dataIndex: 'productName', key: 'productName', width: 150 },
    { title: '规格', dataIndex: 'productFormatName', key: 'productFormatName', width: 150 },
    // { title: '单位', dataIndex: 'unit', key: 'unit', width: 80 },
    { title: '全年任务量', dataIndex: 'yearTask', key: 'yearTask', width: 120 },
    { title: '第一季度任务量', dataIndex: 'firQuarterTask', key: 'firQuarterTask', width: 120 },
    { title: '第二季度任务量', dataIndex: 'twoQuarterTask', key: 'twoQuarterTask', width: 120 },
    { title: '第三季度任务量', dataIndex: 'thrQuarterTask', key: 'thrQuarterTask', width: 120 },
    { title: '第四季度任务量', dataIndex: 'fouQuarterTask', key: 'fouQuarterTask', width: 120 },
    {
      title: '合作费用标准（元/盒）',
      dataIndex: 'cooperationCharge',
      key: 'cooperationCharge',
      width: 150,
    },
    { title: '操作', dataIndex: 'action', key: 'action', width: 100 },
  ];

  const employeeColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      customRender: ({ index }) => index + 1,
    },
    { title: '产品名称', dataIndex: 'productName', key: 'productName', width: 200 },
    { title: '规格', dataIndex: 'productFormatName', key: 'productFormatName', width: 200 },
    // { title: '单位', dataIndex: 'unit', key: 'unit', width: 100 },
    {
      title: '连锁店员单品激励政策',
      dataIndex: 'incentivePolicy',
      key: 'incentivePolicy',
      width: 200,
    },
    {
      title: '连锁货架推荐符号或标志',
      dataIndex: 'suggestionMark',
      key: 'suggestionMark',
      width: 200,
    },
    { title: '操作', dataIndex: 'action', key: 'action', width: 100 },
  ];

  const fileStatus = reactive({
    1: '未生成',
    2: '已生成',
    3: '生成失败',
  });
  const pushContractStatus = reactive({
    1: '未推送',
    2: '已推送',
    3: '推送失败',
  });
  const tableColumns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center', width: 80 },
    {
      title: '协议名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      width: 200,
    },
    {
      title: '协议编码',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
      width: 120,
    },
    {
      title: '合作方',
      dataIndex: 'companyName',
      key: 'companyName',
      align: 'center',
      width: 200,
    },
    {
      title: '生效日期',
      dataIndex: 'startTime',
      key: 'startTime',
      align: 'center',
    },
    {
      title: '失效日期',
      dataIndex: 'endTime',
      key: 'endTime',
      align: 'center',
    },
    {
      title: '负责人员',
      dataIndex: 'recruiterName',
      key: 'recruiterName',
      align: 'center',
    },
    // {
    //   title: '负责人部门',
    //   dataIndex: 'deptName',
    //   key: 'deptName',
    //   align: 'center',
    // },
    {
      title: '协议状态',
      dataIndex: 'contractStatusName',
      key: 'contractStatusName',
      align: 'center',
    },
    {
      title: '审批状态',
      dataIndex: 'approvalStatusName',
      key: 'approvalStatusName',
      align: 'center',
    },
    // {
    //   title: '附件生成状态',
    //   dataIndex: 'fileStatus',
    //   key: 'fileStatus',
    //   align: 'center',
    //   isSlot: true,
    // },
    {
      title: '推送合同系统状态',
      dataIndex: 'pushContractStatus',
      key: 'pushContractStatus',
      align: 'center',
      isSlot: true,
    },
    {
      title: '合同系统状态',
      dataIndex: 'contractSystemStatusName',
      key: 'contractSystemStatusName',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
      width: 240,
    },
  ];
  const tableData = reactive({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 7,
    pageSize: 10,
  });
  // 表格多选
  const selectedKeys = ref<any[]>([]);
  const onSelectChange = (keys: any[]) => {
    selectedKeys.value = keys;
  };
  // 获取表格数据
  const getList = async (flag?: number) => {
    selectedKeys.value = [];
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      let temp = {
        ...searchForm,
        limit: pagination.currentPage,
        size: pagination.pageSize,
        contractType: 1,
      };
      let res = await getContractPage(temp);
      tableData.data = res?.list.map((item, index) => ({
        ...item,
        index: (pagination.currentPage - 1) * pagination.pageSize + index + 1,
      }));
      pagination.totalItems = res.total ?? 0;
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  };
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };
  const signingMethodName = {
    ESEAL: '电子签约',
    PHYSICALSEAL: '纸质签约',
    MIXTURE: '电签+纸质',
  };
  const handleBlur = (field: string) => {
    if (field === 'signatoryName') {
      formData.signNodes.forEach((item) => {
        if (item.type === '2') {
          item.signatoryName = formData.signatoryName;
        }
      });
    } else if (field === 'signatoryPhone') {
      formData.signNodes.forEach((item) => {
        if (item.type === '2') {
          item.signatoryPhone = formData.signatoryPhone;
        }
      });
    }
  };
  const signChange = async (value: any) => {
    formData.signingMethodName = signingMethodName[value];
    formData.signingNumber = 4;
    // if (value === 'PHYSICALSEAL') {
    //   formRules.useNumber[0].required = true;
    // } else {
    //   formRules.useNumber[0].required = false;
    // }
    if (value === 'ESEAL' || value === 'PHYSICALSEAL') {
      formData.signingSort = 'DISORDER';
      formData.signingSortName = '无序签署';
    } else {
      formData.signingSort = 'ORDER';
      formData.signingSortName = '无序签署';
    }
    if (formData.companyId && formData.recruiterCompanyId.length > 0) {
      const ownerIds = recruiterList.value
        .filter((item) => formData.recruiterCompanyId.includes(item.enterpriseCode))
        .map((item) => item.id);
      const res = await signNodelist({
        signingMethod: value,
        agentId: formData.companyId,
        ownerIds,
      });
      // 处理签署节点数据，确保包含显示名称字段
      const processedSignNodes =
        res.map((node: any) => ({
          ...node,
          signingMethodName: signingMethodName[node.signingMethod] || node.signingMethod,
          signingTypeName: getSigningTypeName(node.signingType),
          counterpartyName: node.name,
          signatoryName: node.type === '2' ? formData.signatoryName : node.signatoryName,
          signatoryPhone: node.type === '2' ? formData.signatoryPhone : node.signatoryPhone,
          editing: false,
        })) || [];

      formData.signNodes = processedSignNodes;
    }
  };

  // 获取签章类型名称
  const getSigningTypeName = (type: string) => {
    const typeMap: Record<string, string> = {
      COMPANY_SEAL: '企业盖章',
      PERSONAL_SIGN: '个人签字',
      LP_SIGN: '法定代表人签字',
    };
    return typeMap[type] || type;
  };
  onMounted(() => {
    getList();
    loadAreaData(); // 初始化加载省份数据
  });
  // 弹窗操作方法
  const resetFormData = () => {
    Object.assign(formData, {
      id: undefined,
      code: '',
      // name: '',
      contractType: '',
      productType: undefined,
      // provinceCode: '',
      provinceName: '',
      // cityCode: '',
      // cityName: '',
      // districtCode: '',
      // districtName: '',
      areaValues: [],
      companyName: undefined,
      companyId: undefined,
      supplyMerchantId: undefined,
      supplyId: undefined,
      supplyMerchantName: undefined,
      recruiterName: '',
      recruiterId: '', // 甲方授权代表康缘工号
      recruiterPhone: '',
      deptName: '',
      recruiterCompanyId: [],
      channel: '',
      purchaseTaskBreakdown: '',
      terminalTaskBreakdown: '',
      lastYearSaleMoney: null,
      yearTaskNumType: undefined,
      quarterTaskNumType: undefined,
      b2cAmountType: undefined,
      costAmountType: undefined,
      firstUpperReachSecond: null,
      secondUpperReachThird: null,
      firstStepAwardRate: null,
      firstStepReachLowerLimit: null,
      firstStepReachUpperLimit: null,
      secondStepAwardRate: null,
      secondStepReachUpperLimit: null,
      thirdStepAwardRate: null,
      ladderStepAmountType: undefined,
      coverageRate: null,
      directSaleStoreNum: null,
      joinSaleStoreNum: null,
      singleStoreBoxNum: null,
      signType: undefined,
      signingMethod: undefined,
      signingMethodName: undefined,
      signingSort: '',
      signingSortName: undefined,
      signingNumber: null,
      useNumber: null,
      straddleUseNumber: null,
      signatoryType: '',
      signatoryName: '',
      signatoryPhone: '',
      signatoryIdNumber: '',
      companyCreditCode: '',
      companyPhone: '',
      settleAccountName: '',
      settleBankName: '',
      settleCardNumber: '',
      deliveryAddress: '',
      deliveryEmail: '',
      startTime: null,
      endTime: null,
      contractProducts: [],
      contractProductTasks: [],
      contractProductEmployees: [],
      signNodes: [],
    });

    // 重置下拉选项
    companyNameOptions.value = [];
    supplierOptions.value = [];
    companyNamePagination.current = 1;
    companyNamePagination.hasMore = true;
    supplierPagination.current = 1;
    supplierPagination.hasMore = true;
  };
  const recruiterList = ref<any[]>([]);
  const openModal = async (type: string, title: string, record?: any) => {
    modalType.value = type;
    modalTitle.value = title;
    isView.value = type === 'view';
    modalVisible.value = true;
    const res = await getOtcContractEnterpriseList();
    recruiterList.value = res;

    if (type === 'add') {
      resetFormData();
      const code = await generateSerialNumber();
      formData.code = code || '';
    } else if (type === 'edit' || type === 'view') {
      loadContractInfo(record.id);
      if (type === 'view') {
        const res = await getApproveUrl({ id: record.id });
        console.log(res, '=============================getApproveUrl');
        approvalList.value = res.data || [];
      }
    }
  };

  const loadContractInfo = async (id: string) => {
    try {
      const res = await contractInfo(id);
      if (res) {
        // 构建地区级联选择器的值
        const areaValues: string[] = [];
        if (res.provinceCode) areaValues.push(res.provinceCode);
        if (res.cityCode) areaValues.push(res.cityCode);
        if (res.districtCode) areaValues.push(res.districtCode);

        // 先加载地区数据以支持回显
        await loadAreaDataForEdit(areaValues);

        // 处理签署节点数据，确保包含显示名称字段
        const processedSignNodes = (res.signNodes || []).map((node: any) => ({
          ...node,
          signingMethodName: signingMethodName[node.signingMethod] || node.signingMethod,
          signingTypeName: getSigningTypeName(node.signingType),
          editing: false,
        }));
        companyNameOptions.value = [{ value: res.companyId, label: res.companyName }];
        supplierOptions.value = [{ value: res.supplyMerchantId, label: res.supplyMerchantName }];
        Object.assign(formData, {
          ...res,
          areaValues,
          startTime: res.startTime ? dayjs(res.startTime) : null,
          endTime: res.endTime ? dayjs(res.endTime) : null,
          contractProducts: res.contractProducts || [],
          contractProductTasks: res.contractProductTasks || [],
          contractProductEmployees: res.contractProductEmployees || [],
          signNodes: processedSignNodes,
          recruiterCompanyId: res.recruiterCompanyId.split(',') || [],
          supplyId: res.supplyMerchantId,
          useNumber: res.signNodes[0]?.useNumber || null,
          straddleUseNumber: res.signNodes[0]?.straddleUseNumber || null,
        });
      }
    } catch (error) {
      console.error('加载协议信息失败:', error);
      createMessage.error('加载协议信息失败');
    }
  };

  const handleCancel = () => {
    modalVisible.value = false;
    formRef.value?.resetFields();
  };
  const signTypeName = {
    1: '新签',
    2: '续签',
    3: '变更',
  };
  const productTypeName = {
    1: '单产品协议',
    2: '多产品协议',
  };
  const handleSubmit = async () => {
    try {
      // 先验证基础表单
      await formRef.value?.validate();

      // 再验证表格字段
      // if (!validateTableFields()) {
      //   return;
      // }
      let arr = formData.signNodes.filter((item) => item.type === '2');
      if (formData.signingMethod === 'ESEAL' && !arr[0].signatoryName && !arr[0].signatoryPhone) {
        createMessage.error('电子签约的时候，签约人姓名和手机号必填');
        return;
      }

      submitLoading.value = true;
      const recruiterCompanyName = recruiterList.value
        .filter((item: any) => formData.recruiterCompanyId.includes(item.enterpriseCode))
        .map((item: any) => item.enterpriseName);

      const submitData: any = {
        ...formData,
        startTime: formData.startTime ? formData.startTime.format('YYYY-MM-DD 00:00:00') : null,
        endTime: formData.endTime ? formData.endTime.format('YYYY-MM-DD 00:00:00') : null,
        contractTypeName: '连锁合作协议',
        signTypeName: signTypeName[formData.signType as any],
        productTypeName: productTypeName[formData.productType as any],
        recruiterCompanyId: formData.recruiterCompanyId.join(','),
        recruiterCompanyName: recruiterCompanyName.join(','),
        signatoryType: 1,
        signatoryTypeName: '企业',
        yearTaskNumType: formData.quarterTaskNumType,
        signNodes: formData.signNodes.map((node: any) => ({
          ...node,
          useNumber: formData.useNumber || null,
          straddleUseNumber: formData.straddleUseNumber || null,
        })),
      };

      Reflect.deleteProperty(submitData, 'areaValues');
      Reflect.deleteProperty(submitData, 'supplyId');
      Reflect.deleteProperty(submitData, 'useNumber');
      Reflect.deleteProperty(submitData, 'straddleUseNumber');

      if (modalType.value === 'add') {
        await addContract(submitData);
        createMessage.success('新增协议成功');
      } else if (modalType.value === 'edit') {
        await contractUpdate(submitData);
        createMessage.success('编辑协议成功');
      }

      modalVisible.value = false;
      getList();
    } catch (error) {
      console.error('保存失败:', error);
      // createMessage.error('保存失败');
    } finally {
      submitLoading.value = false;
    }
  };

  // 表格字段验证
  const validateTableFields = () => {
    let isValid = true;
    let errorMessage = '';

    // 验证合作产品信息
    if (formData.contractProducts.length === 0) {
      errorMessage = '请至少添加一个合作产品';
      isValid = false;
    } else {
      for (let i = 0; i < formData.contractProducts.length; i++) {
        const product = formData.contractProducts[i];
        if (!product.productId) {
          errorMessage = `合作产品第${i + 1}行：请选择产品名称`;
          isValid = false;
          break;
        }
        if (!product.productFormatId) {
          errorMessage = `合作产品第${i + 1}行：请选择品规`;
          isValid = false;
          break;
        }
        if (!product.unit) {
          errorMessage = `合作产品第${i + 1}行：请输入单位`;
          isValid = false;
          break;
        }
        if (!product.negotiatedPrice) {
          errorMessage = `合作产品第${i + 1}行：请输入协议价`;
          isValid = false;
          break;
        }
        if (!product.suggestionPrice) {
          errorMessage = `合作产品第${i + 1}行：请输入建议零售价`;
          isValid = false;
          break;
        }
      }
    }

    // 验证年度任务量分解
    if (isValid && formData.contractProductTasks.length === 0) {
      errorMessage = '请至少添加一个年度任务';
      isValid = false;
    } else if (isValid) {
      for (let i = 0; i < formData.contractProductTasks.length; i++) {
        const task = formData.contractProductTasks[i];
        if (!task.productId) {
          errorMessage = `年度任务第${i + 1}行：请选择产品名称`;
          isValid = false;
          break;
        }
        if (!task.productFormatId) {
          errorMessage = `年度任务第${i + 1}行：请选择品规`;
          isValid = false;
          break;
        }
        if (!task.unit) {
          errorMessage = `年度任务第${i + 1}行：请输入单位`;
          isValid = false;
          break;
        }
        if (!task.yearTask) {
          errorMessage = `年度任务第${i + 1}行：请输入全年任务量`;
          isValid = false;
          break;
        }
        if (!task.cooperationCharge) {
          errorMessage = `年度任务第${i + 1}行：请输入合作费用标准`;
          isValid = false;
          break;
        }
      }
    }

    // 验证店员推荐体系产品
    if (isValid && formData.contractProductEmployees.length === 0) {
      errorMessage = '请至少添加一个店员推荐产品';
      isValid = false;
    } else if (isValid) {
      for (let i = 0; i < formData.contractProductEmployees.length; i++) {
        const employee = formData.contractProductEmployees[i];
        if (!employee.productId) {
          errorMessage = `店员推荐产品第${i + 1}行：请选择产品名称`;
          isValid = false;
          break;
        }
        if (!employee.productFormatId) {
          errorMessage = `店员推荐产品第${i + 1}行：请选择品规`;
          isValid = false;
          break;
        }
        if (!employee.unit) {
          errorMessage = `店员推荐产品第${i + 1}行：请输入单位`;
          isValid = false;
          break;
        }
        if (!employee.incentivePolicy) {
          errorMessage = `店员推荐产品第${i + 1}行：请输入连锁店员单品激励政策`;
          isValid = false;
          break;
        }
        if (!employee.suggestionMark) {
          errorMessage = `店员推荐产品第${i + 1}行：请输入连锁货架推荐符号或标志`;
          isValid = false;
          break;
        }
      }
    }

    // 验证签署节点（仅验证正在编辑的节点）
    if (isValid) {
      for (let i = 0; i < formData.signNodes.length; i++) {
        const signNode = formData.signNodes[i];
        // 只验证正在编辑的节点
        if (signNode.editing) {
          if (!signNode.type) {
            errorMessage = `签署节点第${i + 1}行：请选择签署方类型`;
            isValid = false;
            break;
          }
          if (!signNode.counterpartyCode) {
            errorMessage = `签署节点第${i + 1}行：请输入相对方编码`;
            isValid = false;
            break;
          }
          if (!signNode.signingMethod) {
            errorMessage = `签署节点第${i + 1}行：请选择签约方式`;
            isValid = false;
            break;
          }
          if (!signNode.signingType) {
            errorMessage = `签署节点第${i + 1}行：请选择签章类型`;
            isValid = false;
            break;
          }
          if (!signNode.signatoryName) {
            errorMessage = `签署节点第${i + 1}行：请输入签约人姓名`;
            isValid = false;
            break;
          }
          if (!signNode.signatoryPhone) {
            errorMessage = `签署节点第${i + 1}行：请输入签约人手机号`;
            isValid = false;
            break;
          }
        }
      }
    }

    if (!isValid) {
      createMessage.error(errorMessage);
    }

    return isValid;
  };

  // 品规选择弹窗方法
  const openProductSelectModal = (type: string, title: string) => {
    productSelectType.value = type;
    productSelectTitle.value = title;
    productSelectVisible.value = true;
    selectedProductKeys.value = [];
    selectedProducts.value = [];
    productSearchForm.keyword = '';
    productSelectPagination.current = 1;
    loadProductList();
  };

  // 加载品规列表
  const loadProductList = async () => {
    productSelectLoading.value = true;
    try {
      const params = {
        limit: productSelectPagination.current,
        size: productSelectPagination.pageSize,
        keyword: productSearchForm.keyword || undefined,
      };

      const res = await getPageProduct(params);
      if (res && res.list) {
        productSelectData.value = res.list.map((item: any, index: number) => ({
          ...item,
          index,
          id: item.id || `${item.productName}_${item.productFormatName}_${index}`,
          name: item.name.replace(item.newProductgroupidName, ''),
        }));
        productSelectPagination.total = res.total || 0;
      }
    } catch (error) {
      console.error('加载品规列表失败:', error);
      createMessage.error('加载品规列表失败');
    } finally {
      productSelectLoading.value = false;
    }
  };

  // 搜索品规
  const searchProducts = () => {
    productSelectPagination.current = 1;
    loadProductList();
  };

  // 重置搜索
  const resetProductSearch = () => {
    productSearchForm.keyword = '';
    productSelectPagination.current = 1;
    loadProductList();
  };

  // 处理表格分页变化
  const handleProductTableChange = (pagination: any) => {
    productSelectPagination.current = pagination.current;
    productSelectPagination.pageSize = pagination.pageSize;
    loadProductList();
  };

  // 取消选择
  const cancelProductSelect = () => {
    productSelectVisible.value = false;
    selectedProductKeys.value = [];
    selectedProducts.value = [];
  };

  // 确认选择
  const confirmProductSelect = () => {
    if (!selectedProducts.value.length) {
      createMessage.warning('请选择至少一个品规');
      return;
    }

    const type = productSelectType.value;
    let addedCount = 0;
    let duplicateCount = 0;

    selectedProducts.value.forEach((product) => {
      const newItem = {
        // id: Date.now() + Math.random(),
        productId: product.productId,
        productName: product.newProductgroupidName,
        productFormatId: product.newProductgroupid,
        productFormatName: product.name,
        unit: product.defaultUoMscheduleIdName,
      };

      // 检查是否已存在相同的品规（基于productId和productFormatId）
      let isDuplicate = false;
      let targetArray: any[] = [];

      if (type === 'product') {
        targetArray = formData.contractProducts;
      } else if (type === 'task') {
        targetArray = formData.contractProductTasks;
      } else if (type === 'employee') {
        targetArray = formData.contractProductEmployees;
      }

      // 检查是否存在重复
      isDuplicate = targetArray.some(
        (item) =>
          item.productName === newItem.productName &&
          item.productFormatName === newItem.productFormatName,
      );

      if (!isDuplicate) {
        // 不重复，添加到对应表格
        if (type === 'product') {
          formData.contractProducts.push({
            ...newItem,
            negotiatedPrice: null,
            suggestionPrice: null,
          });
        } else if (type === 'task') {
          formData.contractProductTasks.push({
            ...newItem,
            yearTask: null,
            firQuarterTask: null,
            twoQuarterTask: null,
            thrQuarterTask: null,
            fouQuarterTask: null,
            cooperationCharge: null,
          });
        } else if (type === 'employee') {
          formData.contractProductEmployees.push({
            ...newItem,
            incentivePolicy: '',
            suggestionMark: '',
          });
        }
        addedCount++;
      } else {
        duplicateCount++;
      }
    });

    productSelectVisible.value = false;
    selectedProductKeys.value = [];
    selectedProducts.value = [];

    // 显示添加结果消息
    if (addedCount > 0 && duplicateCount > 0) {
      createMessage.success(
        `成功添加 ${addedCount} 个品规，${duplicateCount} 个品规已存在未重复添加`,
      );
    } else if (addedCount > 0) {
      createMessage.success(`成功添加 ${addedCount} 个品规`);
    } else if (duplicateCount > 0) {
      createMessage.warning(`所选 ${duplicateCount} 个品规已存在，未重复添加`);
    }
  };

  // Excel导入方法
  const openExcelImportModal = (type: string, title: string) => {
    excelImportType.value = type;
    excelImportTitle.value = title;
    excelImportVisible.value = true;
    fileList.value = [];
    parsedData.value = [];
    setupPreviewColumns(type);
  };

  // 设置预览表格列
  const setupPreviewColumns = (type: string) => {
    if (type === 'product') {
      previewColumns.value = [
        { title: '产品名称', dataIndex: 'productName', key: 'productName' },
        { title: '规格', dataIndex: 'productFormatName', key: 'productFormatName' },
        { title: '单位', dataIndex: 'unit', key: 'unit' },
        { title: '协议价（元/盒）', dataIndex: 'negotiatedPrice', key: 'negotiatedPrice' },
        { title: '建议零售价（元/盒）', dataIndex: 'suggestionPrice', key: 'suggestionPrice' },
      ];
    } else if (type === 'task') {
      previewColumns.value = [
        { title: '产品名称', dataIndex: 'productName', key: 'productName' },
        { title: '规格', dataIndex: 'productFormatName', key: 'productFormatName' },
        // { title: '单位', dataIndex: 'unit', key: 'unit' },
        { title: '全年任务量', dataIndex: 'yearTask', key: 'yearTask' },
        { title: '第一季度任务量', dataIndex: 'firQuarterTask', key: 'firQuarterTask' },
        { title: '第二季度任务量', dataIndex: 'twoQuarterTask', key: 'twoQuarterTask' },
        { title: '第三季度任务量', dataIndex: 'thrQuarterTask', key: 'thrQuarterTask' },
        { title: '第四季度任务量', dataIndex: 'fouQuarterTask', key: 'fouQuarterTask' },
        {
          title: '合作费用标准（元/盒）',
          dataIndex: 'cooperationCharge',
          key: 'cooperationCharge',
        },
      ];
    } else if (type === 'employee') {
      previewColumns.value = [
        { title: '产品名称', dataIndex: 'productName', key: 'productName' },
        { title: '规格', dataIndex: 'productFormatName', key: 'productFormatName' },
        //{ title: '单位', dataIndex: 'unit', key: 'unit' },
        { title: '连锁店员单品激励政策', dataIndex: 'incentivePolicy', key: 'incentivePolicy' },
        { title: '连锁货架推荐符号或标志', dataIndex: 'suggestionMark', key: 'suggestionMark' },
      ];
    }
  };

  // 文件上传前处理
  const beforeUpload = (file: any) => {
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel';
    if (!isExcel) {
      createMessage.error('只能上传Excel文件！');
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      createMessage.error('文件大小不能超过10MB！');
      return false;
    }

    fileList.value = [file];
    parseExcelFile(file);
    return false; // 阻止自动上传
  };

  // 解析Excel文件
  const parseExcelFile = (file: any) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        processExcelData(jsonData);
      } catch (error) {
        console.error('解析Excel文件失败:', error);
        createMessage.error('解析Excel文件失败，请检查文件格式');
      }
    };
    reader.readAsArrayBuffer(file);
  };

  // 处理Excel数据
  const processExcelData = (jsonData: any[]) => {
    if (!jsonData || jsonData.length < 2) {
      createMessage.error('Excel文件数据为空或格式不正确');
      return;
    }

    const headers = jsonData[0];
    const rows = jsonData.slice(1);
    const type = excelImportType.value;
    const processedData: any[] = [];

    rows.forEach((row: any[], index: number) => {
      if (row.some((cell) => cell !== null && cell !== undefined && cell !== '')) {
        const item: any = {};

        if (type === 'product') {
          item.productName = row[0] || '';
          item.productFormatName = row[1] || '';
          item.unit = row[2] || '';
          item.negotiatedPrice = row[3] || null;
          item.suggestionPrice = row[4] || null;
        } else if (type === 'task') {
          item.productName = row[0] || '';
          item.productFormatName = row[1] || '';
          // item.unit = row[2] || '';
          item.yearTask = row[2] || null;
          item.firQuarterTask = row[3] || null;
          item.twoQuarterTask = row[4] || null;
          item.thrQuarterTask = row[5] || null;
          item.fouQuarterTask = row[6] || null;
          item.cooperationCharge = row[7] || null;
        } else if (type === 'employee') {
          item.productName = row[0] || '';
          item.productFormatName = row[1] || '';
          // item.unit = row[2] || '';
          item.incentivePolicy = row[2] || '';
          item.suggestionMark = row[3] || '';
        }

        processedData.push(item);
      }
    });

    parsedData.value = processedData;

    if (processedData.length === 0) {
      createMessage.warning('未解析到有效数据');
    } else {
      createMessage.success(`成功解析到 ${processedData.length} 条数据`);
    }
  };

  // 移除文件
  const handleRemove = () => {
    fileList.value = [];
    parsedData.value = [];
  };

  // 下载模板
  const downloadTemplate = () => {
    const type = excelImportType.value as 'product' | 'task' | 'employee';
    try {
      downloadExcelTemplate(type);
      createMessage.success('模板下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      createMessage.error('下载模板失败');
    }
  };

  // 取消导入
  const cancelExcelImport = () => {
    excelImportVisible.value = false;
    fileList.value = [];
    parsedData.value = [];
  };

  // 确认导入
  const confirmExcelImport = () => {
    if (!parsedData.value.length) {
      createMessage.warning('没有可导入的数据');
      return;
    }

    importLoading.value = true;
    const type = excelImportType.value;
    let addedCount = 0;
    let duplicateCount = 0;

    try {
      parsedData.value.forEach((item) => {
        // 生成临时ID用于去重检查
        // const tempId = `${item.productName}_${item.productFormatName}`;
        let isDuplicate = false;
        let targetArray: any[] = [];

        if (type === 'product') {
          targetArray = formData.contractProducts;
        } else if (type === 'task') {
          targetArray = formData.contractProductTasks;
        } else if (type === 'employee') {
          targetArray = formData.contractProductEmployees;
        }

        // 检查是否存在重复（基于产品名称和规格）
        isDuplicate = targetArray.some(
          (existingItem) =>
            existingItem.productName === item.productName &&
            existingItem.productFormatName === item.productFormatName,
        );

        if (!isDuplicate) {
          // 添加到对应表格
          const newItem = {
            // id: Date.now() + Math.random(),
            productId: null, // 临时ID，实际使用时需要通过产品名称匹配真实ID
            productName: item.productName,
            productFormatId: null,
            productFormatName: item.productFormatName,
            unit: item.unit,
          };

          if (type === 'product') {
            targetArray.push({
              ...newItem,
              negotiatedPrice: item.negotiatedPrice,
              suggestionPrice: item.suggestionPrice,
            });
          } else if (type === 'task') {
            targetArray.push({
              ...newItem,
              yearTask: item.yearTask,
              firQuarterTask: item.firQuarterTask,
              twoQuarterTask: item.twoQuarterTask,
              thrQuarterTask: item.thrQuarterTask,
              fouQuarterTask: item.fouQuarterTask,
              cooperationCharge: item.cooperationCharge,
            });
          } else if (type === 'employee') {
            targetArray.push({
              ...newItem,
              incentivePolicy: item.incentivePolicy,
              suggestionMark: item.suggestionMark,
            });
          }
          addedCount++;
        } else {
          duplicateCount++;
        }
      });

      // 显示导入结果
      if (addedCount > 0 && duplicateCount > 0) {
        createMessage.success(
          `成功导入 ${addedCount} 条数据，${duplicateCount} 条数据已存在未重复导入`,
        );
      } else if (addedCount > 0) {
        createMessage.success(`成功导入 ${addedCount} 条数据`);
      } else if (duplicateCount > 0) {
        createMessage.warning(`所选 ${duplicateCount} 条数据已存在，未重复导入`);
      }

      excelImportVisible.value = false;
      fileList.value = [];
      parsedData.value = [];
    } catch (error) {
      console.error('导入数据失败:', error);
      createMessage.error('导入数据失败');
    } finally {
      importLoading.value = false;
    }
  };

  // 表格操作方法
  const addProduct = () => {
    openProductSelectModal('product', '选择合作产品');
  };

  const removeProduct = (index: number) => {
    formData.contractProducts.splice(index, 1);
  };

  const handleProductChange = (value: string, index: number) => {
    // 这里可以根据产品ID获取产品信息
    const product = formData.contractProducts[index];
    product.productId = value;
    // 可以在这里调用API获取产品详情并更新产品名称等信息
  };

  const addTask = () => {
    openProductSelectModal('task', '选择年度任务产品');
  };

  const removeTask = (index: number) => {
    formData.contractProductTasks.splice(index, 1);
  };

  const addEmployee = () => {
    openProductSelectModal('employee', '选择店员推荐产品');
  };

  const removeEmployee = (index: number) => {
    formData.contractProductEmployees.splice(index, 1);
  };

  // 签署节点操作方法
  const editSignNode = (index: number) => {
    // 保存原始数据用于取消编辑
    const record = formData.signNodes[index];
    record.originalData = { ...record };
    record.editing = true;
  };

  const saveSignNode = (index: number) => {
    const record = formData.signNodes[index];

    // 验证必填字段
    if (
      !record.type ||
      !record.counterpartyCode ||
      !record.signingMethod ||
      !record.signingType ||
      !record.signatoryName ||
      !record.signatoryPhone
    ) {
      createMessage.error('请填写完整的签署节点信息');
      return;
    }

    // 保存成功，删除原始数据和编辑状态
    delete record.originalData;
    record.editing = false;
    createMessage.success('签署节点保存成功');
  };

  // 处理签约方式变化
  const handleSigningMethodChange = (value: string, record: any) => {
    record.signingMethod = value;
    const methodMap: Record<string, string> = {
      ESEAL: '电子签约',
      PHYSICALSEAL: '纸质签约',
    };
    record.signingMethodName = methodMap[value] || value;
  };

  // 处理签章类型变化
  const handleSigningTypeChange = (value: string, record: any) => {
    record.signingType = value;
    const typeMap: Record<string, string> = {
      COMPANY_SEAL: '企业盖章',
      PERSONAL_SIGN: '个人签字',
      LP_SIGN: '法定代表人签字',
    };
    record.signingTypeName = typeMap[value] || value;
  };

  const getStraddleStampText = (stamp: number) => {
    return stamp === 1 ? '是' : '否';
  };

  const onAdd = () => {
    openModal('add', '新增协议');
  };
  // 重新推送
  const reloadT = async () => {
    if (!selectedKeys.value.length) {
      createMessage.warning('请选择要重新推送的协议');
      return;
    }
    console.log(tableData.data, selectedKeys.value);

    // 获取选中的数据
    const selectedData = tableData.data.filter((item: any) => selectedKeys.value.includes(item.id));

    // 检查是否有已推送的数据（pushContractStatus = 2）
    const pushedData = selectedData.filter(
      (item: any) => item.approvalStatus === '3' && item.pushContractStatus === '2',
    );

    if (pushedData.length !== 0) {
      createMessage.error('选择的OA审核通过的合同中有已推送的数据，请勿重复推送');
      return;
    }

    try {
      // 调用重新推送接口，传入已推送数据的ids
      // const pushedIds = pushedData.map((item: any) => item.id);
      await contractPushRetry(selectedKeys.value);

      createMessage.success(`成功重新推送 ${selectedKeys.value.length} 条协议`);
      // 刷新表格数据
      getList(1);
    } catch (error) {
      console.error('重新推送失败:', error);
      // createMessage.error('重新推送失败');
    }
  };
  const reloadR = () => {};

  const onView = (record: any) => {
    openModal('view', '查看协议', record);
  };

  const onEdit = (record: any) => {
    openModal('edit', '编辑协议', record);
  };

  // 导出表格数据
  const exportTableData = (type: string, title: string) => {
    try {
      let data: any[] = [];
      let header: any = {};
      let filename = '';

      if (type === 'product') {
        data = formData.contractProducts.map((item, index) => ({
          序号: index + 1,
          产品名称: item.productName || '',
          规格: item.productFormatName || '',
          单位: item.unit || '',
          协议价: item.negotiatedPrice || '',
          建议零售价: item.suggestionPrice || '',
        }));
        header = {
          序号: '序号',
          产品名称: '产品名称',
          规格: '规格',
          单位: '单位',
          协议价: '协议价（元/盒）',
          建议零售价: '建议零售价（元/盒）',
        };
        filename = `合作产品信息-${dayjs().format('YYYY-MM-DD')}.xlsx`;
      } else if (type === 'task') {
        data = formData.contractProductTasks.map((item, index) => ({
          序号: index + 1,
          产品名称: item.productName || '',
          规格: item.productFormatName || '',
          全年任务量: item.yearTask || '',
          第一季度任务量: item.firQuarterTask || '',
          第二季度任务量: item.twoQuarterTask || '',
          第三季度任务量: item.thrQuarterTask || '',
          第四季度任务量: item.fouQuarterTask || '',
          合作费用标准: item.cooperationCharge || '',
        }));
        header = {
          序号: '序号',
          产品名称: '产品名称',
          规格: '规格',
          全年任务量: '全年任务量',
          第一季度任务量: '第一季度任务量',
          第二季度任务量: '第二季度任务量',
          第三季度任务量: '第三季度任务量',
          第四季度任务量: '第四季度任务量',
          合作费用标准: '合作费用标准（元/盒）',
        };
        filename = `年度任务量分解-${dayjs().format('YYYY-MM-DD')}.xlsx`;
      } else if (type === 'employee') {
        data = formData.contractProductEmployees.map((item, index) => ({
          序号: index + 1,
          产品名称: item.productName || '',
          规格: item.productFormatName || '',
          连锁店员单品激励政策: item.incentivePolicy || '',
          连锁货架推荐符号或标志: item.suggestionMark || '',
        }));
        header = {
          序号: '序号',
          产品名称: '产品名称',
          规格: '规格',
          连锁店员单品激励政策: '连锁店员单品激励政策',
          连锁货架推荐符号或标志: '连锁货架推荐符号或标志',
        };
        filename = `店员推荐体系产品-${dayjs().format('YYYY-MM-DD')}.xlsx`;
      }

      if (data.length === 0) {
        createMessage.warning(`${title}数据为空，无法导出`);
        return;
      }

      jsonToSheetXlsx({
        data,
        header,
        filename,
      });

      createMessage.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      createMessage.error('导出失败');
    }
  };
</script>

<style scoped lang="less">
  #chainPartnership {
    width: 100%;
    height: 100%;
    padding: 8px;
    .item-detail {
      display: flex;
      flex-direction: column;
      .name,
      .status {
        .stress-text {
          &.REJECTED {
            color: #fc474c;
          }
        }
        :first-child {
          margin-right: 18rpx;
        }
      }
      .status {
        margin-top: 6rpx;
      }
    }
    .comPage_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      > div {
        width: 100%;
        padding: 16px;
        &:last-child {
          flex: 1;
          height: 0;
        }
      }
      .detail_title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 0;
        padding: 16px 0 0 16px;
      }
    }
    .filterForm_box {
      > * + * {
        margin-left: 16px;
      }
    }
  }
  .customStriped {
    background-color: #fff;
  }

  // 协议管理弹窗样式
  .contract-modal-content {
    height: calc(100vh - 110px);
    overflow-y: auto;
    padding: 20px;

    .contract-form {
      .contract-collapse {
        .ant-collapse-item {
          // margin-bottom: 16px;
          // border: 1px solid #d9d9d9;
          border-radius: 6px;

          .ant-collapse-header {
            background-color: #fafafa;
            font-weight: 600;
            font-size: 16px;
          }

          .ant-collapse-content {
            .ant-collapse-content-box {
              padding: 20px;
            }
          }
        }
      }

      .ant-form-item {
        margin-bottom: 16px;
      }

      .ant-table {
        margin-bottom: 16px;

        .ant-table-thead > tr > th {
          background-color: #fafafa;
          font-weight: 600;
        }
      }

      .table-action-row {
        padding: 16px 0;
        text-align: left;
        border-top: 1px solid #f0f0f0;
        margin-top: 16px;
      }
    }

    .modal-footer {
      background-color: #fff;
      padding: 16px 0;
      text-align: right;
      border-top: 1px solid #f0f0f0;
      margin-top: 20px;
    }
  }
  :deep .ant-tabs-nav-wrap {
    padding-left: 20px;
  }
</style>

<style lang="less">
  .full-modala {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
      height: 100vh;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    .ant-modal-header {
      flex-shrink: 0;
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-modal-body {
      flex: 1;
      padding: 0;
      overflow: hidden;
    }

    .ant-modal-footer {
      display: none;
    }
  }
</style>
