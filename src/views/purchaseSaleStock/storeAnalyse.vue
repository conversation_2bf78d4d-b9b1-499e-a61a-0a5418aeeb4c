<template>
  <div id="clockingRecord">
    <div class="targetMange_Box">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="连锁总部">
          <department></department>
        </a-tab-pane>
        <a-tab-pane key="2" tab="连锁配送商业">
          <dispatching></dispatching>
        </a-tab-pane>
        <template #rightExtra>
          <a-tag style="height: 30px;line-height: 30px" color="orange">数据更新时间 {{dayjs().format('YYYY-MM-DD 01:00:00')}}</a-tag>
        </template>
      </a-tabs>

    </div>
  </div>

</template>

<script lang="ts" setup>
import { ref } from 'vue';
const activeKey = ref('1');
import dispatching from "./compoents/dispatchingAnalyse.vue"
import department from "./compoents/departmentAnalyse.vue"
import dayjs from 'dayjs';
</script>

<style scoped lang="less">
#clockingRecord {
  width: 100%;
  height: 100%;
  padding: 8px;
  .targetMange_Box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    > div {
      width: 100%;
      padding: 16px;
      &:last-child {
        flex: 1;
        height: 0;
      }
    }
    .p_box {
      padding: 0 16px;
    }
    .type_top {
      width: 100%;
      padding: 16px 0 0 16px;
      .btn_group {
        display: inline-flex;
        > div {
          padding: 6px 40px;
          border: 1px solid #d9d9d9;
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
          cursor: pointer;
          &:first-child {
            border-right: none;
            border-radius: 3px 0 0 3px;
          }
          &:last-child {
            border-left: none;
            border-radius: 0 3px 3px 0;
          }
        }
        .is_active {
          color: #fff;
        }
      }
    }
  }

}

</style>
