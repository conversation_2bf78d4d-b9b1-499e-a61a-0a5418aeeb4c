<template>
  <div id="aTable">
    <a-table
      :dataSource="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :scroll="scroll"
      :rowKey="rowKey"
      :row-selection="isSelection ? props.rowSelection ?? defaultRowSelection : null"
      @change="handleTableChange"
      :bordered="props.border"
    >
      <template #bodyCell="{ column, record, index }">
        <!-- isHasIndex 序号列 -->
        <template v-if="column?.isHasIndex">{{ index + 1 }} </template>
        <!-- isSlot 自定义插槽 name为key -->
        <template v-else-if="column?.isSlot">
          <slot :name="column.slotName ?? column.key" v-bind="{ column, record }"></slot>
        </template>
        <!-- 如果有 customRender，让 Ant Design Vue 自己处理 -->
        <template v-else-if="!column?.customRender">
          {{ record[column?.key] }}
        </template>
        <template v-else>
          {{ record[column?.key] }}
        </template>
      </template>
      <template v-if="isSummary" #summary>
        <a-table-summary-row>
          <a-table-summary-cell
            v-for="(column, index) in tableColumns"
            :key="column.dataIndex"
            :index="index"
            :align="column.align"
          >
            <slot v-if="column.summary" name="summarySlot" v-bind="{ column, index }"></slot>
            <span v-else>{{ column.summary }}</span>
          </a-table-summary-cell>
        </a-table-summary-row>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { Props } from './types';

  const emit = defineEmits([
    'update:currentPage',
    'paginationChange',
    'onSelectChange',
    'onSelectAll',
    'onSelect',
  ]);
  const props = withDefaults(defineProps<Props>(), {
    tableColumns: () => [],
    tableData: () => [],
    loading: false,
    currentPage: 1,
    pageSize: 10,
    showSizeChanger: true,
    pageSizeOptions: () => [10, 20, 50, 100],
    totalItems: 0,
    showQuickJumper: true,
    scroll: () => ({
      x: '100%',
      y: '900px',
      scrollToFirstRowOnChange: true,
    }),
    isSelection: false,
    rowKey: 'id',
    checkedKeys: () => [],
    isSummary: false,
    showTotal: true,
    border: false
  });
  const defaultRowSelection = computed(() => ({
    type: 'checkbox',
    selectedRowKeys: props.checkedKeys,
    onChange: (selectedRowKeys: (string | number)[], selectedRows: any[]) => {
      emit('onSelectChange', selectedRowKeys, selectedRows);
    },
  }));
  // 列表分页
  const pagination = computed(() => ({
    total: props.totalItems,
    current: props.currentPage,
    pageSize: props.pageSize,
    defaultPageSize: props.pageSize,
    pageSizeOptions: props.pageSizeOptions,
    showSizeChanger: props.showSizeChanger,
    showQuickJumper: props.showQuickJumper,
    showTotal: (total) => (props.showTotal ? `共 ${total} 条` : null),
  }));
  const handleTableChange = (info: Object) => {
    emit('paginationChange', info);
  };
</script>

<style scoped lang="less">
  #aTable {
    height: 100%;
    .ant-table-wrapper {
      height: 100%;
    }
  }
</style>
