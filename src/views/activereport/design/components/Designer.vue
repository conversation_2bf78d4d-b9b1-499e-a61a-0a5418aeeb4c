<template>
  <div id="designer-host" class="design-box">
<!--    <a-button class="button-item" @click.stop="back" type="primary">返回</a-button>-->
<!--    <Designer-->
<!--      ref="designRef"-->
<!--      :language="'zh'"-->
<!--      :onSave="onSave"-->
<!--      :onSaveAs="onSaveAs"-->
<!--      :onOpen="onOpen"-->
<!--      :onCreate="onCreate"-->
<!--      :onRender="onRender"-->
<!--      :onOpenFileMenu="onOpenFileMenu"-->
<!--    />-->
<!--    <Viewer v-if="show" :content="viewContent" />-->
<!--    <a-modal-->
<!--      :visible="open"-->
<!--      title="修改文件名"-->
<!--      :maskClosable="false"-->
<!--      :width="400"-->
<!--      okText="确定"-->
<!--      cancelText="取消"-->
<!--      @ok="save"-->
<!--      @cancel="closeSaveModel"-->
<!--    >-->
<!--      <div class="model-box">-->
<!--        <a-form-->
<!--          :model="formState"-->
<!--          name="basic"-->
<!--          :label-col="{ span: 4 }"-->
<!--          :wrapper-col="{ span: 20 }"-->
<!--          autocomplete="off"-->
<!--          @finish="save"-->
<!--        >-->
<!--          <a-form-item-->
<!--            label="文件名"-->
<!--            name="name"-->
<!--            :rules="[{ required: true, message: '请输入文件名' }]"-->
<!--          >-->
<!--            <a-input v-model:value="formState.name" />-->
<!--          </a-form-item>-->
<!--        </a-form>-->
<!--      </div>-->
<!--    </a-modal>-->
  </div>
</template>
<script language="ts" setup>
</script>
<!--<style scoped>-->
<!--  #viewer-host {-->
<!--    width: 100%;-->
<!--    min-height: 100vh;-->
<!--  }-->

<!--  .design-box {-->
<!--    position: absolute;-->
<!--    top: 0;-->
<!--    left: 0;-->
<!--    right: 0;-->
<!--  }-->

<!--  .button-item {-->
<!--    position: absolute;-->
<!--    right: 60px;-->
<!--    top: 40px;-->
<!--    z-index: 99;-->
<!--  }-->

<!--  .model-box {-->
<!--    padding: 20px;-->
<!--  }-->

<!--  :deep(.model-box) {-->
<!--    padding: 20px;-->
<!--  }-->
<!--</style>-->
