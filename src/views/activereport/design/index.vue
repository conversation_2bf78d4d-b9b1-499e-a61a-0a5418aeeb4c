<template>
  <PageWrapper dense fixed-height contentFullHeight>
    <BasicTable @register="registerTable" v-if="!showDesigner" isMenuTable>
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'report-design:add'">
          {{ t('新增报表') }}
        </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              auth: 'report-design:edit',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              auth: 'report-design:delete',
              color: 'error',
              popConfirm: {
                title: t('是否确认删除'),
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <Designer
      v-if="showDesigner"
      :id="designData.id"
      :file-json-str="designData.fileJsonStr"
      @close="handleClose"
    />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';
  import { getProfessionalPageList, deleteProfessionalReport } from '/@/api/system/report';
  import Designer from './components/Designer.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { PageWrapper } from '/@/components/Page';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';

  const { t } = useI18n();
  const columns: BasicColumn[] = [
    {
      title: t('文件名'),
      dataIndex: 'name',
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      align: 'left',
      resizable: true,
      width: 300,
    },
    {
      title: t('修改时间'),
      dataIndex: 'modifyDate',
      align: 'left',
      resizable: true,
      width: 300,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入关键字'),
      },
    },
  ];
  const designData = reactive({
    fileJsonStr: '',
    id: '',
  });
  const { notification } = useMessage();
  const { hasPermission } = usePermission();

  const [registerTable, { reload }] = useTable({
    title: t('报表列表'),
    api: getProfessionalPageList,
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },

    useSearchForm: true,
    showTableSetting: true,
    striped: false,

    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('report-design:edit')) {
            handleEdit(record);
          }
        },
      };
    },
  });

  const showDesigner = ref<boolean>(false);

  function handleCreate() {
    designData.id = '';
    designData.fileJsonStr = '';
    showDesigner.value = true;
  }

  function handleEdit(record: Recordable) {
    designData.id = record.id;
    designData.fileJsonStr = record.fileJson;
    showDesigner.value = true;
  }
  function handleClose() {
    showDesigner.value = false;
  }
  function handleDelete(record: Recordable) {
    deleteProfessionalReport(record.id).then((_) => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功！'),
      });
    });
  }
</script>
