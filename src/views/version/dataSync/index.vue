<template>
  <ScrollContainer>
    <PageWrapper dense contentFullHeight fixedHeight>
      <div ref="wrapperRef" class="account-setting">
        <div class="left-box">
          <div class="left-label">同步类型</div>
        </div>
        <Tabs tab-position="left" :tabBarStyle="tabBarStyle">
          <template v-for="item in settingList" :key="item.key">
            <TabPane :tab="item.name" class="h-full">
              <component :is="item.component" :image="item.image" />
            </TabPane>
          </template>
        </Tabs>
      </div>
    </PageWrapper>
  </ScrollContainer>
</template>
<script lang="ts" setup>
  import { Tabs, TabPane } from 'ant-design-vue';
  import { ScrollContainer } from '/@/components/Container/index';
  import { PageWrapper } from '/@/components/Page';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { defineAsyncComponent } from 'vue';
  import flowSyncPng from '/@/assets/sync/flowSync.png';
  import formSyncPng from '/@/assets/sync/formSync.png';
  import apiSyncPng from '/@/assets/sync/apiSync.png';

  const partSync = defineAsyncComponent(() => import('./components/partSync.vue'));
  const allSync = defineAsyncComponent(() => import('./components/allSync.vue'));
  const SyncCommon = defineAsyncComponent(() => import('./components/SyncCommon.vue'));

  const { t } = useI18n();
  const tabBarStyle = {
    width: '220px',
  };
  const settingList = [
    {
      key: '1',
      name: t('基础数据增量同步'),
      component: partSync,
    },
    {
      key: '2',
      name: t('基础数据全量同步'),
      component: allSync,
    },
    {
      key: '3',
      name: t('流程同步'),
      component: SyncCommon,
      image: flowSyncPng,
    },
    {
      key: '4',
      name: t('表单同步'),
      component: SyncCommon,
      image: formSyncPng,
    },
    {
      key: '5',
      name: t('接口同步'),
      component: SyncCommon,
      image: apiSyncPng,
    },
  ];
</script>
<style lang="less" scoped>
  .account-setting {
    height: 100%;
    background-color: @component-background;
    margin-right: 7px;

    .left-box {
      height: 50px;
      border: 1px solid #f0f0f0;
      width: 220px;
      line-height: 50px;
      margin-bottom: 10px;

      .left-label {
        margin-left: 20px;
      }
    }

    .base-title {
      padding-left: 0;
    }

    .ant-tabs-tab-active {
      background-color: @item-active-bg;
    }

    :deep(.ant-tabs-content-holder),
    :deep(.ant-tabs-content) {
      height: 100%;
    }
  }

  :deep(.ant-tabs) {
    height: calc(100% - 60px);
  }

  :deep(.ant-tabs-content) {
    overflow: auto;
  }
</style>
