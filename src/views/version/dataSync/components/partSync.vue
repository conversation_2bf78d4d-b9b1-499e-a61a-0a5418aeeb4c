<template>
  <Spin :spinning="isLoading" tip="加载中...">
    <div class="flex flex-col items-center justify-center py-8 bg-white">
      <div class="form-box">
        <a-form
          :model="formState"
          name="basic"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 16 }"
          autocomplete="off"
          ref="FormRef"
        >
          <a-form-item label="功能菜单" :required="menuIds">
            <MenuSelect
              v-model:value="formState.menuIds"
              allmenu="true"
              multiple="true"
              placeholder="允许选择多个菜单项"
              @change="
                (val, node) => {
                  formState.menuIds = val;
                  formState.systemId = node.systemId;
                }
              "
            />
          </a-form-item>
          <a-form-item label="数据字典" :required="dics">
            <DicTreeSelect
              v-model:value="formState.dicIds"
              mode="multiple"
              placeholder="允许多选"
            />
          </a-form-item>
          <a-form-item label="数据连接" :required="menulink">
            <DbSelect
              v-model:value="formState.dataLinkIds"
              mode="multiple"
              placeholder="允许多选"
            />
          </a-form-item>
          <a-form-item label="单据编码" :required="codeRule">
            <Select
              v-model:value="formState.codeRuleIds"
              mode="multiple"
              :options="datacode"
              :fieldNames="{ label: 'name', value: 'id' }"
              placeholder="允许多选"
            />
          </a-form-item>
          <a-form-item label="迁移提示" :required="menuDic">
            <div class="remark">
              <Alert
                message="迁移提示"
                description="此功能用于增量基础数据迁移，如果需要迁移所有基础数据请使用【全量迁移】功能。在线接口、表单、流程请移步至相关功能页面进行导入导出操作"
                type="info"
                showIcon
              />
            </div>
          </a-form-item>
          <a-form-item :wrapper-col="{ offset: 4, span: 20 }">
            <a-button class="!ml-4" type="primary" @click="handleSubmit">
              {{ t('导出数据') }}
            </a-button>
            <ImportData>
              <a-button class="!ml-4" type="primary">
                {{ t('导入数据') }}
              </a-button>
            </ImportData>
          </a-form-item>
        </a-form>
      </div>
      <div class="flex justify-center"></div>
    </div>
  </Spin>
</template>
<script lang="ts" setup>
  import { onMounted, reactive, ref } from 'vue';
  import { Alert, Select, Spin } from 'ant-design-vue';
  import { MenuSelect } from '/@/components/MenuSelect';
  import { DicTreeSelect } from '/@/components/DicTreeSelect';
  import { DbSelect } from '/@/components/DbSelect';
  import { downloadByData } from '/@/utils/file/download';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { exportData } from '/@/api/version/dataSync';
  import { getCodeRule } from '/@/api/system/code';
  import ImportData from './ImportData.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { notification } = useMessage();
  const FormRef = ref();
  const { t } = useI18n();
  const isLoading = ref(true);
  interface FormState {
    menuIds: Arry<String>;
    dicIds: Arry<String>;
    dataLinkIds: Arry<String>;
    codeRuleIds: Arry<String>;
  }

  const formState = reactive<FormState>({
    menuIds: [],
    dicIds: [],
    dataLinkIds: [],
    codeRuleIds: [],
  });
  const datacode = ref<Recordable[]>([]);
  onMounted(async () => {
    datacode.value = await getCodeRule();
    isLoading.value = false; //初始化加载等待效果
  });

  async function handleSubmit() {
    try {
      if (formState.dicIds.length > 0) {
        formState.dicIds = formState.dicIds.split(',');
      } else {
        formState.dicIds = [];
      }
      console.log(formState);
      let res: any = await exportData(formState);
      downloadByData(res, '系统基础数据.json');

      notification.success({
        message: t('提示'),
        description: t('导出成功'),
      }); //提示消息
    } catch (error) {
      notification.success({
        message: t('提示'),
        description: t('导出失败'),
      });
    }
  }
</script>
<style lang="less" scoped>
  .form-box {
    width: 100%;
    padding: 0 20px;
  }
</style>
