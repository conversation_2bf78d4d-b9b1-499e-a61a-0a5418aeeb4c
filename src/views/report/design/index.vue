<template>
  <PageWrapper dense fixed-height contentFullHeight>
    <BasicTable @register="registerTable" v-if="!showFrame">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'report-design:add'">
          {{ t('新增报表') }}
        </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              auth: 'report-design:edit',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              auth: 'report-design:delete',
              color: 'error',
              popConfirm: {
                title: t('是否确认删除'),
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <FramePage v-if="showFrame" :frameSrc="frameSrc" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';
  import { getReportPageList, deleteReport } from '/@/api/system/report';
  import FramePage from '/@/views/sys/iframe/index.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getAppEnvConfig } from '/@/utils/env';
  import { PageWrapper } from '/@/components/Page';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';

  const { t } = useI18n();
  const columns: BasicColumn[] = [
    {
      title: t('文件名'),
      dataIndex: 'name',
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      align: 'left',
      resizable: true,
      width: 300,
    },
    {
      title: t('修改时间'),
      dataIndex: 'modifyDate',
      align: 'left',
      resizable: true,
      width: 300,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入关键字'),
      },
    },
  ];

  const { notification } = useMessage();
  const { hasPermission } = usePermission();

  const [registerTable, { reload }] = useTable({
    title: t('报表列表'),
    api: getReportPageList,
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },

    useSearchForm: true,
    showTableSetting: true,
    striped: false,

    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('report-design:edit')) {
            handleEdit(record);
          }
        },
      };
    },
  });

  let frameSrc = ref<string>(`${getAppEnvConfig().VITE_GLOB_API_URL}/ureport/designer`);
  const showFrame = ref<boolean>(false);

  function handleCreate() {
    showFrame.value = true;
  }

  function handleEdit(record: Recordable) {
    frameSrc.value = `${getAppEnvConfig().VITE_GLOB_API_URL}/ureport/designer?_u=xjr:${
      record.name
    }`;
    showFrame.value = true;
  }

  function handleDelete(record: Recordable) {
    deleteReport([record.id]).then((_) => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功！'),
      });
    });
  }
</script>
