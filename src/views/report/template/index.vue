<template>
  <FramePage :frameSrc="frameSrc" />
</template>
<script lang="ts" setup>
  import { unref } from 'vue';
  import { useRouter } from 'vue-router';
  import { getAppEnvConfig } from '/@/utils/env';
  import FramePage from '/@/views/sys/iframe/index.vue';

  const { currentRoute } = useRouter();
  const { path } = unref(currentRoute);
  const paths = path.split('/');
  const reportName = paths.length > 0 ? paths[paths.length - 1] : '';
  const frameSrc = `${
    getAppEnvConfig().VITE_GLOB_API_URL
  }/ureport/preview?_u=xjr:${reportName}.ureport.xml`;
</script>
