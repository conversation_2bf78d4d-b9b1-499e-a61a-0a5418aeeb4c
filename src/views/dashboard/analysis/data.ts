import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
export interface GrowCardItem {
  icon: string;
  title: string;
  value: number;
  total: number;
  color: string;
  action: string;
}

export const growCardList: GrowCardItem[] = [
  {
    title: t('访问数'),
    icon: 'visit-count|svg',
    value: 2000,
    total: 120000,
    color: 'green',
    action: t('月'),
  },
  {
    title: t('成交额'),
    icon: 'total-sales|svg',
    value: 20000,
    total: 500000,
    color: 'blue',
    action: t('月'),
  },
  {
    title: t('下载数'),
    icon: 'download-count|svg',
    value: 8000,
    total: 120000,
    color: 'orange',
    action: t('周'),
  },
  {
    title: t('成交数'),
    icon: 'transaction|svg',
    value: 5000,
    total: 50000,
    color: 'purple',
    action: t('年'),
  },
];
