<template>
  <!-- <div v-if="desktop.hasCustomDesktop">
    <a-spin :spinning="spinning" :delay="delayTime">
      <DesktopHome v-if="desktop.hasCustomDesktop" :list="desktop.list" :isMenu="true" />
    </a-spin>
  </div>
  <div class="p-4" v-else>
    <GrowCard :loading="loading" class="enter-y" />
    <SiteAnalysis class="!my-4 enter-y" :loading="loading" />
    <div class="md:flex enter-y">
      <VisitRadar class="md:w-1/3 w-full" :loading="loading" />
      <VisitSource class="md:w-1/3 !md:mx-4 !md:my-0 !my-4 w-full" :loading="loading" />
      <SalesProductPie class="md:w-1/3 w-full" :loading="loading" />
    </div>
  </div> -->
  <div class="main_Box">
    <img :src="main_img" alt="" />
  </div>
</template>
<script lang="ts" setup>
  import { reactive, onActivated, ref, watch } from 'vue';
  // import GrowCard from './components/GrowCard.vue';
  // import SiteAnalysis from './components/SiteAnalysis.vue';
  // import VisitSource from './components/VisitSource.vue';
  // import VisitRadar from './components/VisitRadar.vue';
  // import SalesProductPie from './components/SalesProductPie.vue';
  // import DesktopHome from '/@/views/generator/desktop/components/DesktopHome.vue';
  import { useUserStore } from '/@/store/modules/user';
  import main_img from '/@/assets/images/main.jpg';
  // import { useTabs } from '/@/hooks/web/useTabs';
  // import { useTitle } from '@vueuse/core';

  const loading = ref(true);
  const desktop = reactive({ hasCustomDesktop: false, list: [] });

  const spinning = ref<boolean>(true);
  // const delayTime = 500;
  setTimeout(() => {
    loading.value = false;
    spinning.value = false;
  }, 1000);

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  // const { setTitle } = useTabs();

  watch(
    () => userInfo.desktopSchema,
    (v) => {
      getList(v);
    },
    {
      deep: true,
    },
  );
  onActivated(() => {
    desktop.hasCustomDesktop = false;
    setTimeout(() => {
      if (userInfo.desktopSchema?.jsonContent) {
        desktop.list = JSON.parse(userInfo.desktopSchema.jsonContent);
        desktop.hasCustomDesktop = true;
      }
    }, 1);
  });
  getList(userInfo.desktopSchema);
  function getList(desktopSchema) {
    if (desktopSchema?.jsonContent) {
      desktop.list = JSON.parse(desktopSchema.jsonContent);
      //设置Tab标题
      // setTitle(desktopSchema.name);
      // console.log('setTitle: ', desktopSchema.name);
      //设置浏览器标题
      // useTitle(` ${desktopSchema.name} `);
      desktop.hasCustomDesktop = true;
    }
  }
</script>
<style>
  .main_Box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    background-color: #fff;
    img {
      width: 400px;
      height: auto;
    }
  }
</style>
