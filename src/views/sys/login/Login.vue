<template>
  <div :class="prefixCls" class="login-box relative w-full h-full">
    <div class="login-left-box">
      <div class="logo-box">
        <!-- v-if="show" -->
        <!-- <a href="http://www.zilueit.com/" target="_blank"> -->
        <!-- <img :src="logoConfig.loginLogoUrl || logopng" /> -->
        <img :src="logopng" />
        <!-- </a> -->
      </div>
      <!-- <img :src="logoConfig.backgroundLogoUrl || logobg" class="h-full" /> -->
    </div>
    <!-- <div class="fixed-tool">
      <AppDarkModeToggle v-if="!sessionTimeout" />
    </div> -->
    <div class="right-box">
      <div :class="`${prefixCls}-form`">
        <div class="login-left-title">
          <div class="title">{{ t('欢迎登录') }}</div>
          <div class="sub-title">{{ t('OTC管理平台') }}</div>
        </div>
        <LoginForm />
      </div>
    </div>
    <!-- <div class="right-top-box">
      <img src="../../../assets/images/login-right.gif" />
    </div> -->
  </div>
</template>
<script lang="ts" setup>
  // import { computed } from 'vue';
  // import { AppDarkModeToggle } from '/@/components/Application';
  import LoginForm from './LoginForm.vue';
  // import { useGlobSetting } from '/@/hooks/setting';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';
  // import { useLocaleStore } from '/@/store/modules/locale';
  import { useAppStore } from '/@/store/modules/app';
  import logopng from '/@/assets/images/logo-dark.png';
  // import logobg from '/@/assets/images/login-left.png';
  import { getLogoInfo } from '/@/api/system/login';
  import { ref } from 'vue';
  import { LogoConfig } from '/#/config';

  const appStore = useAppStore();
  defineProps({
    sessionTimeout: {
      type: Boolean,
    },
  });

  // const globSetting = useGlobSetting();
  const { prefixCls } = useDesign('login');
  const { t } = useI18n();
  // const localeStore = useLocaleStore();
  // const showLocale = localeStore.getShowPicker;
  // const title = computed(() => globSetting?.title ?? '');

  let logoConfig = ref<LogoConfig>({});
  // let show = ref(false);
  getLogoInfo().then((res) => {
    // show.value = true;
    logoConfig.value = {
      companyName: res.companyName,
      shortName: res.shortName,
      refreshLogoUrl: res.refreshLogoUrl,
      backgroundLogoUrl: res.backgroundLogoUrl,
      designerLogoUrl: res.designerLogoUrl,
      loginLogoUrl: res.loginLogoUrl,
      menuLogoUrl: res.menuLogoUrl,
    };
    appStore.setLogoConfig(logoConfig.value);
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-login';
  @logo-prefix-cls: ~'@{namespace}-app-logo';
  @countdown-prefix-cls: ~'@{namespace}-countdown-input';
  @dark-bg: linear-gradient(to bottom, #364876, #112049);

  .vben-login-form {
    padding: 70px 60px 70px;
  }

  .login-box {
    display: flex;
    background-image: url(/resource/img/login-left.jpg);
    background-size: cover;
    background-repeat: no-repeat;
  }
  .logo-box {
    position: fixed;
    top: 50px;
    left: 40px;
  }

  .logo-box img {
    width: 180px;
  }

  .login-left-title {
    // position: relative;
    // top: 100px;
    // left: 60px;
    margin-bottom: 40px;
  }

  .login-left-title .title {
    font-size: 20px;
    border: none;
    margin-bottom: 10px;
  }

  .login-left-title .sub-title {
    font-size: 36px;
    font-weight: bold;
    // color: #5e95ff;
    color: #ff9201;
  }

  .login-box .right-box {
    position: absolute;
    top: 10%;
    right: 200px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(0px);
    border-radius: 20px;
  }

  .fixed-tool {
    position: fixed;
    right: 20px;
    top: 40px;
    display: flex;
    z-index: 2;
  }

  .right-top-box {
    width: 400px;
    position: fixed;
    right: 0;
    top: 0;
  }

  html[data-theme='dark'] {
    .login-box {
      background: @dark-bg;
    }

    .login-left-title {
      border: none;
    }

    .login-left-title .title {
      color: #fff;
    }

    .@{prefix-cls} {
      background-color: @dark-bg;

      // &::before {
      //   background-image: url(/@/assets/svg/login-bg-dark.svg);
      // }

      .ant-input,
      .ant-input-password {
        background-color: #232a3b;
      }

      .ant-input-affix-wrapper {
        border-color: #525e7c;
        background-color: #232a3b;
      }

      .ant-checkbox-inner {
        border-color: #525e7c;
      }

      .ant-btn:not(.ant-btn-link, .ant-btn-primary) {
        border: 1px solid #4a5569;
      }

      &-form {
        background: transparent !important;
      }

      .app-iconify {
        color: #fff;
      }
    }

    input.fix-auto-fill,
    .fix-auto-fill input {
      -webkit-text-fill-color: #c9d1d9 !important;
      box-shadow: inherit !important;
    }
  }

  .@{prefix-cls} {
    min-height: 100%;
    overflow: hidden;
    @media (max-width: @screen-xl) {
      background-color: @dark-bg;

      .@{prefix-cls}-form {
        background-color: #fff;
      }
    }

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      margin-left: -48%;
      // background-image: url(/@/assets/svg/login-bg.svg);
      background-position: 100%;
      background-repeat: no-repeat;
      background-size: auto 100%;
      content: '';
      @media (max-width: @screen-xl) {
        display: none;
      }
    }

    .@{logo-prefix-cls} {
      position: absolute;
      top: 12px;
      height: 30px;

      &__title {
        font-size: 16px;
        color: #fff;
      }

      img {
        width: 32px;
      }
    }

    .container {
      .@{logo-prefix-cls} {
        display: flex;
        width: 60%;
        height: 80px;

        &__title {
          font-size: 24px;
          color: #fff;
        }

        img {
          width: 48px;
        }
      }
    }

    &-sign-in-way {
      .anticon {
        font-size: 22px;
        color: #888;
        cursor: pointer;

        &:hover {
          color: @primary-color;
        }
      }
    }

    input:not([type='checkbox']) {
      min-width: 360px;

      @media (max-width: @screen-xl) {
        min-width: 320px;
      }

      @media (max-width: @screen-lg) {
        min-width: 260px;
      }

      @media (max-width: @screen-md) {
        min-width: 240px;
      }

      @media (max-width: @screen-sm) {
        min-width: 160px;
      }
    }

    .@{countdown-prefix-cls} input {
      min-width: unset;
    }

    .ant-divider-inner-text {
      font-size: 12px;
      color: @text-color-secondary;
    }
  }
</style>
