<template>
  <div ref="chartRef" id="averageChart" :style="{ width, height }"></div>
</template>
<script setup lang="ts">
  import { watch, ref, Ref } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';

  const props = defineProps({
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '100%',
    },
    data: {
      type: Array as PropType<any>,
      default: () => [],
    },
  });
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  watch(
    () => props.data,
    (newVal) => {
      if (newVal) {
        setOptions({
          title: {
            text: '绩效平均分',
          },
          radar: [
            {
              indicator: newVal.indicator,
              radius: 66, // 缩放
              // center: ['50%', '50%'], // 位置
            },
          ],
          legend: {
            show: true,
            orient: 'horizontal',
            right: '1%',
            bottom: '3%',
            icon: 'circle',
          },
          tooltip: {
            trigger: 'item',
          },
          series: [
            {
              data: newVal.data,
              type: 'radar',
              tooltip: {
                trigger: 'item',
              },
              itemStyle: {
                // 修改 color 函数返回类型为 ZRColor 支持的类型
                color: (params: any): string => {
                  const colors = ['#f39423', '#f9c78b'];
                  return colors[params.dataIndex % colors.length];
                },
              },
            },
          ] as any,
        });
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style scoped lang="less"></style>
