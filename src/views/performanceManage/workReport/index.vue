<template>
  <div id="headOffice">
    <div class="comPage_Box">
      <div>
        <a-tabs v-model:activeKey="activeKey" @change="onActiveChange()">
          <a-tab-pane key="1" tab="工作日报">
            <div class="filterForm_box">
              <a-cascader
                style="width: 300px"
                v-model:value="searchForm.departList"
                :field-names="{ label: 'name', value: 'id', children: 'children' }"
                :options="cascaderOptions"
                change-on-select
                placeholder="请选择组织架构"
                @change="getList()"
              />
              <a-input
                style="width: 240px"
                v-model:value.lazy="searchForm.leaderUserName"
                placeholder="提交人"
                allowClear
                @change="getList()"
                @press-enter="getList()"
              />
              <a-range-picker
                v-model:value="searchForm.time"
                :placeholder="['提交时间', '结束时间']"
                :allow-clear="false"
                @change="getList()"
              />
              <a-button type="primary" @click="getList()">搜索</a-button>
              <a-button @click="reSet()">重置</a-button>
            </div>
            <div class="table_box">
              <c-table
                :tableColumns="tableColumns"
                :tableData="tableData.data"
                :loading="loading"
                :currentPage="pagination.currentPage"
                :totalItems="pagination.totalItems"
                :pageSize="pagination.pageSize"
                @update:current-page="(value) => (pagination.currentPage = value)"
                @pagination-change="handlePaginationChange"
                rowKey="id"
              >
                <template #week="{ record }"> {{ record.startDay }}-{{ record.endDay }} </template>
                <template #action="{ record }">
                  <a-button type="link" @click.stop="onAllot(record.id)">查看详情</a-button>
                </template>
              </c-table>
            </div>
          </a-tab-pane>
          <a-tab-pane key="3" tab="工作周报" @change="getList()">
            <div class="filterForm_box">
              <a-cascader
                style="width: 300px"
                v-model:value="searchForm.departList"
                :field-names="{ label: 'name', value: 'id', children: 'children' }"
                :options="cascaderOptions"
                change-on-select
                placeholder="请选择组织架构"
                @change="getList()"
              />
              <a-input
                style="width: 240px"
                v-model:value.lazy="searchForm.leaderUserName"
                placeholder="提交人"
                allowClear
                @change="getList()"
                @press-enter="getList()"
              />
              <a-date-picker
                v-model:value="searchForm.time"
                :placeholder="['提交时间', '结束时间']"
                :allow-clear="false"
                :format="customWeekStartEndFormat"
                @change="getList()"
              />
              <a-button type="primary" @click="getList()">搜索</a-button>
              <a-button @click="reSet()">重置</a-button>
            </div>
            <div class="table_box">
              <c-table
                :tableColumns="tableColumns"
                :tableData="tableData.data"
                :loading="loading"
                :currentPage="pagination.currentPage"
                :totalItems="pagination.totalItems"
                :pageSize="pagination.pageSize"
                @update:current-page="(value) => (pagination.currentPage = value)"
                @pagination-change="handlePaginationChange"
                rowKey="id"
              >
                <template #week="{ record }"> {{ record.startDay }}-{{ record.endDay }} </template>
                <template #action="{ record }">
                  <a-button type="link" @click.stop="onAllot(record.id)">查看详情</a-button>
                </template>
              </c-table>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="工作月报" @change="getList()">
            <div class="filterForm_box">
              <a-cascader
                style="width: 300px"
                v-model:value="searchForm.departList"
                :field-names="{ label: 'name', value: 'id', children: 'children' }"
                :options="cascaderOptions"
                change-on-select
                placeholder="请选择组织架构"
                @change="getList()"
              />
              <a-input
                style="width: 240px"
                v-model:value.lazy="searchForm.leaderUserName"
                placeholder="提交人"
                allowClear
                @change="getList()"
                @press-enter="getList()"
              />
              <a-range-picker
                v-model:value="searchForm.time"
                :placeholder="['提交时间', '结束时间']"
                :allow-clear="false"
                picker="month"
                @change="getList()"
              />
              <a-button type="primary" @click="getList()">搜索</a-button>
              <a-button @click="reSet()">重置</a-button>
            </div>
            <div class="table_box">
              <c-table
                :tableColumns="tableColumns"
                :tableData="tableData.data"
                :loading="loading"
                :currentPage="pagination.currentPage"
                :totalItems="pagination.totalItems"
                :pageSize="pagination.pageSize"
                @update:current-page="(value) => (pagination.currentPage = value)"
                @pagination-change="handlePaginationChange"
                rowKey="id"
              >
                <template #week="{ record }"> {{ record.startDay }}-{{ record.endDay }} </template>
                <template #action="{ record }">
                  <a-button type="link" @click.stop="onAllot(record.id)">查看详情</a-button>
                </template>
              </c-table>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
  <a-modal
    :width="550"
    v-model:visible="openModalVisible"
    :title="'详情'"
    :confirm-loading="modalLoading"
    :maskClosable="false"
    destroyOnClose
    :footer="null"
    centered
    @cancel="() => (openModalVisible = false)"
  >
    <div class="modal_box">
      <div
        style="
          display: flex;
          justify-content: space-between;
          padding-right: 10px;
          padding-top: 10px;
          align-items: center;
        "
      >
        <h1>报告周期:</h1>
        <span>{{ frequenc == '1' ? reportDate : `${reportDateStart} - ${reportDateEnd}` }}</span>
      </div>

      <div class="table-header">
        <div class="header-cell work-cell">重点工作</div>
        <div class="header-cell target-cell">周期目标</div>
        <div class="header-cell actual-cell">实际达成</div>
        <div class="header-cell rate-cell">达成率</div>
      </div>

      <div class="table-body">
        <div class="table-row" v-for="(e, i) in detailList" :key="i">
          <div class="data-cell work-cell">{{ e.importantWork }}</div>
          <div class="data-cell target-cell">{{ e.cycleTarget }}</div>
          <div class="data-cell actual-cell">{{ e.actualAchievement }}</div>
          <div class="data-cell rate-cell">{{ e.achievementRate }}%</div>
        </div>
      </div>
      <br />
      <h1>问题及反馈:</h1>
      <a-textarea v-model:value="questionFeedback" disabled="" placeholder="" />
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import cTable from '/@/views/components/Table/index.vue';
  import { onMounted, reactive, ref } from 'vue';
  import { getOtcReportList, getOtcWeekReportDetail } from '/@/api/customerManage/account';
  import dayjs from 'dayjs';
  import { useUserStore } from '/@/store/modules/user';
  import { getDepartmentEnabledTree } from '/@/api/system/department';
  const activeKey = ref('1');

  const searchForm = reactive({
    id: '',
    departList: [],
    leaderUserName: '',
    ownerNameOrNumber: '',
    time: null,
    frequency: '',
    reportDate: '',
    departmentNames: '',
  });
  const questionFeedback = ref('');
  const reportDate = ref();
  const cascaderOptions = ref();
  const getDepartMent = async () => {
    try {
      let res = await getDepartmentEnabledTree();
      cascaderOptions.value = res ?? [];
    } catch (err) {
      console.log(err);
    }
  };
  const dataSourceWeek = ref<any[]>([]);
  const columnsWeek = [
    {
      title: '重点工作',
      dataIndex: 'taskTypeName',
      width: '30%',
    },
    {
      title: '周期目标',
      dataIndex: 'targetNum',
    },
    {
      title: '实际达成',
      dataIndex: 'completeNum',
    },
    {
      title: '完成率',
      isSlot: true,
      slots: { customRender: 'rate' },
      dataIndex: 'rate',
    },
  ];
  const reSet = () => {
    searchForm.id = '';
    searchForm.departList = [];
    searchForm.leaderUserName = '';
    searchForm.ownerNameOrNumber = '';
    searchForm.time = null;
    searchForm.frequency = '';
    getList();
  };
  const tableColumns = [
    {
      title: '报告周期',
      dataIndex: 'reportDate',
      key: 'reportDate',
      align: 'center',
      isSlot: true,
    },
    {
      title: '提交人',
      dataIndex: 'userName',
      key: 'userName',
      align: 'center',
      width: 250,
    },

    {
      title: '所属辖区',
      dataIndex: 'departmentNames',
      key: 'departmentNames',
      align: 'center',
    },
    {
      title: '提交时间',
      dataIndex: 'createDate',
      key: 'createDate',
      align: 'center',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
    },
  ];
  const tableData = reactive({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 500,
    pageSize: 10,
  });
  const detailList = ref([]);
  const startDay = ref('');
  const endDay = ref('');
  const customWeekStartEndFormat = (value) => {
    startDay.value = dayjs(value).startOf('week').add(1, 'day').format('YYYY-MM-DD');
    endDay.value = dayjs(value).endOf('week').add(1, 'day').format('YYYY-MM-DD');
    return `${startDay.value} - ${endDay.value}`;
  };
  const onActiveChange = () => {
    startDay.value = '';
    endDay.value = '';
    getList();
  };
  // 获取表格数据
  const getList = async (flag?: number) => {
    const userStore = useUserStore();
    const userId = userStore.getUserInfo.id;

    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    if (activeKey.value === '1' || activeKey.value === '2') {
      if (searchForm.time) {
        startDay.value = dayjs(searchForm.time?.[0]).format('YYYY-MM-DD');
        endDay.value = dayjs(searchForm.time?.[1]).format('YYYY-MM-DD');
      } else {
        startDay.value = '';
        endDay.value = '';
      }
    }
    try {
      let temp = {
        departmentId: searchForm.departList[searchForm.departList.length - 1],
        frequency: activeKey.value,
        limit: pagination.currentPage,
        size: pagination.pageSize,
        startDay: startDay.value,
        endDay: endDay.value,
      };
      console.log(activeKey.value, 'value值');
      let res = await getOtcReportList(temp);
      console.log(res, 'res');
      tableData.data = res?.list ?? [];
      console.log(tableData.data, '数据');
      pagination.totalItems = res.total ?? 0;
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  };
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };

  // 分配责任人
  const openModalVisible = ref<boolean>(false);
  const weekDetail = ref<any>({});
  const getAllotPersonList = async (val?: string) => {
    try {
      let res = await getOtcWeekReportDetail({ id: val });
      console.log(res, '详情');
      detailList.value = res.detailList;
      questionFeedback.value = res.questionFeedback;
      reportDate.value = res.reportDate;
      console.log(reportDate.value, '报告日期');
    } catch (error) {
      console.log(error);
    }
  };
  const modalLoading = ref<boolean>(false);

  const onAllot = (id) => {
    weekDetail.value = {};
    getAllotPersonList(id);
    openModalVisible.value = true;
    modalLoading.value = true;
  };
  onMounted(() => {
    getList();
    getDepartMent();
  });
</script>

<style scoped lang="less">
  #headOffice {
    width: 100%;
    height: 100%;
    padding: 8px;

    .comPage_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;

      > div {
        width: 100%;
        padding: 16px;

        &:last-child {
          flex: 1;
          height: 0;
        }
      }

      .detail_title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 0;
        padding: 16px 0 0 16px;
      }
    }

    .filterForm_box {
      > * + * {
        margin-left: 16px;
      }

      .synchroData_btn {
        float: right;
      }
    }

    .table_box {
    }
  }

  .modal_box {
    padding: 16px;

    .p_box {
      span {
        margin: 0 4px;
      }

      .ant-select {
        margin-left: 4px;
      }
    }
  }
  .table-header {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .header-cell {
      padding: 14px 8px;
      font-size: 13px;
      font-weight: 500;
      color: #666;
      text-align: center;
      border-right: 1px solid #e9ecef;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      &:last-child {
        border-right: none;
      }

      &.work-cell {
        flex: 2;
        text-align: left;
        padding-left: 16px;
        justify-content: flex-start;
        white-space: normal;
        word-wrap: break-word;
      }

      &.target-cell,
      &.actual-cell,
      &.rate-cell {
        flex: 1;
      }
    }
  }

  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s ease;
      min-height: 48px;

      &:hover {
        background-color: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .data-cell {
        padding: 16px 12px;
        font-size: 13px;
        color: #333;
        text-align: center;
        border-right: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &:last-child {
          border-right: none;
        }

        &.work-cell {
          flex: 2;
          text-align: left;
          padding-left: 16px;
          justify-content: flex-start;
          font-weight: 500;
          white-space: normal;
          word-wrap: break-word;
          word-break: break-all;
          line-height: 1.4;
          align-items: flex-start;
          padding-top: 16px;
        }

        &.target-cell,
        &.actual-cell {
          flex: 1;
          font-weight: 600;
          color: #333;
        }

        &.rate-cell {
          flex: 1;
          font-weight: 600;
          color: #52c41a;
        }
      }
    }
  }
</style>
