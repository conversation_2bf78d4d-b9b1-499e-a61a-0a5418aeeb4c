<template>
  <div id="headOffice">
    <div class="comPage_Box">
      <a-tabs v-model:activeKey="activeKey" @change="onChange" >
        <a-tab-pane class="comPage_Box" v-for="item in frequencyList" :key="item.frequency" :tab="item.frequencyName">
          <div class="filterForm_box">
            <a-cascader
              style="width: 300px"
              v-model:value="searchForm.departList"
              :field-names="{ label: 'name', value: 'id', children: 'children' }"
              :options="cascaderOptions"
              change-on-select
              placeholder="请选择组织架构"
              @change="getList()"
            />
            <a-input
              style="width: 240px"
              v-model:value.lazy="searchForm.edpCodeOrName"
              placeholder="员工姓名/工号"
              allowClear
              @change="getList()"
              @pressEnter="getList()"
            />
            <a-date-picker
              v-model:value="searchForm.time"
              placeholder="请选择时间"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              v-if="item.frequency === '1'"
              :allow-clear="false"
              @change="getList()"
            />
            <a-date-picker
              v-model:value="searchForm.time"
              placeholder="请选择时间"
              v-if="item.frequency === '3'"
              picker="week"
              :format="customWeekStartEndFormat"
              :allow-clear="false"
              @change="getList()"
            />
            <a-date-picker
              v-model:value="searchForm.time"
              placeholder="请选择时间"
              v-if="item.frequency === '2'"
              picker="month"
              format="YYYY-MM"
              valueFormat="YYYY-MM"
              :allow-clear="false"
              @change="getList()"
            />
            <a-button type="primary" @click="getList()">搜索</a-button>
            <a-button @click="reSet()">重置</a-button>
            <a-button class="right_btn" type="primary" @click="onSignSet">导出</a-button>
          </div>
          <div class="table_box">
            <c-table
              :tableColumns="rankColumns"
              :tableData="tableData.data"
              :loading="loading"
              :currentPage="pagination.currentPage"
              :totalItems="pagination.totalItems"
              :pageSize="pagination.pageSize"
              @update:current-page="(value) => (pagination.currentPage = value)"
              @pagination-change="handlePaginationChange"
              rowKey="id"
              :border="true"
              :scroll="{ x: 'max-content'}"
            >
            </c-table>
          </div>
        </a-tab-pane>
      </a-tabs>

    </div>
  </div>
</template>
<script setup lang="ts">
import cTable from '/@/views/components/Table/index.vue';
import {onMounted, reactive, ref} from 'vue';
import { getDepartmentEnabledTree } from "/@/api/system/department";
import {useUserStore} from "/@/store/modules/user";
import {
  exportPerformance,
  exportVisitData,
  getPerformanceList
} from "/@/api/performanceManage/targetManage";
import dayjs from 'dayjs';
import { downloadByData } from "/@/utils/file/download";

const cascaderOptions = ref();
const getDepartMent = async () => {
  try {
    let res = await getDepartmentEnabledTree();
    cascaderOptions.value = res ?? [];
  } catch (err) {
    console.log(err);
  }
};
const searchForm = reactive({
  departList:[],
  edpCodeOrName: '',
  time: dayjs(),
  startTime:'',
  endTime:'',
  frequency: '1',
  performanceDate: ''
});
const tableData = reactive({
  data: [],
});
const loading = ref(false);
const pagination = reactive({
  currentPage: 1,
  totalItems: 500,
  pageSize: 10,
});
const rankColumns = ref<any>([])
const activeKey=ref('1')
const frequencyList = [
    {
      "frequency": "1",
      "frequencyName": "每日",
    },
    {
      "frequency": "3",
      "frequencyName": "每周",
    },
    {
      "frequency": "2",
      "frequencyName": "每月",
    }
]
const customWeekStartEndFormat = (value) => {
  console.log(value);
  // 格式化周日期范围（周一至周日）
  const dateRange = `${dayjs(value).startOf('week').add(1, 'day').format('YYYY-MM-DD')} - 
    ${dayjs(value).endOf('week').add(1, 'day').format('YYYY-MM-DD')}`;
}

onMounted(async () => {
  await getDepartMent();
  // searchForm.time = [
  //   dayjs().format('YYYY-MM-DD'),
  //   dayjs().format('YYYY-MM-DD'),
  // ]
  // searchForm.startTime = dayjs().format('YYYY-MM-DD');
  // searchForm.endTime = dayjs().format('YYYY-MM-DD');
  searchForm.performanceDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
  searchForm.time = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
  await getList();
})

// 获取表格数据
const getList = async (flag?: number) => {
  const userStore = useUserStore();
  const userId = userStore.getUserInfo.id;

  if (!flag) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
  }
  loading.value = true;
  tableData.data = [];
  try {
    // 根据频率类型格式化时间
    let performanceDate: undefined | string = undefined;
    if (searchForm.frequency === '1') {
      // 每日：YYYY-MM-DD
      performanceDate = dayjs(searchForm.time).format('YYYY-MM-DD');
    } else if (searchForm.frequency === '2') {
      // 每月：YYYY-MM
      performanceDate = dayjs(searchForm.time).format('YYYY-MM');
    } else if (searchForm.frequency === '3') {
      // 每周：YYYY-MM-DD
      // performanceDate = dayjs(searchForm.time).format('YYYY-MM-DD');
      console.log(searchForm.time);
      performanceDate = undefined
    }

    let temp = {
      ...searchForm,
      userId,
      performanceDate: performanceDate,
      deptId: searchForm.departList?.[searchForm.departList.length - 1] ?? '',
      departList: null,
      edpCodeOrName: searchForm.edpCodeOrName,
      limit: pagination.currentPage,
      size: pagination.pageSize,
      frequency: searchForm.frequency,
      startTime: performanceDate ? undefined : dayjs(searchForm.time).startOf('week').add(1, 'day').format('YYYY-MM-DD'),
      endTime: performanceDate ? undefined : dayjs(searchForm.time).endOf('week').add(1, 'day').format('YYYY-MM-DD')
    };
    // let temp = {
    //   ...searchForm,
    //   userId,
    //   startTime: searchForm.time?.[0]
    //     ? dayjs(searchForm.time?.[0]).format('YYYY-MM-DD')
    //     : '',
    //   endTime: searchForm.time?.[1]
    //     ? dayjs(searchForm.time?.[1]).format('YYYY-MM-DD')
    //     : '',
    //   deptId:searchForm.departList?.[searchForm.departList.length - 1] ?? '',
    //   departList: null,
    //   edpCodeOrName: searchForm.edpCodeOrName,
    //   limit: pagination.currentPage,
    //   size: pagination.pageSize,
    // };
    let res = await getPerformanceList(temp);
    if (!res.list || res.list.length === 0) {
      tableData.data = [];
      loading.value = false;
      return;
    }
    const processedRankData = res.list.map((item: any, index: number) => ({
      ...item,
      id: item.edpCode || `rank_${index}`, // 使用EDP工号作为ID，如果没有则使用索引
    }));

    tableData.data = processedRankData;
    rankColumns.value = generateRankColumns(processedRankData);
    console.log();
    // tableData.data = res?.list ?? [];
    pagination.totalItems = res.total ?? 0;
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
// 处理分页
const handlePaginationChange = (page: any) => {
  pagination.currentPage = page.current;
  pagination.pageSize = page.pageSize;
  getList(1);
};

// 生成动态列配置
const generateRankColumns = (data: any[]) => {
  // 固定列
  const fixedColumns = [
    {
      title: '员工姓名',
      dataIndex: 'saleManName',
      key: 'saleManName',
      align: 'center',
      width: 100,
      minWidth: 100,
      fixed: 'left',
    },
    {
      title: 'EDP工号',
      dataIndex: 'edpCode',
      key: 'edpCode',
      align: 'center',
      width: 100,
      minWidth: 100,
      fixed: 'left',
    },
    {
      title: '所属辖区',
      dataIndex: 'departNames',
      key: 'departNames',
      align: 'center',
      width: 180,
      minWidth: 180,
      fixed: 'left',
    },
  ];

  // 动态列 - 根据taskKpiList生成
  const dynamicColumns: any[] = [];

  if (data && data.length > 0 && data[0].taskKpiList) {
    // 遍历taskKpiList，每个task创建一个合并列
    data[0].taskKpiList.forEach((task: any) => {
      const { taskName, kpiList } = task;

      if (kpiList && kpiList.length > 0) {
        // 为每个task的kpiList创建子列 - 每个KPI生成两列：权重和分数
        const children: any[] = [];

        kpiList.forEach((kpi: any) => {
          children.push({
            title: kpi.kpiName, // 小表头
            dataIndex: `kpi_${taskName}_${kpi.kpiName}`,
            key: `kpi_${taskName}_${kpi.kpiName}`,
            align: 'center',
            width: 130,
            minWidth: 130,
            isSlot: true,
            customRender: ({ record }: any) => {
              // 在record的taskKpiList中找到对应的task，然后在其kpiList中找到对应的kpi
              const taskData = record.taskKpiList?.find((item: any) => item.taskName === taskName);
              if (taskData && taskData.kpiList) {
                const kpiData = taskData.kpiList.find((item: any) => item.kpiName === kpi.kpiName);
                return kpiData ? kpiData.real : '-'; // 绑定real字段
              }
              return '-';
            },
          })
          // 权重列
          children.push({
            title: `权重`, // 权重列标题
            dataIndex: `kpi_${taskName}_${kpi.kpiName}_weight`,
            key: `kpi_${taskName}_${kpi.kpiName}_weight`,
            align: 'center',
            width: 120,
            minWidth: 120,
            isSlot: true,
            customRender: ({ record }: any) => {
              const taskData = record.taskKpiList?.find((item: any) => item.taskName === taskName);
              if (taskData && taskData.kpiList) {
                const kpiData = taskData.kpiList.find((item: any) => item.kpiName === kpi.kpiName);
                return kpiData ? `${kpiData.weight || 0}%` : '-'; // 显示权重百分比
              }
              return '-';
            },
          });

          // 分数列
          children.push({
            title: `分数`, // 分数列标题
            dataIndex: `kpi_${taskName}_${kpi.kpiName}_score`,
            key: `kpi_${taskName}_${kpi.kpiName}_score`,
            align: 'center',
            width: 120,
            minWidth: 120,
            isSlot: true,
            customRender: ({ record }: any) => {
              const taskData = record.taskKpiList?.find((item: any) => item.taskName === taskName);
              if (taskData && taskData.kpiList) {
                const kpiData = taskData.kpiList.find((item: any) => item.kpiName === kpi.kpiName);
                return kpiData ? (kpiData.score || 0) : '-'; // 显示分数或实际值
              }
              return '-';
            },
          });
        });

        // 创建父列（合并列）- taskName作为大表头
        dynamicColumns.push({
          title: taskName, // 大表头
          key: taskName,
          align: 'center',
          children: children,
        });
      }
    });
  }

  return [...fixedColumns, ...dynamicColumns];
};

const reSet = () => {
  searchForm.departList = [];
  searchForm.edpCodeOrName = '';

  // 根据当前频率重置时间
  if (searchForm.frequency === '1') {
    // 每日
    searchForm.time = dayjs().subtract(1, 'day');
  } else if (searchForm.frequency === '2') {
    // 每月
    searchForm.time = dayjs().subtract(1, 'month').format('YYYY-MM');
  } else if (searchForm.frequency === '3') {
    // 每周
    searchForm.time = dayjs().subtract(1, 'week');
  }

  getList();
};

const onSignSet = async () => {
  let res: any = null
  let time: any = { startTime: null, endTime: null };
  if (searchForm.time?.length) {
    time = {
      startTime: dayjs(searchForm.time?.[0]).format('YYYY-MM-DD 00:00:00'),
      endTime: dayjs(searchForm.time?.[1]).endOf('month').format('YYYY-MM-DD 23:59:59'),
    };
  }
  let temp = {
    ...searchForm,
    ...time,
    deptId: searchForm.departList?.[searchForm.departList.length - 1] ?? undefined,
  };
  res = await exportPerformance(temp);
  const name ='业绩明细' + dayjs(new Date()).format('YYYY-MM-DD')
   downloadByData(
    res.data,
    `${name}.xlsx`,
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  );
};

const onChange = (e) => {
  searchForm.frequency = e;
  if (e === '1') {
    // 每日
    searchForm.performanceDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
    searchForm.time = dayjs().subtract(1, 'day');
  } else if (e === '2') {
    // 每月
    searchForm.performanceDate = dayjs().format('YYYY-MM');
    searchForm.time = dayjs().subtract(1, 'month').format('YYYY-MM');
  } else if (e === '3') {
    // 每周
    searchForm.performanceDate = '';
    searchForm.time = dayjs().subtract(1, 'week');
  }
  getList();
}

</script>
<style scoped lang="less">
#headOffice {
  width: 100%;
  height: 100%;
  padding: 8px;

  .comPage_Box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    > div {
      width: 100%;
      padding: 16px;

      &:last-child {
        flex: 1;
        height: 0;
      }
    }

    .detail_title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 0;
      padding: 16px 0 0 16px;
    }
  }

  .filterForm_box {
    > * + * {
      margin-left: 16px;
    }

    .synchroData_btn {
      float: right;
    }
  }

  .table_box {
  }
}

.modal_box {
  padding: 16px;

  .p_box {
    span {
      margin: 0 4px;
    }

    .ant-select {
      margin-left: 4px;
    }
  }
}
</style>
