<template>
  <div class="target_detail">
    <div class="detail_Box">
      <div class="detail_title">{{ `${titleType.name ?? ''}  ${titleTime}` }}</div>
      <a-tabs v-model:activeKey="activeKey" @change="onChange">
        <a-tab-pane :key="item.frequency" :tab="item.frequencyName" v-for="item in items">
          <div class="filterForm_box">
            <a-cascader
              style="width: 300px"
              v-model:value="searchForm.departList"
              :field-names="{ label: 'name', value: 'id', children: 'children' }"
              :options="cascaderOptions"
              change-on-select
              placeholder="请选择组织架构"
              @change="getList()"
            />
            <a-input
              style="width: 240px"
              v-model:value.lazy="searchForm.edpCodeOrName"
              placeholder="员工姓名/EDP工号"
              allowClear
              @change="getList()"
            />
            <a-range-picker
              v-if="titleType.code !== 1"
              v-model:value="searchForm.time"
              :allowClear="false"
              :disabled-date="disabledDate"
              :picker="item.frequency === '2' ? 'month' : 'date'"
              :valueFormat="item.frequency === '2' ? 'YYYY-MM' : 'YYYY-MM-DD'"
              @change="getList()"
            />
            <a-button type="primary" @click="getList()">搜索</a-button>
            <a-button @click="reset">重置</a-button>
            <a-button class="export_btn" type="primary" @click="toExport()">导出</a-button>
          </div>
          <div class="table_box">
            <c-table
              :tableColumns="tempColumns"
              :tableData="tableData.data"
              :loading="loading"
              :currentPage="pagination.currentPage"
              :totalItems="pagination.totalItems"
              :pageSize="pagination.pageSize"
              :scroll="{ x: 1000 }"
              @update:current-page="(value) => (pagination.currentPage = value)"
              @pagination-change="handlePaginationChange"
            />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { useRoute } from 'vue-router';
  import cTable from '/@/views/components/Table/index.vue';
  import { onMounted, reactive, ref, computed } from 'vue';
  import { tableDetailOptions } from './targetData';
  import { exportVisitData, exportSalesPage } from '/@/api/performanceManage/targetManage';
  import { frequencyFieldList, visitFieldList } from '/@/api/performanceManage/frequencyManage';
  import { downloadByData } from '/@/utils/file/download';
  import dayjs from 'dayjs';
  import { getDepartmentEnabledTree } from '/@/api/system/department';
  const kpiList = ref([]);

  let activeKey = ref('');

  const cascaderOptions = ref();

  const tempColumns = ref([]);
  // if (tableData.data.length > 0 && Array.isArray(tableData.data[0].kpiList)) {
  //      tableData.data[0].kpiList.forEach(kpi => {
  //      const kpiNames = `${kpiList.kpiName}`;
  // }
  // };

  const getDepartMent = async () => {
    try {
      let res = await getDepartmentEnabledTree();
      console.log(res, '打印');
      cascaderOptions.value = res ?? [];
    } catch (err) {
      console.log(err);
    }
  };
  interface SearchFormType {
    taskId: string | number;
    time: dayjs.Dayjs[] | [];
    edpCodeOrName?: string | undefined;
    departList: any[];
    frequency: number | null;
    startTime: string;
    endTime: string;
    size: number;
    weekStart: string;
    weekEnd: string;
  }
  const searchForm = reactive<SearchFormType>({
    taskId: '',
    time: [],
    edpCodeOrName: undefined,
    departList: [],
    frequency: null,
    startTime: '',
    endTime: '',
    size: 1,
    weekStart: '',
    weekEnd: '',
  });
  const reset = () => {
    searchForm.time = [];
    searchForm.edpCodeOrName = undefined;
    searchForm.departList = [];
    titleTime.value =
      startDate.value && endDate.value ? startDate.value + ' 至 ' + endDate.value : '';
    let type = route.query.taskType as string;
    let temp = { name: type, code: 3 };
    if (type.includes('业绩')) temp = { name: '连锁配送业绩', code: 1 };
    if (type.includes('协访')) temp = { name: '协访目标', code: 2 };
    // if (type.includes('拜访')) temp = { name: '拜访目标', code: 3 };
    titleType.value = temp;
    searchForm.time = [startDate.value, endDate.value];
    getList();
  };
  const titleType = ref({ name: '', code: -1 }); // 标题类型
  const titleTime = ref(''); // 标题时间
  const startDate = ref(); // 目标周期开始节点
  const endDate = ref(); // 目标周期结束节点
  const items = ref();
  const route = useRoute();
  onMounted(async () => {
    searchForm.taskId = route.query.id as string;
    startDate.value = route.query.startDate;
    endDate.value = route.query.endDate;
    titleTime.value =
      startDate.value && endDate.value ? startDate.value + ' 至 ' + endDate.value : '';
    let type = route.query.taskType as string;
    titleType.value = { name: type, code: 3 };
    const result = await frequencyList();
    items.value = result;
    if (Array.isArray(items.value) && items.value.length > 0) {
      activeKey.value = items.value[0].frequency;
      console.log(activeKey.value, 'key');
    }
    if (activeKey.value == '2') {
      const currentMonth = dayjs().format('YYYY-MM');
      const startMonth = dayjs(startDate.value).format('YYYY-MM');
      if (startMonth === currentMonth) {
        searchForm.time = [];
      } else {
        searchForm.time = [dayjs().add(-1, 'month').startOf('month'), dayjs().add(-1, 'month').endOf('month')];
      }
    } else if (activeKey.value === '3') {
      searchForm.time = [dayjs().add(-1, 'week').startOf('week').add(1, 'day').format('YYYY-MM-DD'), dayjs().add(-1, 'week').endOf('week').add(1, 'day').format('YYYY-MM-DD')];
    } else {
      searchForm.time = [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')];
    }
    console.log(searchForm.time, '选择时间');
    await getList();
    getDepartMent();
  });

  const tableData = reactive({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 500,
    pageSize: 10,
  });
  const onChange = (key) => {
    console.log('activeKey =', key, '类型 =', typeof key);
    tempColumns.value = [];
    if (activeKey.value === '2') {
      const currentMonth = dayjs().format('YYYY-MM');
      const startMonth = dayjs(startDate.value).format('YYYY-MM');

      if (startMonth === currentMonth) {
        searchForm.time = [];
      } else {
        searchForm.time = [
          dayjs().add(-1, 'month').startOf('month'),
          dayjs().add(-1, 'month').endOf('month'),
        ];
      }
    } else if (activeKey.value === '1') {
      searchForm.time = [
        dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      ];
    } else {
      searchForm.time = [
        dayjs().add(-1, 'week').startOf('week').add(1, 'day').format('YYYY-MM-DD'),
        dayjs().add(-1, 'week').endOf('week').add(1, 'day').format('YYYY-MM-DD'),
      ];
    }
    console.log('activeKey.value =', activeKey.value, '类型 =', typeof activeKey.value);
    searchForm.departList = [];
    searchForm.edpCodeOrName = '';
    getList();
  };
  const getList = async (flag?: number) => {
    tempColumns.value = [
      {
        title: '员工姓名',
        dataIndex: 'salesmanName',
        key: 'salesmanName',
        align: 'center',
        width: 150,
        fixed: 'left',
      },
      {
        title: 'EDP工号',
        dataIndex: 'edpCode',
        key: 'edpCode',
        align: 'center',
        width: 150,
        fixed: 'left',
      },
      {
        title: '所属辖区',
        dataIndex: 'departNames',
        key: 'departNames',
        align: 'center',
        width: 200,
        fixed: 'left',
      },
    ];
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      const params = {
        limit: pagination.currentPage,
        size: pagination.pageSize,
        departId: searchForm.departList?.[searchForm.departList.length - 1] ?? '',
        frequency: activeKey.value,
        time: searchForm.time || undefined,
        edpCodeOrName: searchForm.edpCodeOrName,
        taskId: searchForm.taskId,
        startTime:
          searchForm.time && searchForm.time.length > 0
            ? dayjs(searchForm.time?.[0]).format('YYYY-MM-DD')
            : startDate.value,
        endTime:
          searchForm.time && searchForm.time.length > 0
            ? activeKey.value == '2'
              ? dayjs(searchForm.time?.[1]).isSame(dayjs(), 'month')
                ? dayjs(new Date()).format('YYYY-MM-DD')
                : dayjs(searchForm.time?.[1]).endOf('month').format('YYYY-MM-DD')
              : dayjs(searchForm.time?.[1]).format('YYYY-MM-DD')
            : endDate.value,
      };
      const res = await visitFieldList(params);
      if (res && res.list) {
        // tableData.data = res.list.map((item, index) => ({
        //   ...item,
        //   index: (pagination.currentPage - 1) * pagination.pageSize + index + 1,
        //   required: item.required === '1',
        //   baseline: item.kpiList[0].baseline,
        //   real: item.kpiList[1].real,
        // }));
        let tempArr = res.list;
        tempArr.forEach((item, index) => {
          if (item?.kpiList && item.kpiList.length > 0) {
            item.index = index + 1;
            item.kpiList.forEach((e, flag) => {
              item['baseline' + flag] = e.baseline;
              item['real' + flag] = e.real;
            });
          }
        });
        tableData.data = tempArr;
        pagination.totalItems = res.total || 0;
      }
      const kpiNames = tableData.data[0].kpiList;
      let dataname = '';
      if (activeKey.value == '1') {
        dataname = '日期';
      } else if (activeKey.value == '2') {
        dataname = '月份';
      } else {
        dataname = '周次';
      }
      // const week = `${weekStart} ${weekEnd}`;
      tempColumns.value.push({
        title: dataname,
        dataIndex: 'weekStart',
        key: 'week',
        align: 'center',
        width: 200,
        isSlot: true,
        customRender: ({ record }) => {
          const start = record.weekStart;
          const end = record.weekEnd;
          const day = record.currentTime;
          if (dataname == '月份') {
            return dayjs(end).format('YYYY-MM');
          } else if (dataname == '日期') {
            return dayjs(day).format('YYYY-MM-DD');
          } else {
            return `${dayjs(start).format('YYYY-MM-DD')} 至 ${dayjs(end).format('YYYY-MM-DD')}`;
          }
        },
      });
      for (let i = 0; i < kpiNames.length; i++) {
        tempColumns.value.push({
          title: kpiNames[i].kpiName + '-目标值(基准)',
          dataIndex: 'baseline' + i,
          key: 'baseline' + i,
          align: 'center',
          width: 150,
        });
        tempColumns.value.push({
          title: kpiNames[i].kpiName + '-实际值',
          dataIndex: 'real' + i,
          key: 'real' + i,
          align: 'center',
          width: 150,
        });
      }

      loading.value = false;
    } catch (error) {
      loading.value = false;
    }
  };
  const frequencyList = async () => {
    try {
      const result = await frequencyFieldList({ id: searchForm.taskId });
      const orderMap = {
        1: 1,
        3: 2,
        2: 3,
      };
      const sortedResult = (result || []).sort((a, b) => {
        return (orderMap[a.frequency] || 99) - (orderMap[b.frequency] || 99);
      });
      return sortedResult;
    } catch (error) {
      console.error('Error fetching resource:', error);
      throw error;
    }
  };

  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    tempColumns.value = [
      // { title: '序号', dataIndex: 'index', key: 'index', align: 'center', width: 80 },
      {
        title: '员工姓名',
        dataIndex: 'salesmanName',
        key: 'salesmanName',
        align: 'center',
        width: 150,
        fixed: 'left',
      },
      {
        title: 'EDP工号',
        dataIndex: 'edpCode',
        key: 'edpCode',
        align: 'center',
        width: 150,
        fixed: 'left',
      },
      {
        title: '所属辖区',
        dataIndex: 'departNames',
        key: 'departNames',
        align: 'center',
        width: 200,
        fixed: 'left',
      },
    ];
    getList(1);
  };
  // const btnType = ref(1);
  // const chooseBtnType = (type: number) => {
  //   btnType.value = type;
  //   getList();
  // };
  // 导出
  const toExport = async (flag?: number) => {
    let res: any = null;
    loading.value = true;
    try {
      const params = {
        frequency: activeKey.value,
        // time: searchForm.time || undefined,
        edpCodeOrName: searchForm.edpCodeOrName,
        taskId: searchForm.taskId,
        departId: searchForm.departList?.[searchForm.departList.length - 1] ?? '',
        startTime:
          searchForm.time && searchForm.time.length > 0
            ? dayjs(searchForm.time?.[0]).format('YYYY-MM-DD')
            : startDate.value,
        endTime:
          searchForm.time && searchForm.time.length > 0
            ? dayjs(searchForm.time?.[1]).format('YYYY-MM-DD')
            : endDate.value,
      };
      res = await exportVisitData(params);
      console.log('导出返回数据', res);
      downloadByData(
        res.data,
        `${titleType.value.name}-${dayjs(new Date()).format('YYYY-MM-DD')}.xlsx`,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } catch (error) {
      console.error('导出失败', error);
    } finally {
      loading.value = false;
    }
  };

  // 日期禁选规则
  const disabledDate = computed(() => {
    return (current: dayjs.Dayjs) => {
      const start = dayjs(startDate.value);
      if (activeKey.value === '1') {
        return current < start || current > dayjs(new Date());
      } else if (activeKey.value === '2') {
        return current < start || current > dayjs().endOf('month');
      } else {
        return current < start || current > dayjs(new Date());
      }
    };
  });
</script>

<style scoped lang="less">
  .target_detail {
    width: 100%;
    height: 100%;
    padding: 8px;
    .detail_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      > div {
        width: 100%;
        padding: 16px;
        &:last-child {
          flex: 1;
          height: 0;
        }
      }
      .detail_title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 0;
        padding: 16px 0 0 16px;
      }
    }
    .filterForm_box {
      > * + * {
        margin-left: 16px;
      }
      // .export_btn {
      //   margin-left: 50px;
      // }
      .btn_group {
        display: inline-flex;
        float: right;
        > div {
          padding: 4px 15px;
          border: 1px solid #d9d9d9;
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
          cursor: pointer;
          &:first-child {
            border-right: none;
            border-radius: 3px 0 0 3px;
          }
          &:last-child {
            border-left: none;
            border-radius: 0 3px 3px 0;
          }
        }
        .is_active {
          color: #fff;
        }
      }
    }
    .table_box {
    }
  }
</style>
