import { getDicDetailList } from '/@/api/system/dic';
import { getPostList } from '/@/api/system/post';
import {
  getTask,
  getTaskPage,
  toTaskAdd,
  toTaskDel,
  toTaskUpdate,
  updateTaskEnabled,
} from '/@/api/performanceManage/targetManage';

interface IOptions {
  name: string;
  code: string;
}
// 目标类型
export const getTypeOptions = async () => {
  const res = (await getDicDetailList({ itemId: '1873985657830871041' })) as
    | IOptions[]
    | null
    | undefined;
  return res ?? [];
};

// 目标状态
export const getStatusOptions = async () => {
  const res = await getDicDetailList({ itemId: '1874003942026743809' });
  return res;
};

// 核算频率
export const getFrequencyOptions = async () => {
  const res = await getDicDetailList({ itemId: '1874692484663074818' });
  return res;
};

// 数据范围
export const getDataRangeOptions = async () => {
  return await getDicDetailList({ itemId: '1940608946591232001' });
};

// 执行人
export const getLevelOptions = async () => {
  const res = await getPostList({ menuIds: [], buttonIds: [] });
  return res;
};

export const getTaskPageList = async (params?: object) => {
  const res = await getTaskPage(params);
  return res;
};

export const getTaskById = async (params?: object) => {
  const res = await getTask(params);
  return res;
};

export const onTaskUpdate = async (params: object) => {
  const res = await toTaskUpdate(params);
  return res;
};

export const onTaskEnabled = async (params: object) => {
  const res = await updateTaskEnabled(params);
  return res;
};

export const onTaskAdd = async (params: object) => {
  const res = await toTaskAdd(params);
  return res;
};

export const onTaskDel = async (params: object) => {
  const res = await toTaskDel(params);
  return res;
};



// 达成明细
export const tableDetailOptions = {
  // 业绩
  1: [
    {
      title: '员工姓名',
      dataIndex: 'emplName',
      key: 'emplName',
      align: 'center',
      summary: true,
      sumKey: 'title',
    },
    {
      title: 'EDP工号',
      dataIndex: 'erpCode',
      key: 'erpCode',
      align: 'center',
    },
    {
      title: '所属辖区',
      dataIndex: 'businessUnitIdName',
      key: 'businessUnitIdName',
      align: 'center',
    },
    {
      title: '年计划',
      dataIndex: 'yearPlanAmount',
      key: 'yearPlanAmount',
      align: 'center',
    },
    {
      title: '年实绩',
      dataIndex: 'yearFinishAmount',
      key: 'yearFinishAmount',
      align: 'center',
    },
    {
      title: '月计划',
      dataIndex: 'monthPlanAmount',
      key: 'monthPlanAmount',
      align: 'center',
      summary: true,
      sumKey: 'total',
    },
    {
      title: '月实绩',
      dataIndex: 'monthFinishAmount',
      key: 'monthFinishAmount',
      align: 'center',
      summary: true,
      sumKey: 'total',
    },
    {
      title: '指标完成率',
      dataIndex: 'finishRate',
      key: 'finishRate',
      align: 'center',
      summary: true,
      sumKey: 'total',
      sumType: 'percent',
    },
    {
      title: '同比增长率',
      dataIndex: 'yoyGrowthRate',
      key: 'yoyGrowthRate',
      align: 'center',
    },
  ],
  // 协访
  2: [
    {
      title: '所属辖区',
      dataIndex: 'departNames',
      key: 'departNames',
      align: 'center',
      summary: true,
      sumKey: 'title',
    },
    {
      title: '员工姓名',
      dataIndex: 'saleManName',
      key: 'saleManName',
      align: 'center',
    },
    {
      title: 'EDP工号',
      dataIndex: 'edpCode',
      key: 'edpCode',
      align: 'center',
    },
    {
      title: '目标值',
      dataIndex: 'target',
      key: 'target',
      align: 'center',
      summary: true,
      sumKey: 'total',
    },
    {
      title: '实际值',
      dataIndex: 'real',
      key: 'real',
      align: 'center',
      summary: true,
      sumKey: 'total',
    },
    {
      title: '完成率',
      dataIndex: 'completionRate',
      key: 'completionRate',
      align: 'center',
      summary: true,
      sumKey: 'total',
      sumType: 'percent',
    },
  ],
  // 拜访
  3: [
    {
      title: '员工姓名',
      dataIndex: 'saleManName',
      key: 'saleManName',
      align: 'center',
      summary: true,
      sumKey: 'title',
    },
    {
      title: 'EDP工号',
      dataIndex: 'edpCode',
      key: 'edpCode',
      align: 'center',
    },
    {
      title: '所属辖区',
      dataIndex: 'departNames',
      key: 'departNames',
      align: 'center',
    },
    {
      title: '目标值',
      dataIndex: 'target',
      key: 'target',
      align: 'center',
      summary: true,
      sumKey: 'total',
    },
    {
      title: '实际值',
      dataIndex: 'real',
      key: 'real',
      align: 'center',
      summary: true,
      sumKey: 'total',
    },
    {
      title: '完成率',
      dataIndex: 'completionRate',
      key: 'completionRate',
      align: 'center',
      summary: true,
      sumKey: 'total',
      sumType: 'percent',
    },
  ],
};
