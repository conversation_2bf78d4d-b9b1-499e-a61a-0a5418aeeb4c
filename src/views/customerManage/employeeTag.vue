<template>
  <div id="employeeTag">
    <div class="targetMange_Box">
      <div class="filterForm_box">
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.tagName"
          placeholder="标签名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
      </div>
      <div class="p_box">
        <a-button type="primary" @click="onEdit()">新增标签</a-button>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
        >
          <template #statusId="{ record }">
            <span
              class="tag_div"
              :style="{ color: record.tagColor, borderColor: record.tagColor }"
              >{{ record.tagName }}</span
            >
          </template>
          <template #showStatus="{ record }">
            <a-radio-group
              :disabled="record.tagCode === '4'"
              v-model:value="record.showStatus"
              name="radioGroup"
              @change="editStatus(record)"
            >
              <a-radio value="1">显示</a-radio>
              <a-radio value="0">隐藏</a-radio>
            </a-radio-group>
          </template>
          <template #incidenceNumber="{ record }">
            <span
              class="storeNum"
              :style="{ color: getThemeColor, borderBottom: `1px solid ${getThemeColor}` }"
              @click="onConnectDetail(record)"
              >{{ record.incidenceNumber }}</span
            >
          </template>
          <template #action="{ record }">
            <a-button type="link" @click.stop="onConnect(record)">关联客户</a-button>
            <a-button type="link" v-if="record.tagCode !== '4'" @click.stop="onEdit(record)"
              >编辑</a-button
            >
            <a-button
              type="link"
              v-if="record.tagCode !== '4'"
              :loading="record.delloading"
              @click.stop="onDelete(record)"
              >删除</a-button
            >
          </template>
        </c-table>
      </div>
    </div>
  </div>
  <a-modal
    :width="960"
    v-model:visible="openModalVisible"
    :title="modalInfo.id ? '编辑标签' : '新增标签'"
    :confirm-loading="modalLoading"
    :maskClosable="false"
    destroyOnClose
    centered
    @ok="handleModalOk"
    @cancel="() => (openModalVisible = false)"
  >
    <div class="modal_box">
      <p>基础信息</p>
      <a-form
        class="form_box"
        ref="formRef"
        :model="modalInfo"
        :rules="modalRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        autocomplete="off"
        labelAlign="right"
        :colon="true"
        @validate="handleValidate"
      >
        <a-form-item label="标签名称" name="tagName" required>
          <a-input
            style="width: 220px"
            v-model:value.lazy="modalInfo.tagName"
            :maxlength="5"
            show-count
            placeholder="请输入"
            allowClear
          />
        </a-form-item>
        <a-form-item label="标签类型" name="tagType" required>
          <a-select v-model:value="modalInfo.tagType" style="width: 220px" placeholder="请选择">
            <a-select-option v-for="option in typeOptions" :key="option.value" :value="option.value">
              {{ option.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="标签编码" name="tagCode" required>
          <a-input-number
            style="width: 220px"
            v-model:value.lazy="modalInfo.tagCode"
            placeholder="请输入"
            min="0"
          />
        </a-form-item>
        <a-form-item label="标签颜色" name="tagColor" required>
          <input type="color" v-model="modalInfo.tagColor" />
        </a-form-item>
        <a-form-item label="分类排序" name="sort">
          <a-inputNumber v-model:value="modalInfo.sort" placeholder="请输入" style="width: 220px" />
        </a-form-item>
        <a-form-item label="是否前端显示" name="showStatus" required>
          <a-radio-group v-model:value="modalInfo.showStatus" name="showStatus">
            <a-radio value="1">显示</a-radio>
            <a-radio value="0">隐藏</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
  <connectDetailModal ref="connectDetailRef" @on-hide="getList()" />
  <connectModal ref="connectRef" @on-hide="getList()" />
</template>

<script lang="ts" setup>
  import cTable from '/@/views/components/Table/index.vue';
  import connectDetailModal from './components/connectDetailModal.vue';
  import connectModal from './components/connectModal.vue';
  import { onMounted, reactive, ref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
  const { getThemeColor } = useRootSetting();
  const { notification } = useMessage();
  import { getTagPage, tagDel, tagAdd } from '/@/api/customerManage/tags';
  import { getDicDetailList } from '/@/api/system/dic';

  // 获取标签类型
  const typeOptions = ref<any[]>([]);
  const getTypeOptions = async () => {
    let res = await getDicDetailList({ itemId: '1878734277637451777' });
    typeOptions.value = res.map((item) => ({
      name: item.name,
      value: item.value,
    }));
  };
  const searchForm = reactive({
    tagName: '',
  });
  const reSet = () => {
    searchForm.tagName = '';
    getList();
  };
  const tableColumns = [
    {
      title: '标签名称',
      dataIndex: 'tagName',
      key: 'tagName',
      align: 'center',
    },
    {
      title: '标签类型',
      dataIndex: 'tagTypeName',
      key: 'tagTypeName',
      align: 'center',
    },
    {
      title: '标签展示',
      dataIndex: 'statusId',
      key: 'statusId',
      isSlot: true,
      align: 'center',
    },
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      align: 'center',
    },
    {
      title: '前端是否展示',
      dataIndex: 'showStatus',
      key: 'showStatus',
      isSlot: true,
      align: 'center',
    },
    {
      title: '已关联门店',
      dataIndex: 'incidenceNumber',
      key: 'incidenceNumber',
      isSlot: true,
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
    },
  ];
  const tableData = reactive({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 500,
    pageSize: 10,
  });
  const getList = async (flag?: number) => {
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      let temp = {
        ...searchForm,
        limit: pagination.currentPage,
        size: pagination.pageSize,
      };
      let res = await getTagPage(temp);
      tableData.data = res?.list ?? [];
      pagination.totalItems = res.total ?? 0;
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  };
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    console.log('Page changed to:', pagination);
    getList(1);
  };

  onMounted(() => {
    getList();
    getTypeOptions();
  });

  const formRef = ref();
  interface ModalInfo {
    id?: string;
    tagName?: string;
    tagCode?: number;
    tagColor?: string;
    sort?: number;
    showStatus?: number;
    tagType?: string;
  }
  const validatePass = (rule: any, value: string ) => {
    if (value === '') {
      return Promise.reject('请输入标签编码');
    } else {
      const code = value?.toString() ?? '';
      const isEditMode = !!modalInfo.value.id;
      const exists = tableData.data.some(
        (item: any) =>
          item.tagCode === code &&
          (!isEditMode || item.id !== modalInfo.value.id)
      );
      if (exists) {
        return Promise.reject('该标签编码已存在，请重新输入');
      } else {
        return Promise.resolve();
      }
    }
  };
  const modalInfo = ref<ModalInfo>({});
  const modalRules = {
    tagCode: [{ required: true, validator: validatePass, trigger: 'blur' }],
  };
  // 新增/编辑标签
  const openModalVisible = ref<boolean>(false);
  const onEdit = (obj?: ModalInfo) => {
    modalInfo.value = obj ? { ...obj } : { tagColor: '#000' };
    openModalVisible.value = true;
  };
  // 弹框确认
  const modalLoading = ref<boolean>(false);
  const handleModalOk = () => {
    console.log('handleModalOk', modalInfo.value);
    formRef.value
      .validate()
      .then(async () => {
        try {
          modalLoading.value = true;
          let temp = {
            ...modalInfo.value,
            tagCode:!!modalInfo.value.tagCode ? modalInfo.value.tagCode.toString() : null, // 转换为数字类型
          };
          let res = await tagAdd(temp);
          if (res) {
            openModalVisible.value = false;
            notification.success({
              message: '提示',
              description: '操作成功',
            });
            getList();
          }
          modalLoading.value = false;
        } catch (err) {
          console.log(err);
          modalLoading.value = false;
        }
      })
      .catch((error) => {
        console.log('error', error);
      });
  };
  // 删除标签
  const onDelete = async (row: any) => {
    try {
      let res = await tagDel([row.id]);
      row.delloading = false;
      if (res) {
        notification.success({
          message: '提示',
          description: '删除成功',
        });
        getList();
      }
    } catch (err) {
      console.log(err);
      row.delloading = false;
    }
  };

  // 显示隐藏
  const editStatus = async (row: any) => {
    try {
      let temp = {
        id: row.id,
        showStatus: row.showStatus,
      };
      let res = await tagAdd(temp);
      if (res) {
        notification.success({
          message: '提示',
          description: '操作成功',
        });
        getList(1);
      }
    } catch (err) {
      console.log(err);
    }
  };

  // 已关联门店数弹框
  const connectDetailRef = ref();
  const onConnectDetail = (row: any) => {
    console.log('onConnectDetail', row);
    if (connectDetailRef.value) {
      connectDetailRef.value.onShow(row.id);
    }
  };
  // 关联客户弹框
  const connectRef = ref();
  const onConnect = (row: any) => {
    console.log('onConnect', row);
    if (connectRef.value) {
      connectRef.value.onShow(row.id);
    }
  };
</script>

<style scoped lang="less">
  #employeeTag {
    width: 100%;
    height: 100%;
    padding: 8px;
    .targetMange_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      > div {
        width: 100%;
        padding: 16px;
        &:last-child {
          flex: 1;
          height: 0;
        }
      }
      .p_box {
        padding: 0 16px;
      }
    }
    .filterForm_box {
      * + * {
        margin-left: 16px;
      }
    }
    .table_box {
      .tag_div {
        padding: 2px 8px;
        border-radius: 4px;
        border: 1px solid;
      }
      .storeNum {
        font-weight: bold;
        cursor: pointer;
      }
    }
  }
  .modal_box {
    padding: 16px;
    .form_box {
      margin-top: 10px;
    }
    :deep(.ant-form-item-explain-error) {
      //display: none !important;
    }
  }
</style>
