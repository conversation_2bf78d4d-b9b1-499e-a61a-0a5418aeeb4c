{"name": "ky-otc", "version": "1.0.1", "author": {"name": "ky_otc", "email": "", "url": ""}, "scripts": {"bootstrap": "pnpm install", "serve": "npm run dev", "dev": "vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=5100 vite build && esno ./build/script/postBuild.ts", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode test && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "test:unit": "jest", "test:gzip": "npx http-server dist --cors --gzip -c-1", "test:br": "npx http-server dist --cors --brotli -c-1", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "prepare": "husky install", "gen:icon": "esno ./build/generate/icon/index.ts"}, "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-vue": "^6.1.0", "@antv/x6": "^2.18.1", "@dsb-norge/vue-keycloak-js": "^2.4.0", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@fullcalendar/vue3": "^6.1.10", "@iconify/iconify": "^3.1.0", "@logicflow/core": "^1.2.7", "@logicflow/extension": "^1.2.7", "@vue/runtime-core": "3.3.4", "@vueuse/core": "^10.1.2", "@zxcvbn-ts/core": "^3.0.1", "ant-design-vue": "3.2.20", "axios": "^1.4.0", "big.js": "^6.2.2", "bpmn-js": "^13.1.0", "bpmn-js-properties-panel": "^1.25.0", "camunda-bpmn-moddle": "^7.0.1", "codemirror": "^5.65.2", "cropperjs": "^1.5.12", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "diagram-js": "^12.1.1", "dom-to-image": "^2.6.0", "echarts": "^5.3.1", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "inherits": "^2.0.4", "intro.js": "^5.0.0", "js-base64": "^3.7.5", "js-pinyin": "^0.1.9", "jsbarcode": "^3.11.6", "jsbi-calculator": "^0.3.6", "keycloak-js": "^24.0.1", "lodash-es": "^4.17.21", "luckyexcel": "^1.0.1", "luckysheet": "^2.1.13", "min-dash": "^4.0.0", "min-dom": "^4.0.3", "mockjs": "^1.1.0", "moment": "^2.29.4", "node-opencc": "^2.0.1", "nprogress": "^0.2.0", "nzh": "^1.0.8", "path-to-regexp": "^6.2.0", "pinia": "2.0.12", "print-js": "^1.6.0", "qrcode": "^1.5.0", "qs": "^6.10.3", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "snowflake-id": "^1.1.0", "sortablejs": "^1.14.0", "tinymce": "^5.10.3", "vditor": "^3.9.2", "vue": "3.3.4", "vue-clipboard3": "^2.0.0", "vue-esign": "^1.1.4", "vue-grid-layout": "3.0.0-beta1", "vue-i18n": "^9.2.2", "vue-json-pretty": "^2.2.4", "vue-router": "^4.2.1", "vue-types": "^5.0.3", "vuedraggable": "^4.1.0", "wangeditor": "4.6.3", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@iconify/json": "^2.2.67", "@purge-icons/generated": "^0.9.0", "@types/codemirror": "^5.60.7", "@types/crypto-js": "^4.1.1", "@types/fs-extra": "^11.0.1", "@types/inquirer": "^9.0.3", "@types/intro.js": "^5.1.1", "@types/lodash-es": "^4.17.7", "@types/mockjs": "^1.0.7", "@types/node": "^18.16.0", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.0", "@types/qs": "^6.9.7", "@types/showdown": "^2.0.1", "@types/sortablejs": "^1.15.1", "@typescript-eslint/eslint-plugin": "^5.59.6", "@typescript-eslint/parser": "^5.59.6", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/compiler-sfc": "3.3.4", "@vue/test-utils": "^2.3.2", "autoprefixer": "^10.4.14", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "cz-git": "^1.6.1", "czg": "^1.6.1", "dotenv": "^16.0.3", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.13.0", "esno": "^0.16.3", "fs-extra": "^11.1.1", "husky": "^8.0.3", "inquirer": "^9.2.6", "less": "^4.1.3", "lint-staged": "^13.2.2", "picocolors": "^1.0.0", "postcss": "^8.4.23", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.8", "rimraf": "^5.0.1", "rollup": "^3.22.1", "stylelint": "^15.6.2", "stylelint-config-recommended": "^11.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^33.0.0", "stylelint-order": "^6.0.3", "terser": "^5.17.4", "ts-node": "^10.9.1", "typescript": "^5.0.4", "unplugin-vue-setup-extend-plus": "^1.0.0", "vite": "^4.3.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-mkcert": "^1.15.0", "vite-plugin-mock": "^2.9.6", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-pwa": "^0.15.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-windicss": "^1.9.0", "vite-vue-plugin-html": "^1.0.1", "vite-vue-plugin-theme": "^1.0.0", "vue-eslint-parser": "^9.3.0", "vue-tsc": "^1.6.5"}, "repository": {"type": "git", "url": "git+https://github.com/anncwb/vue-vben-admin.git"}, "license": "MIT", "bugs": {"url": "https://github.com/anncwb/vue-vben-admin/issues"}, "homepage": "https://github.com/anncwb/vue-vben-admin", "engines": {"node": ">= 16.0.0", "pnpm": ">=8.1.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix --allow-empty-input", "prettier --write"], "*.md": ["prettier --write"]}, "packageManager": "pnpm@9.12.2"}